<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <q-input
      v-model="module.data.headline1"
      v-bind="$inri.input"
      hide-bottom-space
      :label="$t('syndicate_plus.aplus_define_template.headline')"
      :maxlength="Validation.techSpecs.headline1.maxLength"
      counter
      @keyup.enter="updateModel"
      @blur="updateModel"
      @drop="onDrop(module.data.headline1, 'headline1')"
    />
    <div class="flex flex-row flex-nowrap">
      <c-specs-list
        v-model="module.data.specs"
        :max-spec-name-length="Validation.techSpecs.specName.maxLength"
        :max-spec-definition-length="Validation.techSpecs.definition.maxLength"
        @update-model="updateModel"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, TechSpecData } from '@customTypes/Aplus';
import { CSpecsList } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Refs
const module = ref<ContentModule<TechSpecData>>({} as ContentModule<TechSpecData>);

// Functions
const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, moduleParameter) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data[moduleParameter] = result;
    updateModel();
  }
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      headline1: '',
      specs: [],
    } as TechSpecData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
<style lang="scss" scoped></style>
