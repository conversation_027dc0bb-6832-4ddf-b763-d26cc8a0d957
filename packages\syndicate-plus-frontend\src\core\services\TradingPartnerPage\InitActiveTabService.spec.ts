import { describe, it, expect, vitest, beforeEach } from 'vitest';
import { getInitialActiveTab, setLastActiveTab } from './InitActiveTabService';
import { TradingPartnerTabNames } from '@core/enums';
import sessionStorageManager from '@utils/sessionStorageManager';

vitest.mock('@utils/sessionStorageManager');

const sessionManagerMock = sessionStorageManager as any;

describe('TradingPartnerPage InitActiveTabService', () => {
  beforeEach(() => {
    sessionManagerMock.getItemValue.mockReset();
    sessionManagerMock.setItem.mockReset();
  });

  describe('getInitialActiveTab', () => {
    it('should return COLLECTIONS when no active tab has been saved', () => {
      // Arrange
      sessionManagerMock.getItemValue.mockReturnValue(null);

      // Act
      const activeTab = getInitialActiveTab('trading-partner-1');

      // Assert
      expect(activeTab).toBe(TradingPartnerTabNames.COLLECTIONS);
      expect(sessionManagerMock.getItemValue).toHaveBeenCalledWith('tradingPartnerPageLastActiveTab');
      expect(sessionManagerMock.getItemValue).toHaveBeenCalledWith('tradingPartnerPageLastPartnerId');
    });

    it('should return saved tab state when returning to the same trading partner', () => {
      // Arrange
      sessionManagerMock.getItemValue.mockImplementation((key) => {
        if (key === 'tradingPartnerPageLastPartnerId') {
          return 'trading-partner-1';
        }
        if (key === 'tradingPartnerPageLastActiveTab') {
          return TradingPartnerTabNames.HISTORY;
        }
        return null;
      });

      // Act
      const activeTab = getInitialActiveTab('trading-partner-1');

      // Assert
      expect(activeTab).toBe(TradingPartnerTabNames.HISTORY);
    });

    it('should return COLLECTIONS tab when switching to a different trading partner', () => {
      // Arrange
      sessionManagerMock.getItemValue.mockImplementation((key) => {
        if (key === 'tradingPartnerPageLastPartnerId') {
          return 'trading-partner-1';
        }
        if (key === 'tradingPartnerPageLastActiveTab') {
          return TradingPartnerTabNames.HISTORY;
        }
        return null;
      });

      // Act
      const activeTab = getInitialActiveTab('trading-partner-2');

      // Assert
      expect(activeTab).toBe(TradingPartnerTabNames.COLLECTIONS);
      expect(sessionManagerMock.setItem).toHaveBeenCalledWith('tradingPartnerPageLastPartnerId', 'trading-partner-2');
    });
  });

  describe('setLastActiveTab', () => {
    it('should save tab state and trading partner ID in session storage', () => {
      // Act
      setLastActiveTab('trading-partner-1', TradingPartnerTabNames.PRODUCTS);

      // Assert
      expect(sessionManagerMock.setItem).toHaveBeenCalledWith(
        'tradingPartnerPageLastActiveTab',
        TradingPartnerTabNames.PRODUCTS
      );
      expect(sessionManagerMock.setItem).toHaveBeenCalledWith('tradingPartnerPageLastPartnerId', 'trading-partner-1');
    });

    it('should update storage when changing tabs for the same trading partner', () => {
      // Act
      setLastActiveTab('trading-partner-1', TradingPartnerTabNames.HISTORY);
      setLastActiveTab('trading-partner-1', TradingPartnerTabNames.PRODUCTS);

      // Assert - the latest call should update the tab without changing the partner ID
      expect(sessionManagerMock.setItem).toHaveBeenCalledWith(
        'tradingPartnerPageLastActiveTab',
        TradingPartnerTabNames.PRODUCTS
      );
    });
  });
});
