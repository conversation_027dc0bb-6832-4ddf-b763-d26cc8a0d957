import { JobState } from '@core/enums';
import { DataSubmission, DataSubmissionEntitiesResponse, DataSubmissionResponse } from '@core/interfaces';
import { outputAdapterClient } from '@core/Utils';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';

export const fetchDataSubmissions = async (
  page = 1,
  pageSize = 100,
  jobId?: number
): Promise<DataSubmissionResponse> => {
  const environmentGid = getEnvironmentGlobalId();
  let url = `/api/environments/${environmentGid}/data-submissions?page=${page}&pageSize=${pageSize}`;
  if (jobId) {
    url += `&jobId=${jobId}`;
  }
  const response = await outputAdapterClient.get(url, 'Error fetching data submissions');
  return response
    ? (response.data as DataSubmissionResponse)
    : { dataSubmissions: [] as DataSubmission[], totalCount: 0, pageSize, page };
};

export const fetchDataSubmissionEntities = async (
  dataSubmissionId: number,
  page = 1,
  pageSize = 100,
  state: JobState
): Promise<DataSubmissionEntitiesResponse> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/data-submissions/${dataSubmissionId}/entities?page=${page}&pageSize=${pageSize}&state=${state}`;
  const response = await outputAdapterClient.get(url, 'Error fetching data submission entries');
  return response ? (response.data as DataSubmissionEntitiesResponse) : { entities: [], totalCount: 0, pageSize, page };
};
