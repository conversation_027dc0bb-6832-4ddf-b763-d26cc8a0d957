import { ref } from 'vue';
import { ContentModule, ThreeImageTextData } from '@customTypes/Aplus';

export default function useThreeImagesAndText() {
  // Refs
  const module = ref<ContentModule<ThreeImageTextData>>({} as ContentModule<ThreeImageTextData>);

  // Functions
  const initData = () => {
    if (!Object.keys(module.value.data)?.length) {
      module.value.data = {
        headline: '',
        image1: {
          altText: '',
          angle: '',
        },
        headline1: '',
        body1: '',
        image2: {
          altText: '',
          angle: '',
        },
        headline2: '',
        body2: '',
        image3: {
          altText: '',
          angle: '',
        },
        headline3: '',
        body3: '',
      } as ThreeImageTextData;
    }
  };

  return { module, initData };
}
