<template>
  <section class="c-inri-section">
    <c-no-data
      v-if="isLoading"
      src="building"
      image-height="195px"
      :title="$t('syndicate_plus.loading.crafting')"
      :text="$t('syndicate_plus.loading.aplus_preflight_preparing')"
    >
      <q-spinner-dots class="no-data-title-slot" color="primary" size="20" />
    </c-no-data>
    <q-table
      v-else
      flat
      dense
      hide-bottom
      separator="cell"
      class="aplus-preflight-table sticky-table-header"
      row-key="processedId"
      :pagination="{
        page: 1,
        rowsPerPage: 0,
      }"
      :no-data-label="$t('syndicate_plus.common.table.no_data')"
      :rows="displayedRows"
      :columns="columns"
      virtual-scroll
      :loading="virtualScrollLoading"
      :virtual-scroll-item-size="31"
      :virtual-scroll-sticky-size-start="31"
      @virtual-scroll="onScroll"
    >
      <template #header="scope">
        <q-tr>
          <q-th v-for="col in scope.cols" :key="col.name" :class="getHeaderCellStyle(col)">
            <div class="header-cell">
              <span>{{ col.name.toLocaleLowerCase() }}</span>
            </div>
          </q-th>
        </q-tr>
      </template>
      <template #body="scope">
        <q-tr>
          <q-td v-for="col in scope.cols" :key="col.name" :class="getCellStyle(scope.row, col)">
            <c-error-tooltip
              v-if="cellHasValidationErrors(scope.row, col)"
              :errors="cellErrorsMessage(scope.row, col, ValidationResult.ERROR)"
              :warnings="cellErrorsMessage(scope.row, col, ValidationResult.WARNING)"
            />
            {{ renderValue(col, scope.row) }}
            <q-tooltip v-if="col.isExtraColumn">{{ scope.row.row[col.field] }}</q-tooltip>
          </q-td>
        </q-tr>
      </template>
      <template #loading>
        <q-inner-loading showing color="primary" class="inner-loading">
          <c-spinner color="primary" size="40" />
        </q-inner-loading>
      </template>
    </q-table>
  </section>
</template>
<script setup lang="ts">
import { PropType, watchEffect } from 'vue';
import { useAPlusPreflightTable } from '@composables/APlusPreflight';
import { useVirtualScroll } from '@composables';
import { CNoData, CErrorTooltip } from '@components';
import { APlusTemplate } from '@customTypes/Aplus';
import { ValidationResult, ValidationError } from '@customTypes';

const props = defineProps({
  destinationId: {
    type: Number,
    required: true,
  },
  subCatalogId: {
    type: Number,
    required: true,
  },
  selectedTemplate: {
    type: Object as PropType<APlusTemplate>,
    required: false,
    default: null,
  },
});
const emit = defineEmits(['apply-filter']);

// Variables
const pageSize = 40;

// Composables
const { isLoading, performPreflight, rows, columns } = useAPlusPreflightTable(
  emit,
  props.subCatalogId,
  props.destinationId
);
const { displayedRows, onScroll, virtualScrollLoading } = useVirtualScroll(rows, pageSize);

watchEffect(async () => {
  if (props.selectedTemplate) {
    await performPreflight(props.selectedTemplate);
  }
});

// Functions
const getCellStyle = (row, col): string => {
  if (col.isExtraColumn) {
    return 'sticky extra-column-cell';
  }

  const errors = cellErrorsMessage(row, col, ValidationResult.ERROR);
  const warnings = cellErrorsMessage(row, col, ValidationResult.WARNING);

  if (errors?.length > 0) {
    return 'required-error';
  }

  if (warnings?.length > 0) {
    return 'warning';
  }

  return '';
};

const cellErrorsMessage = (row, col, severity: ValidationResult): string[] => {
  const errors = row.errors[col.field] as ValidationError[];

  return errors?.filter((e) => e.severity == severity).map((e) => e.message);
};

const cellHasValidationErrors = (row, col): boolean => {
  const errors = row.errors[col.field] as ValidationError[];

  return !!errors && errors.length !== 0;
};

const getHeaderCellStyle = (col): string => {
  return col.isExtraColumn ? 'sticky extra-column-header' : '';
};

const renderValue = (col, row): string => {
  return !row.row[col.field] || row.row[col.field] === '' ? '-' : row.row[col.field];
};
</script>

<style scoped lang="scss">
.aplus-preflight-table {
  max-height: 80vh;
  margin-bottom: 200px;
}

.no-data-title-slot {
  padding-top: 9px;
  display: inline;
}

th {
  background-color: var(--color-grey-lighter);
  padding-right: 0px;
}

td:nth-child(1),
td:nth-child(2),
td:nth-child(3),
th:nth-child(1),
th:nth-child(2),
th:nth-child(3) {
  overflow: hidden;
  white-space: nowrap;
}

td:nth-child(1),
th:nth-child(1) {
  left: 0;
  min-width: 150px;
  max-width: 150px;
}

td:nth-child(2),
th:nth-child(2) {
  left: 150px;
  min-width: 150px;
  max-width: 150px;
}

td:nth-child(3),
th:nth-child(3) {
  left: 300px;
  min-width: 150px;
  max-width: 150px;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;

  &.extra-column-header {
    background-color: var(--color-green-10) !important;
    z-index: 2;
  }

  &.extra-column-cell {
    background-color: #fafafa;
    color: var(--color-grey-dark);
    z-index: 1;
  }
}

.header-cell {
  display: flex;
  flex-flow: row;
  align-items: center;

  span {
    padding-right: 20px;
  }
}

.required-error {
  border-color: var(--color-red);
  background-color: var(--color-red-light);
}

.warning {
  border-color: var(--color-yellow);
  background-color: var(--color-yellow-light);
}

.inner-loading {
  z-index: 3;
}
</style>
