import { mount, shallowMount } from '@vue/test-utils';
import { expect, it, describe, vi, Mock, beforeEach } from 'vitest';
import { useRoute } from 'vue-router';
import CMediaMappingTabPanelVue from '@components/MediaMapping/CMediaMappingTabPanel.vue';
import CMediaSpecification from '@components/MediaMapping/CMediaSpecification.vue';
import CMediaNamingOverride from '@components/MediaMapping/CMediaNamingOverride.vue';
import CMediaSlot from '@components/MediaMapping/CMediaSlot.vue';
import { MediaMappingService } from '@services/MediaMappingService';
import { Template } from '@customTypes/template';
import CSection from '@inriver/inri/src/components/CSection.vue';
import CSelect from '@inriver/inri/src/components/CSelect.vue';
import CGrid from '@inriver/inri/src/components/CGrid.vue';
import { createPinia, setActivePinia } from 'pinia';

vi.mock('@services/MediaMappingService');
vi.mock('vue-router');

describe('CMediaMappingTabPanel', () => {
  let mockRoute;
  const useRouteMock = useRoute as Mock;

  const mockTemplates: Template[] = [
    {
      name: 'Template 1',
      disabled: false,
      id: '1a2b3c',
    },
    {
      name: 'Template 2',
      disabled: true,
      id: '4d5e6f',
    },
  ];

  const mockDestinationId: String = '12';

  const CMediaMappingTabPanelProps = {
    tradingPartnerName: 'testName',
    templateSelectVal: mockTemplates[0],
  };

  const components = {
    'c-section': CSection,
    'c-select': CSelect,
    'c-grid': CGrid,
    'c-media-slot': CMediaSlot,
    'c-media-specification': CMediaSpecification,
    'c-media-naming-override': CMediaNamingOverride,
  };

  const mocks = {
    $t: (msg: string) => msg,
  };

  beforeEach(() => {
    const mockMediaMappingService = {
      getTemplates: vi.fn().mockResolvedValue(mockTemplates),
      getMediaMapping: vi.fn().mockResolvedValue(mockDestinationId),
    };
    mockRoute = {
      params: [{ destinationId: '1' }],
    };
    useRouteMock.mockReturnValue(mockRoute);

    (MediaMappingService as Mock).mockReturnValue(mockMediaMappingService);
    setActivePinia(createPinia());
  });

  it('check component properties', async () => {
    // Arrange
    const wrapper = shallowMount(CMediaMappingTabPanelVue, {
      props: CMediaMappingTabPanelProps,
      global: {
        components,
        mocks,
      },
    });

    // Act + Assert
    expect(wrapper.props().tradingPartnerName).toMatch('testName');
  });

  // TODO: Fix this test later because of v-model in select enabling rendering with v-if clause
  it.skip('contains media specification', async () => {
    // Arrange
    const wrapper = mount(CMediaMappingTabPanelVue, {
      children: [CMediaSpecification],
      props: CMediaMappingTabPanelProps,
      global: {
        components,
        mocks,
      },
    });

    // Act + Assert
    expect(wrapper.find('.c-media-specification').exists()).toBeTruthy();
  });

  // // TODO: Fix this test later because of v-model in select enabling rendering with v-if clause
  it.skip('contains media naming override', async () => {
    // Arrange
    const wrapper = mount(CMediaMappingTabPanelVue, {
      props: CMediaMappingTabPanelProps,
      children: [CMediaNamingOverride],
      global: {
        stubs: {
          'c-section': true,
          'c-select': true,
          'c-media-specification': true,
          'c-media-slots': true,
        },
      },
    });

    // Act + Assert
    expect(wrapper.find('.c-media-naming-override').exists()).toBeTruthy();
  });

  // // TODO: Fix this test later because of v-model in select enabling rendering with v-if clause
  it.skip('contains media slots', async () => {
    // Arrange
    const wrapper = mount(CMediaMappingTabPanelVue, {
      props: CMediaMappingTabPanelProps,
      children: [CMediaSlot],
      global: {
        stubs: {
          'c-section': true,
          'c-select': true,
          'c-media-specification': true,
          'c-media-naming-override': true,
        },
      },
    });

    // Act + Assert
    expect(wrapper.find('.c-media-slot').exists()).toBeTruthy();
  });
});
