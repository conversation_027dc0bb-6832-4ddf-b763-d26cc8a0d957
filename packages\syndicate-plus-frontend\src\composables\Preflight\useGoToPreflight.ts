import { Ref } from 'vue';
import { useRouter } from '@composables/useRouter';
import { ProductFilterDto } from '@dtos/ProductFilterDto';
import { CollectionDetails } from '@customTypes/CollectionDetails';
import { SyndicationPageTabNames } from '@enums';

export default function useGoToPreflight(
  filter: Ref<ProductFilterDto>,
  tab: Ref<SyndicationPageTabNames | null>,
  selectedCollection: Ref<CollectionDetails | null>,
  selectAllProductsFilterApplied: Ref<boolean>,
  searchValue: Ref<string | null>
) {
  // Composables
  const { goToPage } = useRouter();

  // Functions
  const goToPreflightPage = (selectedProductIds: string[]) => {
    const query =
      tab.value === SyndicationPageTabNames.Collection && selectedCollection.value
        ? createQueryForCollections()
        : createQueryForProducts(selectedProductIds);

    goToPage('preflight-page', null, query);
  };

  const createQueryForCollections = () => {
    if (!selectedCollection.value) {
      throw new Error('Collection can not be undefined');
    }

    return {
      selectAllProductsFilterApplied: true,
      groupBy: filter.value.groupBy.id,
      collections: [selectedCollection.value.id],
    };
  };

  const createQueryForProducts = (selectedProductIds: string[]) => {
    const { groupBy, brands, collections, productTypes } = filter.value;

    return selectAllProductsFilterApplied.value
      ? {
          selectAllProductsFilterApplied: true,
          groupBy: groupBy.id,
          brands,
          collections,
          productTypes,
          searchValue: searchValue.value,
        }
      : {
          ids: selectedProductIds.join(','),
          groupBy: groupBy.id,
        };
  };

  return { goToPreflightPage };
}
