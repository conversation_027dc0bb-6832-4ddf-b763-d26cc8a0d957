import { vi, it, describe, expect, Mock } from 'vitest';
import GridService from '@services/GridService';
import { useAplusColumnsBuilder } from '@composables/APlusPreflight';
import { GridHeaderDto } from '@dtos/GridHeaderDto';
import { AplusPreflightColumn } from '@customTypes/Aplus';

vi.mock('@services/GridService');

const destinationId = 1;
const templateName = 'template';

describe('useAPlusColumnsBuilder', () => {
  describe('buildColumns', async () => {
    it('sets an empty array if the grid service returns an empty array', async () => {
      // Arrange
      (GridService.getHeaders as Mock).mockResolvedValue([]);
      const { columns, buildColumns } = useAplusColumnsBuilder(destinationId);

      // Act
      await buildColumns(templateName);

      // Assert
      expect(columns.value).to.deep.equal([]);
    });

    it('sets the columns correctly', async () => {
      // Arrange
      const gridHeaders = [
        {
          headerName: 'name1',
          field: 'field1',
        },
        {
          headerName: 'name2',
          field: 'field2',
        },
      ] as GridHeaderDto[];
      (GridService.getHeaders as Mock).mockResolvedValue(gridHeaders);
      const { columns, buildColumns } = useAplusColumnsBuilder(destinationId);

      // Act
      await buildColumns(templateName);

      // Assert
      expect(columns.value).to.deep.equal([
        {
          name: 'name1',
          field: 'field1',
          label: 'field1',
          isExtraColumn: false,
        },
        {
          name: 'name2',
          field: 'field2',
          label: 'field2',
          isExtraColumn: false,
        },
      ] as AplusPreflightColumn[]);
    });

    it('skips the column if headerName is empty', async () => {
      // Arrange
      const gridHeaders = [
        {
          headerName: '',
          field: 'field1',
        },
        {
          headerName: 'name2',
          label: 'field2',
          field: 'field2',
        },
      ] as GridHeaderDto[];
      (GridService.getHeaders as Mock).mockResolvedValue(gridHeaders);
      const { columns, buildColumns } = useAplusColumnsBuilder(destinationId);

      // Act
      await buildColumns(templateName);

      // Assert
      expect(columns.value).to.deep.equal([
        {
          name: 'name2',
          label: 'field2',
          field: 'field2',
          isExtraColumn: false,
        },
      ] as AplusPreflightColumn[]);
    });
  });
});
