<template>
  <div class="mx-auto min-w-1000px w-1000px text-preview">
    <div class="flex flex-nowrap justify-center m-10px">
      <div class="text-center">
        <div class="main-headline">{{ module.data.headline1 }}</div>
        <c-html-preview :html="module.data.body1" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, TextData } from '@customTypes/Aplus';
import { CHtmlPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<TextData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.text-preview {
  .main-headline {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>
