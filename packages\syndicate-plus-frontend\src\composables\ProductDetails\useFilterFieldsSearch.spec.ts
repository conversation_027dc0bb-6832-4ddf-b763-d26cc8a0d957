import { computed, ref } from 'vue';
import { expect, it, describe } from 'vitest';
import { useFilterFieldsSearch } from '@composables/ProductDetails';
import { FilterField } from '@customTypes/Products';

describe('useFilterFieldsSearch', () => {
  describe('searchValue and filteredColumns', () => {
    it('filters the allColumns list correctly', async () => {
      // Arrange
      const allColumns = computed(() => [{ name: 'a' }, { name: 'b' }, { name: 'c' }] as FilterField[]);
      const showInternalNames = ref(false);
      const { searchValue, filteredColumns } = useFilterFieldsSearch(allColumns, showInternalNames);

      // Act
      searchValue.value = 'a';

      // Assert
      expect(filteredColumns.value).toStrictEqual([{ name: 'a' }]);
    });

    it('filters the allColumns list by part of a word correctly', async () => {
      // Arrange
      const allColumns = computed(
        () => [{ name: 'testWord' }, { name: 'wordTest' }, { name: 'TestwordTest' }, { name: 'new' }] as FilterField[]
      );
      const showInternalNames = ref(false);
      const { searchValue, filteredColumns } = useFilterFieldsSearch(allColumns, showInternalNames);

      // Act
      searchValue.value = 'word';

      // Assert
      expect(filteredColumns.value).to.deep.equal([
        { name: 'testWord' },
        { name: 'wordTest' },
        { name: 'TestwordTest' },
      ]);
    });

    it('filters the allColumns internal names list by part of a word correctly', async () => {
      // Arrange
      const allColumns = computed(
        () =>
          [
            { name: 'testWord', internalName: 'TESTWORD' },
            { name: 'wordTest', internalName: 'WORD1' },
            { name: 'TestwordTest', internalName: 'BULLETTEST' },
            { name: 'new', internalName: 'WORD' },
          ] as FilterField[]
      );
      const showInternalNames = ref(true);
      const { searchValue, filteredColumns } = useFilterFieldsSearch(allColumns, showInternalNames);

      // Act
      searchValue.value = 'word';

      // Assert
      expect(filteredColumns.value).to.deep.equal([
        { name: 'testWord', internalName: 'TESTWORD' },
        { name: 'wordTest', internalName: 'WORD1' },
        { name: 'new', internalName: 'WORD' },
      ]);
    });

    it('filters the allColumns list both by name and display name', async () => {
      // Arrange
      const columns = [
        { name: 'colNameA', displayName: 'Col Name A' },
        { name: 'colNameB', displayName: 'Col Name B' },
        { name: 'colNameC', displayName: 'Col Name C' },
      ] as FilterField[];
      const testCases = [
        { search: 'colNameA', expectedFilteredColumns: [columns[0]] },
        { search: 'Col Name', expectedFilteredColumns: columns },
        { search: 'Col Name C', expectedFilteredColumns: [columns[2]] },
        { search: 'col name a', expectedFilteredColumns: [columns[0]] },
        { search: 'colnamea', expectedFilteredColumns: [columns[0]] },
      ];
      const showInternalNames = ref(false);

      const allColumns = computed(() => columns);

      testCases.forEach(({ search, expectedFilteredColumns }) => {
        const { searchValue, filteredColumns } = useFilterFieldsSearch(allColumns, showInternalNames);

        // Act
        searchValue.value = search;

        // Assert
        expect(filteredColumns.value).toStrictEqual(expectedFilteredColumns);
      });
    });
  });
});
