<template>
  <c-select
    v-model="selectedTemplate"
    :options="fieldsArray"
    :placeholder="$t('syndicate_plus.common.template')"
    :label="$t('syndicate_plus.common.template')"
    :dense="dense"
    option-label="name"
    option-value="id"
    clearable
  />
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, watch } from 'vue';
import { useSuggestionStore } from '../stores/SuggestionStore';
import { useRoute } from 'vue-router';
import { SuggestionTypeEnum } from '../enums/SuggestionTypeEnum';
import { Field } from '../types/field';

defineProps({
  dense: {
    type: Boolean,
    default: true,
  },
});

const { fetchSuggestions } = useSuggestionStore();
const route = useRoute();

const { destinationId } = route.params;

const selectedTemplate = ref<Field>();
const fieldsArray = ref<Field[]>();

const emit = defineEmits(['templateSelectedEvent', 'singleTemplateSelectedEvent', 'noTemplatesEvent']);

onBeforeMount(async () => {
  fieldsArray.value = await fetchSuggestions(
    parseInt(destinationId as string),
    SuggestionTypeEnum.ExportMultiPreflightTemplates
  );
  if (!fieldsArray.value?.length) {
    emit('noTemplatesEvent');
  }
  if (fieldsArray.value.length == 1) {
    selectedTemplate.value = fieldsArray.value[0];
  }
});

watch(selectedTemplate, (newVal) => {
  if (fieldsArray.value?.length == 1) {
    emit('singleTemplateSelectedEvent', newVal);
  }
  emit('templateSelectedEvent', newVal);
});
</script>

<style lang="scss"></style>
