<template>
  <q-list>
    <q-item v-for="(_, index) in model" :key="index" v-ripple clickable>
      <q-item-section side>
        <q-btn label="X" @click="removeSpec(index)" />
      </q-item-section>
      <q-item-section avatar>
        <q-input
          v-model="model[index].specName"
          v-bind="$inri.input"
          :label="$t('syndicate_plus.aplus_define_template.enter_specification')"
          :maxlength="maxSpecNameLength"
          counter
          hide-bottom-space
          @keyup.enter="updateSpec"
          @blur="updateSpec"
          @drop="onDropSpec(model[index].specName, index, 'specName')"
        />
      </q-item-section>
      <q-item-section avatar>
        <q-input
          v-model="model[index].definition"
          v-bind="$inri.input"
          class="definition"
          :label="$t('syndicate_plus.aplus_define_template.enter_definition')"
          :maxlength="maxSpecDefinitionLength"
          counter
          hide-bottom-space
          @keyup.enter="updateSpec"
          @blur="updateSpec"
          @drop="onDropSpec(model[index].definition, index, 'definition')"
        />
      </q-item-section>
    </q-item>
    <q-item v-ripple clickable>
      <q-item-section side>
        <q-btn disabled label="X" />
      </q-item-section>
      <q-item-section avatar>
        <q-input
          v-model="newSpec.specName"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.enter_specification')"
          :maxlength="maxSpecNameLength"
          counter
          @keyup.enter="addSpec"
          @blur="addSpec"
          @drop="onDropNewSpec(newSpec.specName, 'specName')"
        />
      </q-item-section>
      <q-item-section avatar>
        <q-input
          v-model="newSpec.definition"
          v-bind="$inri.input"
          class="definition"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.enter_definition')"
          :maxlength="maxSpecDefinitionLength"
          counter
          @keyup.enter="addSpec"
          @blur="addSpec"
          @drop="onDropNewSpec(newSpec.definition, 'definition')"
        />
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Spec } from '@customTypes/Aplus';
import { useFieldsTabStore } from '@stores/TemplateBuilder';

const props = defineProps({
  modelValue: {
    type: Array<Spec>,
    default: [],
  },
  maxSpecNameLength: {
    type: Number,
    default: 100,
  },
  maxSpecDefinitionLength: {
    type: Number,
    default: 100,
  },
});

const fieldsStore = useFieldsTabStore();

// Emits
const emit = defineEmits(['update-model']);

// Refs
const model = ref(props.modelValue);
const newSpec = ref({ specName: '', definition: '' } as Spec);

// Functions
const addSpec = (): void => {
  if (!newSpec.value?.definition.trim() || !newSpec.value?.specName.trim()) {
    return;
  }

  model.value.push(newSpec.value);
  newSpec.value = { specName: '', definition: '' } as Spec;
  emit('update-model');
};

const updateSpec = (): void => {
  emit('update-model');
};

const onDropNewSpec = (value: string, parameter): void => {
  newSpec.value[parameter] = fieldsStore.onDrop(value);
  addSpec();
};

const onDropSpec = (value: string, index: number, parameter): void => {
  model.value[index][parameter] = fieldsStore.onDrop(value);
  updateSpec();
};

const removeSpec = (index: number): void => {
  model.value.splice(index, 1);
  emit('update-model');
};
</script>

<style scoped lang="scss">
:deep(.q-field__control) {
  min-width: auto !important;
}

.definition {
  :deep(.q-field__control) {
    min-width: 655px !important;
  }
}

:deep(.q-item.q-item-type) {
  align-items: baseline;
  height: 70px;
}
</style>
