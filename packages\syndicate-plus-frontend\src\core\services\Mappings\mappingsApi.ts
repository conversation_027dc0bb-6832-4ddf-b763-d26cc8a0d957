import { Mapping, MappingDetailsResponse, NewMapping } from '@core/interfaces';
import { portalFetch, proxyPortalFetch } from '@utils';

export const getMappingsByFormatFileId = async (formatFileId: number): Promise<Mapping[]> => {
  const url = `/api/syndication/mapping/existing?formatId=${formatFileId}`;
  try {
    if (import.meta.env.DEV) {
      const response = await proxyPortalFetch(url);
      return response?.ok ? appendFormatFileId(formatFileId, await response.json()) : [];
    }

    const response = await portalFetch(url);
    return appendFormatFileId(formatFileId, await response.json());
  } catch (error) {
    console.error('Error fetching mappings', error);
    return [];
  }
};

function appendFormatFileId(formatFileId: number, mappings: Mapping[]): Mapping[] {
  return mappings.map((mapping) => {
    return {
      ...mapping,
      FormatFileId: formatFileId,
    };
  });
}

export const getMapping = async (id: number): Promise<MappingDetailsResponse | undefined> => {
  const url = `/api/syndication/mapping/existing/${id}`;
  try {
    if (import.meta.env.DEV) {
      const response = await proxyPortalFetch(url);
      return response?.ok ? await response.json() : undefined;
    }

    const response = await portalFetch(url);
    return await response.json();
  } catch (error) {
    console.error(`Error fetching mapping with id ${id}`, error);
    return undefined;
  }
};

export const updateMapping = async (mapping: MappingDetailsResponse): Promise<boolean> => {
  const url = `/api/syndication/mapping/updateMapping`;
  try {
    if (import.meta.env.DEV) {
      const response = await proxyPortalFetch(url, { method: 'POST', body: JSON.stringify(mapping) });

      return response?.ok;
    }

    const response = await portalFetch(url, { method: 'POST', body: JSON.stringify(mapping) });

    return response?.ok;
  } catch (error) {
    console.error(`Error updating mapping with id ${mapping.MappingId}`, error);
    return false;
  }
};

export const saveMapping = async (mapping: NewMapping): Promise<number | undefined> => {
  const url = `/api/syndication/mapping/mapping`;
  try {
    if (import.meta.env.DEV) {
      const response = await proxyPortalFetch(url, { method: 'POST', body: JSON.stringify(mapping) });

      return response?.ok ? await response.json() : undefined;
    }

    const response = await portalFetch(url, { method: 'POST', body: JSON.stringify(mapping) });

    return response?.ok ? await response.json() : undefined;
  } catch (error) {
    console.error(`Error saving mapping with id ${mapping.MappingId}`, error);
    return undefined;
  }
};

export const checkIfMappingIsUsed = async (mappingId: number): Promise<string[]> => {
  const url = `/api/syndication/mapping/syndicationsUsingMapping/${mappingId}`;
  try {
    if (import.meta.env.DEV) {
      const response = await proxyPortalFetch(url);

      return await response?.json();
    }

    const response = await portalFetch(url);
    return await response?.json();
  } catch (error) {
    console.error(`Error when check if mapping ${mappingId} is used`, error);
    return [];
  }
};

export const deleteMapping = async (mappingId: number): Promise<boolean> => {
  const url = `/api/syndication/mapping/existing/${mappingId}`;
  if (import.meta.env.DEV) {
    const response = await proxyPortalFetch(url, { method: 'DELETE' });

    return response.ok;
  }

  return (await portalFetch(url, { method: 'DELETE' })).ok;
};

export const getSkuEntityTypes = async (): Promise<string[]> => {
  const url = '/api/syndication/mapping/skuentities';
  try {
    if (import.meta.env.DEV) {
      const response = await proxyPortalFetch(url);

      return await response?.json();
    }

    const response = await portalFetch(url);
    return await response?.json();
  } catch (error) {
    console.error('Error fetching sku entity types ', error);
    return [];
  }
};
