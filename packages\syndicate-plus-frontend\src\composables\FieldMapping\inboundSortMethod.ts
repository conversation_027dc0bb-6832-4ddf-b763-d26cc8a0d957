import { TemplateDefinition } from '@customTypes';

export function sortInboundTemplates(inboundTemplates: TemplateDefinition[]): TemplateDefinition[] {
  return inboundTemplates.sort(compareInboundNames);
}

export function compareInboundNames(a, b) {
  if (checkHasNameDifference(a, b, 'master') !== 0) {
    return checkHasNameDifference(a, b, 'master');
  } else if (checkHasNameDifference(a, b, 'inbound') !== 0) {
    return checkHasNameDifference(a, b, 'inbound');
  }
  // If both have "master" or neither do, sort alphabetically
  return a.templateName?.localeCompare(b.templateName) ?? 1;
}

function checkHasNameDifference(a, b, name) {
  const aHasName = a.templateName?.toLowerCase().includes(name);
  const bHasName = b.templateName?.toLowerCase().includes(name);

  // If a has "master" and b doesn't, a comes first (-1)
  if (aHasName && !bHasName) {
    return -1;
  }
  // If b has "master" and a doesn't, b comes first (1)
  if (!aHasName && bHasName) {
    return 1;
  }

  return 0;
}
