import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { CancellationError } from '@customTypes';

// Refs
const showProgress = ref(false);
const progress = ref(0);
const progressLabel = ref('');
const shouldCancel = ref(false);

export default function useProgress() {
  // Composables
  const { t } = useI18n();

  // Functions
  const startProgress = () => {
    showProgress.value = true;
  };

  const updateProgress = (current: number, max: number, progressLabelText?: string): void => {
    progress.value = (1 / max) * (current + 1);
    const text = progressLabelText ?? 'previews';
    progressLabel.value = t('syndicate_plus.below_the_fold.dialog.progress.label', {
      current,
      max,
      text,
    });
  };

  const cancelProgress = () => {
    shouldCancel.value = true;
  };

  const checkCancellation = () => {
    if (shouldCancel.value) {
      throw new CancellationError();
    }
  };

  const resetProgress = () => {
    showProgress.value = false;
    progress.value = 0;
    progressLabel.value = '';
    shouldCancel.value = false;
  };

  return {
    startProgress,
    updateProgress,
    cancelProgress,
    checkCancellation,
    resetProgress,
    showProgress,
    progress,
    progressLabel,
  };
}
