<template>
  <div class="counter">
    <div v-if="numberOfSelectedItems">
      <strong>{{ numberOfSelectedItems }}</strong>
      {{ `${$t('syndicate_plus.common.selected_mask', { totalItems, totalItemsText })}` }}
    </div>
    <div v-else>
      {{ `${totalItems} ${totalItemsText}` }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
  selectedItems: {
    type: Number,
    required: true,
    validator: (propValue: number) => propValue >= 0,
  },
  totalItems: {
    type: Number,
    required: true,
    validator: (propValue: number) => propValue >= 0,
  },
  totalItemsText: {
    type: String,
    default: '',
  },
});

const numberOfSelectedItems = computed(() => {
  return props.selectedItems > 0 ? props.selectedItems : 0;
});
</script>

<style scoped lang="scss">
.counter {
  margin-left: auto;
  padding-right: 10px;
}
</style>
