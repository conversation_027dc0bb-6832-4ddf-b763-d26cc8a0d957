import { describe, it, expect, vi } from 'vitest';
import { computed, ref } from 'vue';
import { useCollectionButtons } from '@composables/Collection';
import { SyndicationPageTabNames, CollectionUploadType } from '@enums';
import { CollectionDetails } from '@customTypes/CollectionDetails';

import useEmitter from '@composables/useEmitter';

vi.mock('@composables/useEmitter');

(useEmitter as any).mockReturnValue({
  on: vi.fn(),
});

describe('useCollectionButtons', () => {
  const tab = ref(SyndicationPageTabNames.Collection);
  const isProductsTabWithSelectedProducts = computed(() => true);
  const {
    isRenameCollectionButtonDisabled,
    isDeleteCollectionButtonVisible,
    isCreateEmptyCollectionButtonVisible,
    isGoToCollectionDetailsButtonVisible,
    onCollectionRowClick,
  } = useCollectionButtons(tab, isProductsTabWithSelectedProducts);

  describe('rename button', () => {
    // Act
    it('should be disabled when selected collection is not user created', async () => {
      const collectionDetails = ref({
        id: 1,
        name: 'test',
        uploadType: CollectionUploadType.FILE_IMPORT,
      } as CollectionDetails);
      onCollectionRowClick(collectionDetails.value);
      // Arrange
      // Assert
      expect.soft(isRenameCollectionButtonDisabled.value).toBe(true);
    });

    it('should not be disabled when selected collection is user created', async () => {
      const collectionDetails = ref({
        id: 1,
        name: 'test',
        uploadType: CollectionUploadType.USER_CREATED,
      } as CollectionDetails);
      onCollectionRowClick(collectionDetails.value);
      // Arrange
      // Assert
      expect.soft(isRenameCollectionButtonDisabled.value).toBe(false);
    });
  });
  it('should show delete, create empty collection and details buttons when on collection tab and collection is selected', async () => {
    const collectionDetails = ref({
      id: 1,
      name: 'test',
      uploadType: CollectionUploadType.USER_CREATED,
    } as CollectionDetails);
    onCollectionRowClick(collectionDetails.value);
    // Arrange
    // Assert
    expect.soft(isDeleteCollectionButtonVisible.value).toBe(true);
    expect.soft(isCreateEmptyCollectionButtonVisible.value).toBe(true);
    expect.soft(isGoToCollectionDetailsButtonVisible.value).toBe(true);
  });

  it('should hide delete button when collection has been deleted', async () => {
    // Arrange
    const tab = ref(SyndicationPageTabNames.Collection);
    const isProductsTabWithSelectedProducts = computed(() => true);
    const collectionDetails = ref({
      id: 1,
      name: 'test',
    } as CollectionDetails);
    const { isDeleteCollectionButtonVisible, onCollectionRowClick, onCollectionDeleted } = useCollectionButtons(
      tab,
      isProductsTabWithSelectedProducts
    );
    // Act
    onCollectionRowClick(collectionDetails.value);
    onCollectionDeleted(collectionDetails.value);
    // Assert
    expect(isDeleteCollectionButtonVisible.value).toBe(false);
  });
});
