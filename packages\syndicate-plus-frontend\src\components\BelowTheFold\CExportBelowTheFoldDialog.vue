<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <c-dialog class="c-dialog" :model-value="showProgress" hide-confirm-button @cancel="cancelProgress">
      <q-linear-progress size="25px" :value="progress" color="green" class="progress">
        <div class="absolute-full flex flex-center">
          <span class="progress-label">{{ progressLabel }}</span>
        </div>
      </q-linear-progress></c-dialog
    >
    <div class="row q-gutter-lg w-1/2">
      <div class="col section">
        <h3>
          {{ $t('syndicate_plus.below_the_fold.dialog.title') }}
        </h3>
        <q-input
          v-model="exportName"
          v-bind="$inri.input"
          :label="$t('syndicate_plus.below_the_fold.dialog.export_name')"
          hide-bottom-space
        />
        <c-select
          v-model="selectedTemplate"
          :label="$t('syndicate_plus.common.template')"
          :options="templates"
          option-value="aplusTemplateId"
          option-label="templateName"
          hide-bottom-space
          clearable
        />
        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.below_the_fold.dialog.export')"
            :disabled="exportButtonIsDisabled"
            @click="logEventAndClick(ActionName.EXPORT_BELOW_THE_FOLD, onSave)"
          />
        </div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useDialog, notify } from '@inriver/inri';
import { useAppInsightsStore } from '@stores';
import { ActionName, ProductGrouping } from '@enums';
import { APlusTemplate } from '@customTypes/Aplus';
import { CancellationError } from '@customTypes';
import TemplateService from '@services/TemplateService';
import { useExportBelowTheFold, useProgress } from '@composables/ExportBelowTheFold';
import { useProductsStore } from '@stores';

const props = defineProps({
  destinationId: {
    type: Number,
    required: true,
  },
  subCatalogId: {
    type: Number,
    required: true,
  },
});
defineExpose({ closeDialog });

// Refs
const exportName = ref('');
const templates = ref<APlusTemplate[]>([]);
const selectedTemplate = ref<APlusTemplate | undefined>();
const { selectedProducts } = storeToRefs(useProductsStore());

// Computed
const exportButtonIsDisabled = computed(() => isLoading.value || !exportName.value?.trim() || !selectedTemplate.value);
const { t } = useI18n();

// Composables
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel, confirmSuccess } = useDialog();
const { exportBelowTheFold } = useExportBelowTheFold(selectedProducts);
const { showProgress, progress, progressLabel, cancelProgress } = useProgress();

// Functions
const onSave = async () => {
  if (!selectedTemplate.value) {
    throw new Error('template can not be undefined');
  }

  try {
    await exportBelowTheFold(exportName.value, selectedTemplate.value, props.subCatalogId, props.destinationId);
  } catch (error) {
    if (!(error instanceof CancellationError)) {
      notify.error(t('syndicate_plus.below_the_fold.dialog.error'), {
        position: 'bottom-right',
      });
    }
  }

  confirmSuccess(null);
};

function closeDialog() {
  confirmSuccess(null);
}

// Lifecycle methods
onBeforeMount(async () => {
  isLoading.value = true;
  try {
    templates.value = await TemplateService.getAPlusTemplates([], ProductGrouping.SKU, 1000, 1, [{}]);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped lang="scss">
:deep(.q-linear-progress__model) {
  opacity: 0.5;
}

.progress-label {
  opacity: 1;
  color: var(--color-text);
  font-size: 14px;
}

.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }

  .action-buttons {
    padding-top: 10px;
  }
}
</style>
