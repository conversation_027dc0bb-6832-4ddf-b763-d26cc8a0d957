<template>
  <q-table
    ref="tableRef"
    v-model:selected="selected"
    flat
    dense
    hide-bottom
    separator="cell"
    class="collections-table sticky-table-header"
    :pagination="tablePagination"
    :rows="currentCollections"
    row-key="name"
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    :columns="columns"
    binary-state-sort
    virtual-scroll
    :loading="isCollectionsLoading"
    :virtual-scroll-item-size="31"
    :virtual-scroll-sticky-size-start="31"
    @virtual-scroll="onScroll"
    @row-click="onRowClick"
    @row-dblclick="onRowDoubleClick"
    @request="onRequest"
  >
    <template #loading>
      <q-inner-loading :showing="isCollectionsLoading" color="primary">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </template>
  </q-table>
</template>
<script setup lang="ts">
import { PropType, nextTick, toRefs, ref, onMounted } from 'vue';
import { CollectionDetails } from '@customTypes';
import { columns } from '@components/SyndicationPage/Collections/index';
import { useCollectionsStore } from '@stores/CollectionsStore';
import { storeToRefs } from 'pinia';

const props = defineProps({
  collections: {
    type: Array as PropType<CollectionDetails[]>,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  lastPage: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['fetch', 'on-row-click', 'go-to-collection', 'change-order-by']);

const collectionsStore = useCollectionsStore();

// Refs
const tableRef = ref();
const { collections: currentCollections, isLoading: isCollectionsLoading } = toRefs(props);
const { descending } = storeToRefs(collectionsStore);
const selected = ref<CollectionDetails[]>();
const tablePagination = ref({
  page: 1,
  rowsPerPage: 0,
  sortBy: 'name',
  descending: descending.value,
  rowsNumber: 1,
});

// Functions
async function onScroll(details) {
  const { to, ref } = details;
  const isPageBottom = to === currentCollections.value.length - 1;

  if (!props.isLoading && !props.lastPage && isPageBottom) {
    emits('fetch');
    nextTick(() => {
      ref.refresh();
    });
  }
}

const onRowClick = (_, row: CollectionDetails): void => {
  const newRowValue = selected.value?.includes(row) ? null : row;
  selected.value = newRowValue ? [newRowValue] : [];
  emits('on-row-click', newRowValue);
};

const onRowDoubleClick = (_, row: CollectionDetails): void => {
  selected.value = [row];
  emits('go-to-collection', row);
};

const onRequest = (tableProps) => {
  const { pagination } = tableProps;
  if (descending.value !== pagination.descending) {
    tablePagination.value.descending = pagination.descending;
    collectionsStore.clearStore();
    collectionsStore.setSortDirection(pagination.descending);
    emits('fetch');
  }
};

// Lifecycle methods
onMounted(() => {
  tableRef.value.requestServerInteraction();
});
</script>
<style scoped lang="scss">
.collections-table {
  max-height: calc(100vh - 203px);
  margin-bottom: 200px;
}
</style>
