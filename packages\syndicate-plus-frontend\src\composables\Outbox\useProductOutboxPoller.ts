import { watchEffect, Ref } from 'vue';
import { useInterval } from '@composables';
import { useProductOutboxStore } from '@stores';
import { SyndicationPageTabNames } from '@enums';
import { OutboxStatus } from '@enums/OutboxStatus';

export default function useProductOutboxPoller(activeTab: Ref<string>, destinationId: number) {
  // 720 retries with 5 seconds interval => retry for one hour
  const attempts = 720;
  const intervalMs = 5000;

  // Refs
  const { fetchDataSilently, getProductOutboxRows } = useProductOutboxStore();
  const { startInterval, stopInterval } = useInterval(attempts, intervalMs, refreshData);

  watchEffect(async () => {
    await onActiveTabChanged(activeTab.value);
  });

  // Functions
  async function onActiveTabChanged(activeTab: string) {
    if (activeTab === SyndicationPageTabNames.Outbox) {
      await startPolling();
    } else {
      stopInterval();
    }
  }

  async function refreshData() {
    await fetchDataSilently(destinationId);
    if (!hasNewUnfinishedJobs()) {
      stopInterval();
    }
  }

  async function startPolling() {
    //We need to do an initial fetch to know if there are unfinished jobs
    await fetchDataSilently(destinationId);
    hasNewUnfinishedJobs() && startInterval();
  }

  function hasNewUnfinishedJobs(): boolean {
    const currentDate = new Date();
    const oneHourAgoDate = new Date(currentDate.getTime() - 60 * 60 * 1000);

    return getProductOutboxRows()?.some(
      (outbox) =>
        (outbox.status.outboxStatus === OutboxStatus.IN_PROGRESS ||
          outbox.status.outboxStatus === OutboxStatus.IN_QUEUE) &&
        new Date(outbox.startDateTimeStamp) > oneHourAgoDate
    );
  }
}
