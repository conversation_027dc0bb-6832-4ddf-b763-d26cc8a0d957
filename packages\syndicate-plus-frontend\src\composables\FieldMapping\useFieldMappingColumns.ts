import { computed, Ref } from 'vue';

export default function useFieldMappingColumns(tradingPartnerName: Ref<string>) {
  // Computed
  const columns = computed(() => {
    const columns = [
      {
        name: 'status',
        field: 'status',
        label: '',
        align: 'left' as const,
        style: 'width: 5%',
      },
      {
        name: 'suggestedMatch',
        field: 'suggestedMatch',
        label: '',
        align: 'left' as const,
        style: 'width: 5%',
      },
      {
        name: 'sourceFieldName',
        field: 'sourceFieldName',
        label: 'source field',
        align: 'left' as const,
        style: 'width: 5%',
        featureFlag: true,
      },
      {
        name: 'actions',
        field: 'actions',
        label: '',
        align: 'center' as const,
        featureFlag: true,
      },
      {
        name: 'syndicationFieldName',
        field: 'syndicationFieldName',
        label: 'syndication field',
        align: 'left' as const,
        style: 'width: 20%',
      },
      {
        name: 'actions',
        field: 'actions',
        label: '',
        align: 'center' as const,
      },
      {
        name: 'targetFieldName',
        field: 'targetFieldName',
        label: `${tradingPartnerName.value.toLowerCase()} field`,
        align: 'left' as const,
        style: 'width: 20%; color: var(--color-grey-dark)',
      },
      {
        name: 'defaultValue',
        field: 'defaultValue',
        label: 'default value',
        align: 'left' as const,
        style: 'width: 13%',
      },
      {
        name: 'function',
        field: 'function',
        label: 'function',
        align: 'left' as const,
        style: 'width: 20%',
      },
      {
        name: 'importanceType',
        field: 'importanceType',
        label: 'importance',
        align: 'left' as const,
        style: 'width: 5%; color: var(--color-grey-dark)',
      },
      {
        name: 'dataType',
        field: 'dataType',
        label: `data type`,
        align: 'left' as const,
        style: 'width: 5%',
      },
      {
        name: 'format',
        field: 'format',
        label: `data format`,
        align: 'left' as const,
        style: 'width: 5%',
      },
      {
        name: 'length',
        field: 'length',
        label: `length`,
        align: 'left' as const,
        style: 'width: 8%',
      },
      {
        name: 'parentOverrideField',
        field: 'parentOverrideField',
        label: `override`,
        align: 'left' as const,
        style: 'width: 5%',
      },
    ];

    return columns;
  });

  return { columns };
}
