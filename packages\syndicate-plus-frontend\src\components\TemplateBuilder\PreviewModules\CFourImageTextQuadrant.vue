<template>
  <div class="mx-auto min-w-1000px w-1000px four-images-text-quadrant-preview">
    <div v-for="row in numberOfBlocks / 2" :key="row" class="flex flex-row flex-nowrap">
      <div
        v-for="column in numberOfBlocks / 2"
        :key="column"
        class="module-block text-wrap-word-break m-10px four-image"
      >
        <div class="flex flex-row flex-nowrap" :data-testid="`${getOrderIndex(row, column)}-four-image-text-quadrant`">
          <c-caption-angle-image-block-preview
            :image-data="module?.data[`image${getOrderIndex(row, column)}`]"
            image-data-class="image mb-10px"
          />
          <div class="headline">{{ module?.data[`headline${getOrderIndex(row, column)}`] }}</div>
        </div>
        <c-html-preview :html="module?.data[`body${getOrderIndex(row, column)}`]" class="description" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';
import { ContentModule, FourImageTextQuadrantData } from '@customTypes/Aplus';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Variables
const numberOfBlocks = 4;

// Computed
const module = computed<ContentModule<FourImageTextQuadrantData>>(() => {
  return store.getModuleByIndex(props.index);
});

// Functions
const getOrderIndex = (row: number, column: number) => {
  return (row - 1) * 2 + column;
};
</script>

<style lang="scss" scoped>
.four-images-text-quadrant-preview {
  .headline {
    font-size: 18px;
    padding-left: 30px;
    font-weight: bold;
    width: 335px;
  }

  .description {
    padding: 0px;
  }

  .four-image {
    :deep() {
      .image {
        object-fit: cover;
        width: 135px;
        min-width: 135px;
        height: 135px;
        min-height: 135px;

        &img {
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
