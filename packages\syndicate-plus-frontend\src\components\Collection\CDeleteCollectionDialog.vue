<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-confirm-button
    hide-cancel-button
    @cancel="cancel"
  >
    <h3>{{ $t('syndicate_plus.collections.delete_collection_dialog') }}</h3>

    <div class="action-buttons mt-10">
      <c-btn
        :label="$t('syndicate_plus.collections.delete_collection')"
        @click="logEventAndClick(ActionName.DELETE_COLLECTION, deleteCollection)"
      >
      </c-btn>
      <c-btn
        class="ml-10px"
        color="surface"
        :label="$t('syndicate_plus.common.cancel')"
        @click="logEventAndClick(ActionName.CANCEL_DELETE_COLLECTION, cancel)"
      >
      </c-btn>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { useEmitter } from '@composables';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';

// Composables
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel } = useDialog();

const emitter = useEmitter();

// Functions
const deleteCollection = async () => {
  emitter.emit('delete-collection');
};
</script>

<style scoped lang="scss"></style>
