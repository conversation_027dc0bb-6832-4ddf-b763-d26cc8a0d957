parameters:
  - name: serviceConnection
    displayName: Azure Service Connection
    type: string

  - name: appName
    displayName: Application name
    type: string

steps:
  - checkout: self
  - task: PowerShell@2
    displayName: Get location
    inputs:
      targetType: 'inline'
      script: |
        Switch ('$(region)')
        {
            'euw' {Write-Host "##vso[task.setvariable variable=location;]westeurope"}
            'use' {Write-Host "##vso[task.setvariable variable=location;]eastus"}
        }

  - task: AzureCLI@2
    displayName: Create Resources
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: bash
      scriptLocation: inlineScript
      inlineScript: |
        az --version
        az group create --name ${{ parameters.appName }} --location $(location)
        az deployment group create \
          --resource-group ${{ parameters.appName }} \
          --template-file 'azure/infrastructure/main.bicep' \
          --parameters \
            appName=${{ parameters.appName }} \
            customDomain=$(customDomain)
