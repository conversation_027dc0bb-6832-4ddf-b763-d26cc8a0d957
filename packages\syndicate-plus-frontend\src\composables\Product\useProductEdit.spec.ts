import { describe, it, expect, vi } from 'vitest';
import { useProductEdit } from '@composables/Product';

describe('useProductEdit', () => {
  describe('toggleEditMode', () => {
    it('sets the isEditEnabled value correctly', async () => {
      // Arrange
      const { isEditingMode, toggleEditMode } = useProductEdit();
      isEditingMode.value = false;
      const newEditingModeValue = true;

      // Act
      toggleEditMode(newEditingModeValue);

      // Assert
      expect(isEditingMode.value).toBe(newEditingModeValue);
    });
  });

  describe('isEditButtonVisible', () => {
    it('returns true if editing is allowed at the environment settings level', async () => {
      // Arrange
      vi.mock('@composables', () => {
        return {
          useEnvironmentSettings: vi.fn().mockReturnValue({
            isEditEnabled: {
              value: true,
            },
          }),
        };
      });
      const { isEditButtonVisible } = useProductEdit();

      // Act + Assert
      expect(isEditButtonVisible.value).toBeTruthy();
    });
  });
});
