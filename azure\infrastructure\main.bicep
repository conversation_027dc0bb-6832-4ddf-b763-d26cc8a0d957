param location string = resourceGroup().location

param costcenter string = '1040'

@description('Sets the the full application name for creating the RG and rescources')
param appName string

@description('Its not possible to deploy SWA to eastus region so we need to set region explicit')
param useStaticWebAppLocation string = 'eastus2'

param customDomain string

module euwStaticWebApp './modules/staticWebApp.bicep' = if (location == 'westeurope') {
  name: 'euwStaticWebApp'
  params: {
    costcenter: costcenter
    location: location
    appName: appName
    customDomain: customDomain
  }
}

module useStaticWebApp './modules/staticWebApp.bicep' = if (location == 'eastus') {
  name: 'useStaticWebApp'
  params: {
    costcenter: costcenter
    location: useStaticWebAppLocation
    appName: appName
    customDomain: customDomain
  }
}

module euwApplicationInsights './modules/applicationInsights.bicep' = if (location == 'westeurope') {
  name: 'euwApplicationInsights'
  params: {
    location: location
    appName: appName
  }
}

module useApplicationInsights './modules/applicationInsights.bicep' = if (location == 'eastus') {
  name: 'useApplicationInsights'
  params: {
    location: location
    appName: appName
  }
}
