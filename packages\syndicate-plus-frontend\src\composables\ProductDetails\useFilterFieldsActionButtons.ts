import { computed, Ref, ComputedRef } from 'vue';
import { useLocalStorage } from '@vueuse/core';
import { FilterField } from '@customTypes/Products';

export default function useFilterFieldsActionButtons(
  allColumns: ComputedRef<FilterField[]>,
  selectedColumnNames: Ref<string[]>,
  applyFilterSelections: Function,
  destinationId?: number
) {
  // Refs
  const isFilterInitialized = useLocalStorage(`aplus.${destinationId}.details.is-filter-initialized`, false);

  // Computed
  const isSelectedAll = computed(() => {
    if (!selectedColumnNames.value?.length) {
      return false;
    }

    if (selectedColumnNames.value?.length === allColumns.value?.length) {
      return true;
    }

    return null;
  });

  // Functions
  const selectAll = (): void => {
    selectedColumnNames.value = selectedColumnNames.value?.length ? [] : allColumns.value.map((x) => x.name);
    applyFilterSelections && applyFilterSelections();
  };

  const clearFilter = (): void => {
    selectedColumnNames.value = getDefaultColumnNames();
    applyFilterSelections && applyFilterSelections();
  };

  const getDefaultColumnNames = () => {
    isFilterInitialized.value = true;

    return allColumns.value.filter((x) => x.isRequired).map((x) => x.name);
  };

  return { isFilterInitialized, isSelectedAll, selectAll, clearFilter, getDefaultColumnNames };
}
