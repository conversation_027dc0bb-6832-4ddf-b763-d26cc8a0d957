import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import { InfoColor } from '@enums';
import { CInfoBlock } from '@components/Shared';

describe('CInfoBlock', () => {
  it('calculates right custom classes for the wrapper info container', async () => {
    // Arrange
    const props = {
      bgColor: InfoColor.LIGHTORANGE,
      borderColor: InfoColor.ORANGE,
      title: 'test title',
      description: 'test description',
    };

    // Act
    const wrapper = mount(CInfoBlock, {
      props,
      global: {
        mocks: {
          $t: (msg: string) => msg,
        },
      },
    });

    // Assert
    expect(wrapper.find('.info-container').classes()).toContain('bg-orange-light');
    expect(wrapper.find('.info-container').classes()).toContain('orange');
  });

  it('calculates right custom classes for close button', async () => {
    // Arrange
    const props = {
      bgColor: InfoColor.LIGHTORANGE,
      borderColor: InfoColor.ORANGE,
      title: 'test title',
      description: 'test description',
    };

    // Act
    const wrapper = mount(CInfoBlock, {
      props,
      global: {
        mocks: {
          $t: (msg: string) => msg,
        },
      },
    });

    // Assert
    expect(wrapper.find('.close-button').classes()).toContain('btn-orange-light');
  });
});
