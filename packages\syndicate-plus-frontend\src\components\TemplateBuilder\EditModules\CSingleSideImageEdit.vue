<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <div class="flex m-10px justify-center">
      <div v-if="alignLeft" class="flex flex-row flex-nowrap">
        <c-caption-angle-image-block
          v-model="module.data.image"
          :dimensions="dimensions"
          :hide-caption="true"
          :max-alt-text-length="Validation.singleSideImageLeft.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url)"
        />
        <div class="ml-20px min-w-500px">
          <q-input
            v-model="module.data.headline"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.headline')"
            :maxlength="Validation.singleSideImageLeft.headline.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data.headline, 'headline')"
          />
          <c-text-editor
            v-model="module.data.body"
            :max-length="Validation.singleSideImageLeft.body.maxLength"
            @on-field-drop="onDrop(module.data.body, 'body')"
          />
        </div>
      </div>
      <div v-else class="flex flex-row flex-nowrap">
        <div class="mr-20px min-w-500px">
          <q-input
            v-model="module.data.headline"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.headline')"
            :maxlength="Validation.singleSideImageRight.headline.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data.headline, 'headline')"
          />
          <c-text-editor
            v-model="module.data.body"
            :max-length="Validation.singleSideImageRight.body.maxLength"
            @on-field-drop="onDrop(module.data.body, 'body')"
          />
        </div>
        <c-caption-angle-image-block
          v-model="module.data.image"
          :dimensions="dimensions"
          :hide-caption="true"
          :max-alt-text-length="Validation.singleSideImageRight.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url)"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, SingleSideImageData, Dimension } from '@customTypes/Aplus';
import { CTextEditor, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
  alignLeft: {
    type: Boolean,
    default: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Refs
const module = ref<ContentModule<SingleSideImageData>>({} as ContentModule<SingleSideImageData>);

// Variables
const dimensions = { width: 300, height: 300 } as Dimension;

// Functions
const handleUrlUpdate = (url: string) => {
  module.value.data.image.angle = url;

  updateModel();
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, moduleParameter) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data[moduleParameter] = result;
    updateModel();
  }
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      body: '',
      headline: '',
      image: {
        altText: '',
        angle: '',
      },
    } as SingleSideImageData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
<style lang="scss" scoped></style>
