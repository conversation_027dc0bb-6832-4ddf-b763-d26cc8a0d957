variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'use'
  stackType: 'dev1a'

  # Deploy dependencies
  dependsOn: 'Build_useDev1a'
  dependsOnSecond: 'Build_useDev1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-dev1a-use.productmarketingcloud.com'

  auth0Domain: 'inriverdev.eu.auth0.com'
  auth0ClientId: 'nbL2G6DGbSzuA3PHlw8gA9WjCBtCwOUz'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: ''

  allowMissingOrgMapping: true
  
  # Application insights instrumentation key
  instrumentationKey: '4cb4f6e2-822f-44c4-b038-564da3c2fdf1'