div.header {
  display: none;
}

app-navigation-list {
  display: none;
}

.aplus-content-preview {
  padding: 0 !important;
}

div.aplus-content-preview div.preview-header div.title {
  display: none !important;
}

.column {
  height: auto !important;
}

app-button .button {
  color: white !important;
  height: 40px !important;
  border-radius: 20px !important;
  line-height: 40px !important;
  font-weight: 500 !important;
  padding: 0 20px !important;
  min-width: 150px !important;
  font-size: 16px !important;
  text-transform: lowercase !important;
}

app-button .button.blue {
  background-color: black !important;
}

.breadcrumb li {
  text-align: center;
  border-right-style: solid;
  border-right-color: white;
  border-right-width: thin;
}

.breadcrumb li a {
  color: black !important;
  font-weight: bold;
  text-transform: lowercase;
  padding-left: 25px !important;
  padding-right: 25px !important;
  background-color: lightgray !important;
  cursor: pointer !important;
}

.breadcrumb li a.active {
  background-color: #66d0a8 !important;
}

.breadcrumb li a:after {
  content: none !important;
}

.breadcrumb li a:before {
  content: none !important;
}

app-modal.backdrop.show {
  left: 0 !important;
  width: 100% !important;
}

.aplus-content-preview,
.section,
.section-body,
.aplus-module-container,
.aplus-module-container_2,
.field-label,
.pending-list,
.pending-list-2 {
  animation: none !important;
}
