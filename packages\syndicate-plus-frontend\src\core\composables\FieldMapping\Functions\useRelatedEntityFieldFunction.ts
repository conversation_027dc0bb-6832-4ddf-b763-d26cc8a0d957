import { computed, Ref, ref } from 'vue';
import { storeToRefs } from 'pinia';
import {
  RelatedEntityFieldSettings,
  RelatedEntityFieldArgs,
  RelatedEntityFieldValues,
  RelatedEntityFieldModel,
  RelatedEntityResponse,
  RelationsObject,
  Relation,
  ConverterTransformation,
  RelationsModel,
} from '@core/interfaces/FieldMapping/Functions';
import { EntityType, InriverFieldType, Relative } from '@core/interfaces';
import { EntityTypeRelations, InriverDataType } from '@core/enums';
import { useConverterArgsParser } from '@core/composables/FieldMapping/Functions';
import { useLanguages } from '@core/composables/Common';
import { useEditFieldMappingStore } from '@core/stores';
import { fetchRelatedEntities } from '@core/services';
import { getFieldTypes } from '@core/services/FieldTypes';
import { checkIfFieldTypeIsLocaleString } from '@core/Utils';

export default function useRelatedEntityFieldFunction(entityTypes: Ref<EntityType[]>, converterArgs?: string) {
  // Variables
  const maxRelationEntityTypeLevels = 3;

  // Stores
  const editFieldMappingStore = useEditFieldMappingStore();

  // Refs
  const currentModel = ref<RelatedEntityFieldModel>();
  const relatedEntities = ref<RelatedEntityResponse[]>([]);
  const currentFieldTypes = ref<InriverFieldType[]>([]);
  const isLoading = ref(false);
  const { mapping } = storeToRefs(editFieldMappingStore);

  // Composables
  const { transformatedFunction } = useConverterArgsParser<RelatedEntityFieldSettings>(converterArgs);
  const { languages } = useLanguages();

  // Computed
  const settings = computed(() => {
    return getRelatedEntityFieldFunctionSettings();
  });

  const selectedFieldTypeIsLocaleString = computed(() => checkIfFieldTypeIsLocaleString(currentModel.value?.field));

  const isAddButtonVisible = computed(
    () =>
      !!currentModel.value?.relations.length &&
      currentModel.value.relations.length < maxRelationEntityTypeLevels &&
      currentFieldTypes.value.length
  );

  const isRemoveButtonVisible = computed(
    () => !!currentModel.value?.relations.length && currentModel.value?.relations.length > 1
  );

  const mappingEntityTypes = computed(() => {
    if (!mapping.value?.WorkareaEntityTypeId) {
      return [];
    }

    const existingMappingEntityTypes = [] as EntityType[];
    const mappingEntityTypeIds = [
      mapping.value.WorkareaEntityTypeId,
      mapping.value.FirstRelatedEntityTypeId,
      mapping.value.SecondRelatedEntityTypeId,
    ];
    for (const entityTypeId of mappingEntityTypeIds) {
      const entityType = getEntityTypeById(entityTypeId);
      if (entityType) {
        existingMappingEntityTypes.push(entityType);
      }
    }

    return existingMappingEntityTypes;
  });

  const lastSelectedEntityTypeId = computed(
    () => currentModel.value?.relations[currentModel.value?.relations.length - 1].relative?.Id ?? ''
  );

  // Functions
  const onRelationChange = async (index: number) => {
    if (index < 0 || index >= maxRelationEntityTypeLevels || !currentModel.value) {
      return;
    }

    currentModel.value.relations.splice(index + 1);
    currentModel.value.relations[index].relative = getRelativesByIndex(index)[0];
    await fetchRelatedEntitiesAndFieldTypes();
  };

  const onEntityTypeChange = async (index: number) => {
    if (index < 0 || index >= maxRelationEntityTypeLevels || !currentModel.value) {
      return;
    }

    currentModel.value.relations.splice(index + 1);
    await fetchRelatedEntitiesAndFieldTypes();
  };

  const getRelativesByIndex = (index: number): Relative[] => {
    let relatives: Relative[] = [];
    if (
      index < 0 ||
      index >= maxRelationEntityTypeLevels ||
      !currentModel.value ||
      !currentModel.value.relations?.length
    ) {
      return [];
    }

    if (index === 0) {
      relatives =
        currentModel.value.relations[index].relation === EntityTypeRelations.PARENT
          ? currentModel.value.mainEntityType.Parents
          : currentModel.value.mainEntityType.Children;
    }

    if (index > 0) {
      const relation = currentModel.value.relations[index];
      const currentRelative = currentModel.value.relations[index - 1].relative;
      const currentEntityType = currentRelative ? getEntityTypeById(currentRelative.Id) : undefined;
      if (!currentEntityType) {
        return [];
      }

      if (relation) {
        relatives =
          relation.relation === EntityTypeRelations.PARENT ? currentEntityType.Parents : currentEntityType.Children;
      }
    }

    return relatives.filter(
      (entityType) =>
        mapping.value?.FirstLinkEntityTypeId !== entityType.LinkEntityTypeId &&
        mapping.value?.SecondLinkEntityTypeId !== entityType.LinkEntityTypeId &&
        !currentModel.value?.relations.some((relation) => relation.relative?.Id === entityType.LinkEntityTypeId)
    );
  };

  const getEntityTypeById = (id: string): EntityType | undefined =>
    entityTypes.value.find((entityType) => entityType.Id === id);

  const getLinkIdsPath = (): string => {
    if (!currentModel.value) {
      return '';
    }

    const linkEntityTypeIds = currentModel.value.relations.map((relation) => relation.relative?.LinkEntityTypeId);
    if (mapping.value?.WorkareaEntityTypeId === currentModel.value.mainEntityType.Id) {
      return linkEntityTypeIds.join(',');
    }

    if (currentModel.value.mainEntityType.Id === mapping.value?.FirstRelatedEntityTypeId) {
      mapping.value.FirstLinkEntityTypeId && linkEntityTypeIds.unshift(mapping.value.FirstLinkEntityTypeId);
      return linkEntityTypeIds.join(',');
    }

    if (currentModel.value.mainEntityType.Id === mapping.value?.SecondRelatedEntityTypeId) {
      mapping.value.SecondLinkEntityTypeId && linkEntityTypeIds.unshift(mapping.value.SecondLinkEntityTypeId);
      mapping.value.FirstLinkEntityTypeId && linkEntityTypeIds.unshift(mapping.value.FirstLinkEntityTypeId);
      return linkEntityTypeIds.join(',');
    }

    return linkEntityTypeIds.join(',');
  };

  const getDirectionsPath = (): string => {
    if (!currentModel.value) {
      return '';
    }

    const currentModelDirectionsPath = currentModel.value.relations.map((relation) =>
      getRelationPath(relation.relation)
    );

    if (mapping.value?.WorkareaEntityTypeId === currentModel.value.mainEntityType.Id) {
      return currentModelDirectionsPath.join(',');
    }

    if (currentModel.value.mainEntityType.Id === mapping.value?.FirstRelatedEntityTypeId) {
      // mapping first relation entity type relation to workarea entity type
      const relation = getRelation(mapping.value.WorkareaEntityTypeId, mapping.value.FirstRelatedEntityTypeId);
      if (relation) {
        currentModelDirectionsPath.unshift(getRelationPath(relation));
      }
    }

    if (currentModel.value.mainEntityType.Id === mapping.value?.SecondRelatedEntityTypeId) {
      // mapping first relation entity type relation to workarea entity type
      const relation = getRelation(mapping.value.FirstRelatedEntityTypeId, mapping.value.WorkareaEntityTypeId);
      if (relation) {
        currentModelDirectionsPath.unshift(getRelationPath(relation));
      }
      // mapping second related entity type relation to first related entity type
      const secondRelation = getRelation(
        mapping.value.SecondRelatedEntityTypeId,
        mapping.value.FirstRelatedEntityTypeId
      );
      if (secondRelation) {
        currentModelDirectionsPath.unshift(getRelationPath(secondRelation));
      }
    }

    return currentModelDirectionsPath.join(',');
  };

  const getRelationModel = (): RelationsObject => {
    if (!currentModel.value) {
      return {} as RelationsObject;
    }

    const relations = {} as Relation;
    currentModel.value.relations.forEach((relation, index) => {
      const relative = relation.relative;
      if (relative) {
        relations[index + 1] = {
          direction: relation.relation,
          dropdownValue: JSON.stringify({
            entityTypeId: relative.Id,
            linkEntityTypeId: relative.LinkEntityTypeId,
          }),
        };
      }
    });
    const settingsJsonObject = {
      mainEntityTypeId: currentModel.value.mainEntityType.Id,
      relations,
      fieldTypeId: currentModel.value.field.id,
      language:
        selectedFieldTypeIsLocaleString.value && currentModel.value.language ? currentModel.value.language.Name : '',
      entityId: currentModel.value.entity.Key.toString(),
    } as RelationsObject;

    return settingsJsonObject;
  };

  const addRelation = async () => {
    if (
      !currentModel.value ||
      !currentModel.value.relations?.length ||
      currentModel.value.relations.length >= maxRelationEntityTypeLevels
    ) {
      return;
    }

    if (!lastSelectedEntityTypeId.value) {
      return;
    }

    if (currentModel.value) {
      currentModel.value.relations.push({
        relation: EntityTypeRelations.PARENT,
      });
      const currentIndex = currentModel.value.relations.length - 1;
      currentModel.value.relations[currentIndex].relative = getRelativesByIndex(currentIndex)[0];
    }

    await fetchRelatedEntitiesAndFieldTypes();
  };

  const removeRelation = async (index: number) => {
    if (
      !currentModel.value ||
      index < 0 ||
      index >= maxRelationEntityTypeLevels ||
      currentModel.value?.relations.length === 1
    ) {
      return;
    }

    currentModel.value.relations.splice(index);
    await fetchRelatedEntitiesAndFieldTypes();
  };

  const setDefaultValues = async () => {
    const currentIndex = 0;
    const currentModelMainEntityType = currentModel.value?.mainEntityType ?? mappingEntityTypes.value[0];
    if (!currentModelMainEntityType) {
      return;
    }

    currentModel.value = {
      mainEntityType: currentModelMainEntityType,
      relations: [
        {
          relation: EntityTypeRelations.PARENT,
          relative: undefined,
        },
      ],
      entity: {} as RelatedEntityResponse,
      field: {} as InriverFieldType,
    } as RelatedEntityFieldModel;
    trySetFirstAvailableEntityTypeForIndex(currentIndex);
  };

  const trySetFirstAvailableEntityTypeForIndex = async (index: number) => {
    const availableRelatedEntityTypes = getRelativesByIndex(0);
    if (availableRelatedEntityTypes?.length && !!currentModel.value) {
      currentModel.value.relations[index].relative = availableRelatedEntityTypes[0];
      onEntityTypeChange(index);
    }
  };

  const getRelation = (currentEntityTypeId: string, nextEntityTypeId: string): EntityTypeRelations | undefined => {
    const currentEntityType = getEntityTypeById(currentEntityTypeId);
    const nextEntityType = getEntityTypeById(nextEntityTypeId);
    if (!currentEntityType || !nextEntityType) {
      return undefined;
    }

    if (currentEntityType.Parents.find((parent) => parent.Id === nextEntityType.Id)) {
      return EntityTypeRelations.PARENT;
    }

    if (currentEntityType.Children.find((child) => child.Id === nextEntityType.Id)) {
      return EntityTypeRelations.CHILD;
    }

    return undefined;
  };

  const getRelationPath = (relation: EntityTypeRelations): '0' | '1' =>
    relation === EntityTypeRelations.PARENT ? '0' : '1';

  const onMainEntityTypeChange = async () => {
    await setDefaultValues();
  };

  const fetchAndSetRelatedEntitiesByEntityType = async (entityTypeId: string) => {
    await fetchRelatedEntitiesByEntityType(entityTypeId);
    setFirstRelatedEntity();
  };

  const fetchRelatedEntitiesByEntityType = async (
    entityTypeId: string,
    currentPage?: number,
    selectedId?: number
  ): Promise<void> => {
    relatedEntities.value = await fetchRelatedEntities(entityTypeId, currentPage, selectedId);
  };

  const setFirstRelatedEntity = () => {
    if (!currentModel.value || !relatedEntities.value?.length) {
      return;
    }

    currentModel.value.entity = relatedEntities.value[0];
  };

  const fetchAndSetRelatedFieldTypes = async (entityTypeId: string) => {
    await fetchRelatedFieldTypes(entityTypeId);
    setFirstFieldType();
    setFirstLanguage();
  };

  const fetchRelatedFieldTypes = async (entityTypeId: string) => {
    if (!currentModel.value) {
      return;
    }

    currentFieldTypes.value = [];
    if (!lastSelectedEntityTypeId.value) {
      return;
    }

    const inriverFieldTypesResponse = await getFieldTypes(entityTypeId);
    currentFieldTypes.value = editFieldMappingStore.toInriverFieldTypes(inriverFieldTypesResponse);
  };

  const setFirstFieldType = () => {
    if (!currentModel.value || !currentFieldTypes.value?.length) {
      return;
    }

    currentModel.value.field = currentFieldTypes.value[0];
  };

  const setFirstLanguage = () => {
    if (currentModel.value?.field.dataType === InriverDataType.LOCALE_STRING && !!languages.value.length) {
      currentModel.value.language = languages.value[0];
    }
  };

  const fetchRelatedEntitiesAndFieldTypes = async () => {
    isLoading.value = true;
    await fetchAndSetRelatedFieldTypes(lastSelectedEntityTypeId.value);
    await fetchAndSetRelatedEntitiesByEntityType(lastSelectedEntityTypeId.value);
    isLoading.value = false;
  };

  const getRelatedEntityFieldFunctionSettings = (): ConverterTransformation => {
    const languageName = currentModel.value?.language ? currentModel.value?.language.Name : '';
    const fieldTypeId = currentModel.value?.field ? currentModel.value?.field.id : '';
    const linkIdsPath = getLinkIdsPath();
    const directionPath = getDirectionsPath();
    const relationModel = getRelationModel();
    const args = [
      lastSelectedEntityTypeId.value,
      fieldTypeId,
      languageName,
      linkIdsPath,
      directionPath,
      JSON.stringify(relationModel),
      currentModel.value?.entity?.Key.toString() ?? '',
    ] as RelatedEntityFieldArgs;
    const values: RelatedEntityFieldValues = [];
    return { function: new RelatedEntityFieldSettings(args, values) } as ConverterTransformation;
  };

  const initCurrentModel = async () => {
    isLoading.value = true;
    try {
      const argsLastSelectedEntityTypeIndex = 1;
      const argsLanguageIndex = 2;
      const argsRelatinModelIndex = 5;
      const argsEntityIdIndex = 6;
      const args = transformatedFunction.value.args;
      const relationSettings = JSON.parse(args[argsRelatinModelIndex]) as RelationsObject;
      const { mainEntityTypeId } = relationSettings;
      const language =
        args[argsLanguageIndex] && languages.value
          ? languages.value.find((lang) => lang.Name === args[argsLanguageIndex])
          : undefined;
      currentModel.value = {
        mainEntityType: getEntityTypeById(mainEntityTypeId) ?? ({} as EntityType),
        language: language,
        entity: undefined as any,
        field: undefined as any,
        relations: [],
      } as RelatedEntityFieldModel;

      const relationKeys = Object.keys(relationSettings.relations);
      for (let i = 0; i < relationKeys.length; i++) {
        currentModel.value.relations.push({
          relation: relationSettings.relations[relationKeys[i]].direction as EntityTypeRelations,
          relative: {
            DisplayName: '', // TODO:
            Id: JSON.parse(relationSettings.relations[relationKeys[i]].dropdownValue).entityTypeId,
            LinkEntityTypeId: JSON.parse(relationSettings.relations[relationKeys[i]].dropdownValue).linkEntityTypeId,
          } as Relative,
        } as RelationsModel);
      }

      await fetchRelatedFieldTypes(lastSelectedEntityTypeId.value);
      await fetchRelatedEntitiesByEntityType(lastSelectedEntityTypeId.value);

      currentModel.value.entity =
        relatedEntities.value?.find((x) => x.Key.toString() === args[argsEntityIdIndex].toString()) ??
        ({} as RelatedEntityResponse);
      currentModel.value.field =
        currentFieldTypes.value.find((x) => x.id === args[argsLastSelectedEntityTypeIndex]) ?? ({} as InriverFieldType);
    } finally {
      isLoading.value = false;
    }
  };

  const onDialogFormInit = async () => (converterArgs ? await initCurrentModel() : await setDefaultValues());

  return {
    settings,
    relatedEntities,
    mappingEntityTypes,
    selectedFieldTypeIsLocaleString,
    maxRelationEntityTypeLevels,
    currentModel,
    currentFieldTypes,
    isAddButtonVisible,
    isRemoveButtonVisible,
    selectedEntityTypeId: lastSelectedEntityTypeId,
    onMainEntityTypeChange,
    onDialogFormInit,
    initCurrentModel,
    getRelatedEntityFieldFunctionSettings,
    getRelation,
    onRelationChange,
    onEntityTypeChange,
    getRelativesByIndex,
    addRelation,
    removeRelation,
    isLoading,
  };
}
