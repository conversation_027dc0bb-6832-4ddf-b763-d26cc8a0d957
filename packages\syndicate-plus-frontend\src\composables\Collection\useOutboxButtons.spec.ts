import { beforeEach, describe, expect, it, vi, vitest } from 'vitest';
import { ref } from 'vue';
import { OutboxStatus, SquareColor, SyndicationPageTabNames } from '@enums';
import useOutboxButtons from '@composables/Collection/useOutboxButtons';
import { ProductOutboxRow } from '@customTypes';
import outboxHttpService from '@httpservices/Outbox/OutboxHttpService';
import { notify } from '@inriver/inri';

vitest.mock('@httpservices/Outbox/OutboxHttpService');

describe('useOutboxButtons', () => {
  const inProgressOutboxRow = {
    id: 1,
    catalogExportId: 1,
    status: {
      outboxStatus: OutboxStatus.IN_PROGRESS,
      squareColor: SquareColor.PURPLE,
    },
  } as ProductOutboxRow;

  beforeEach(() => {
    const notifyError = vi.spyOn(notify, 'error');
    notifyError.mockImplementation(() => vi.fn());
    const notifySuccess = vi.spyOn(notify, 'success');
    notifySuccess.mockImplementation(() => vi.fn());
  });
  describe('showCancelJob', () => {
    it('should return false if selectedOutboxRow is undefined', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Outbox);
      const selectedOutboxRow = ref<ProductOutboxRow | undefined>(undefined);
      const { isCancelOutboxJobButtonVisible } = useOutboxButtons(tab, selectedOutboxRow);

      expect(isCancelOutboxJobButtonVisible.value).toBe(false);
    });

    it('should return false if tab is not Outbox', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Collection);
      const selectedOutboxRow = ref<ProductOutboxRow | undefined>({
        status: {
          outboxStatus: OutboxStatus.IN_PROGRESS,
          squareColor: SquareColor.PURPLE,
        },
      } as ProductOutboxRow);
      const { isCancelOutboxJobButtonVisible } = useOutboxButtons(tab, selectedOutboxRow);

      expect(isCancelOutboxJobButtonVisible.value).toBe(false);
    });

    it('should return true if selectedOutboxRow status is InProgress', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Outbox);
      const selectedOutboxRow = ref<ProductOutboxRow | undefined>({
        status: {
          outboxStatus: OutboxStatus.IN_PROGRESS,
          squareColor: SquareColor.PURPLE,
        },
      } as ProductOutboxRow);
      const { isCancelOutboxJobButtonVisible } = useOutboxButtons(tab, selectedOutboxRow);

      expect(isCancelOutboxJobButtonVisible.value).toBe(true);
    });

    it('should return true if selectedOutboxRow status is InQueue', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Outbox);
      const selectedOutboxRow = ref<ProductOutboxRow | undefined>({
        status: {
          outboxStatus: OutboxStatus.IN_QUEUE,
          squareColor: SquareColor.PURPLE,
        },
      } as ProductOutboxRow);
      const { isCancelOutboxJobButtonVisible } = useOutboxButtons(tab, selectedOutboxRow);

      expect(isCancelOutboxJobButtonVisible.value).toBe(true);
    });
  });

  describe('cancelJob', () => {
    it('should call cancelJob', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Outbox);
      const selectedOutboxRow = ref<ProductOutboxRow | undefined>(inProgressOutboxRow);
      const { cancelJob } = useOutboxButtons(tab, selectedOutboxRow);

      // Act
      cancelJob();

      // Assert
      expect(outboxHttpService.cancelJob).toHaveBeenCalledWith(1);
    });
  });
});
