:root {
  --color-text: #2d2d2c;
  --color-grey: #ccc;
  --color-grey-dark: #888;
}

body {
  min-width: 100px;
  min-height: 100%;
  font-family: var(--font-family);
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  font-size: 14px;
  color: var(--color-text);
  font-variant-ligatures: none;
}

a {
  text-decoration: none;
  color: var(--color-text);
}

p {
  margin: 0 0 16px;
}

.flex {
  display: flex;
  flex-wrap: wrap;
}

.export-container {
  gap: 24px;
}

.c-product-card {
  position: relative;
  width: 193px;
}

.c-product-card .content {
  width: 193px;
  height: 193px;
}

.c-product-card .content img {
  width: inherit;
  height: inherit;
  object-fit: contain;
}

.c-product-card .description {
  color: var(--color-grey-dark);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 193px;
}

.c-product-card .text {
  width: 193px;
  margin-top: 4px;
}

.c-product-card .brand-name {
  color: var(--color-grey-dark);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 193px;
  margin-bottom: 0px;
}

.card-badge-container {
  display: none;
}
