import { describe, it, expect, afterEach, beforeEach, Mock, vi } from 'vitest';
import { isSyndicatePlusWithCoreEnabled } from './moduleEnabledService';
import isFeatureEnabled from '@utils/isFeatureEnabled';

vi.mock('@utils/isFeatureEnabled');

describe('isSyndicatePlusWithCoreEnabled', () => {
  let originalAppData = undefined;
  (isFeatureEnabled as Mock).mockReturnValue(false);

  beforeEach(() => {
    originalAppData = window.appData;
  });

  afterEach(() => {
    window.appData = originalAppData;
  });

  it('returns false if isSyndicatePlusWithCoreEnabled is false', () => {
    window.appData = { isSyndicatePlusWithCoreEnabled: false };
    expect(isSyndicatePlusWithCoreEnabled()).toBe(false);
  });

  it("returns false if isSyndicatePlusWithCoreEnabled is 'false'", () => {
    window.appData = { isSyndicatePlusWithCoreEnabled: 'false' };
    expect(isSyndicatePlusWithCoreEnabled()).toBe(false);
  });

  it('returns false if isSyndicatePlusWithCoreEnabled is undefined', () => {
    window.appData = {};
    expect(isSyndicatePlusWithCoreEnabled()).toBe(false);
  });
});
