<template>
  <div v-if="functionToDisplay" class="c-field-mapping-function">
    <div v-if="functionToDisplay.name === FunctionType.CONCATCASE">
      <q-card-section :horizontal="true">
        <div class="flex-container">
          <div v-for="(field, index) in functionToDisplay.fields" :key="index">
            <q-card dense>
              <div class="color-custom-card function-name ml-1">
                {{ mapFieldNameToLabel(field.name) }}
              </div>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </div>
    <div v-else-if="functionToDisplay.name != FunctionType.IF_THEN">
      <q-card-section :horizontal="true">
        <div class="flex-container">
          <q-card dense>
            <div class="color-custom-card function-name mr-2">
              {{ functionToDisplay.name.toLowerCase() }}
            </div>
          </q-card>
        </div>
        <div v-for="(field, index) in functionToDisplay.fields" :key="index" class="flex-container">
          <q-card dense>
            <div v-if="field.isCustom" class="color-custom-card">
              {{ correctDisplayOfSpecialCustomSigns(field.name) }}
            </div>
            <div v-else class="color-card">
              {{ mapFieldNameToLabel(field.name) }}
            </div>
          </q-card>
          <span v-if="index < functionToDisplay.fields.length - 1" class="plus-sign">+</span>
        </div>
      </q-card-section>
    </div>
    <div v-else>
      <q-card-section :horizontal="true">
        <div class="flex-container">
          <q-card dense>
            <div class="color-custom-card function-name mr-2">
              {{ functionToDisplay.name.toLowerCase() }}
            </div>
          </q-card>
        </div>
        <div class="text-in-function">If</div>
        <q-card dense>
          <div v-if="functionToDisplay.GetLeftCondition().isCustom" class="color-custom-card">
            {{ correctDisplayOfSpecialCustomSigns(functionToDisplay.GetLeftCondition().name) }}
          </div>
          <div v-else class="color-card">
            {{ mapFieldNameToLabel(functionToDisplay.GetLeftCondition().name) }}
          </div>
        </q-card>
        <div class="text-in-function">==</div>
        <q-card dense>
          <div v-if="functionToDisplay.GetRightCondition().isCustom" class="color-custom-card">
            {{ correctDisplayOfSpecialCustomSigns(functionToDisplay.GetRightCondition().name) }}
          </div>
          <div v-else class="color-card">
            {{ mapFieldNameToLabel(functionToDisplay.GetRightCondition().name) }}
          </div>
        </q-card>
        <div class="text-in-function">then</div>
        <q-card dense>
          <div v-if="functionToDisplay.GetLeftOutcome().isCustom" class="color-custom-card">
            {{ correctDisplayOfSpecialCustomSigns(functionToDisplay.GetLeftOutcome().name) }}
          </div>
          <div v-else class="color-card">
            {{ mapFieldNameToLabel(functionToDisplay.GetLeftOutcome().name) }}
          </div>
        </q-card>
        <div class="text-in-function">else</div>
        <q-card dense>
          <div v-if="functionToDisplay.GetRightOutcome().isCustom" class="color-custom-card">
            {{ correctDisplayOfSpecialCustomSigns(functionToDisplay.GetRightOutcome().name) }}
          </div>
          <div v-else class="color-card">
            {{ mapFieldNameToLabel(functionToDisplay.GetRightOutcome().name) }}
          </div>
        </q-card>
      </q-card-section>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onBeforeMount, ref } from 'vue';
import FunctionParser from '@services/helper/FunctionParser';
import { FunctionType } from '@enums/FunctionType';
import { useSuggestionStore } from '@stores/SuggestionStore';
import { SuggestionTypeEnum } from '@enums/SuggestionTypeEnum';
import { useRoute } from 'vue-router';
import { Field } from '@customTypes/field';

const route = useRoute();
let fieldsMapping: Field[];
const { fetchSuggestions } = useSuggestionStore();

const props = defineProps({
  func: {
    type: String,
    default: '',
  },
});
const functionToDisplay = ref<any>();

onBeforeMount(async () => {
  const destinationId = parseInt(route.params.destinationId as string);
  fieldsMapping = await fetchSuggestions(destinationId, SuggestionTypeEnum.TemplateInternalFields);
  functionToDisplay.value = FunctionParser.parseToConcreteFunction(props.func);
});

function correctDisplayOfSpecialCustomSigns(sign: string) {
  if (sign === ' ') {
    return '[space]';
  }
  if (sign === '') {
    return '[NONE]';
  }

  return sign;
}

function mapFieldNameToLabel(fieldName: string) {
  const mapping = fieldsMapping.filter((c) => c.id == fieldName);
  if (mapping.length > 0) {
    return mapping[0].name;
  }

  return fieldName;
}
</script>

<style lang="scss" scoped>
.c-field-mapping-function {
  font-size: 10px;
  padding: 0px;

  .q-card {
    border-radius: 30px;
    box-shadow: none;
  }
}

.c-field-mapping-function .q-card > div:last-child,
.q-card > img:last-child {
  border-radius: 30px;
  border: 1.5px solid var(--color-grey-dark);
  padding: 4px;
  box-shadow: none;
}

.color-card,
.color-custom-card {
  background-color: var(--color-primary);
  height: 20px;
  line-height: 11px;

  &.function-name {
    background-color: var(--color-grey-lighter);
  }
}

.plus-sign {
  margin: 5px 5px;
}

.flex-container {
  display: flex;
  align-items: center;
}

.text-in-function {
  margin: auto 8px;
}
</style>
