import { JobState } from '@core/enums';

export interface DataSubmissionResponse {
  dataSubmissions: DataSubmission[];
  totalCount: number;
  pageSize: number;
  page: number;
}

export interface DataSubmission {
  jobId: number;
  environmentGid: string;
  tradingPartnerId: string;
  state: JobState;
  id: number;
  createdDate: string;
  updatedDate: string;
}

export interface DataSubmissionEntitiesResponse {
  entities: DataSubmissionEntity[];
  totalCount: number;
  pageSize: number;
  page: number;
}

export interface DataSubmissionEntity {
  id: number;
  createdDate: string;
  updatedDate: string;
  dataSubmissionId: number;
  entityId: number;
  correlationId: string;
  state: JobState;
  apiFailureResponse?: string;
}
