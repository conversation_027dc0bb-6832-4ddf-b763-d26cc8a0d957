import { describe, it, expect } from 'vitest';
import { PreflightColumnDto } from '@dtos';
import { BaseStrategy } from '@composables/Preflight/StopCellEditStrategies';

class ConcreteStrategy extends BaseStrategy {
  constructor(rows, columns) {
    super(rows, columns);
  }

  public getNextAvailableRowIdFake = (currentRowId: number) => this.getNextAvailableRowId(currentRowId);
  public getPreviousAvailableRowIdFake = (currentRowId: number) => this.getPreviousAvailableRowId(currentRowId);
  public getNextAvailableColumnIdFake = (currentColumnId: number) => this.getNextAvailableColumnId(currentColumnId);
  public getPreviousAvailableColumnIdFake = (currentColumnId: number) =>
    this.getPreviousAvailableColumnId(currentColumnId);
  public getFirstAvailableColumnIdFake = () => this.getFirstAvailableColumnId();
  public getLastAvailableColumnIdFake = () => this.getLastAvailableColumnId();
}

describe('BaseStrategy', () => {
  describe('getFirstAvailableColumnId', () => {
    it('returns first available column id', async () => {
      // Arrange
      const rows = [];
      const columns = [
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ];
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const columnId = strategy.getFirstAvailableColumnIdFake();

      // Assert
      expect(columnId).toBe(2);
    });
  });

  describe('getLastAvailableColumnId', () => {
    it('returns last available column id', async () => {
      // Arrange
      const rows = [];
      const columns = [
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ];
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const columnId = strategy.getLastAvailableColumnIdFake();

      // Assert
      expect(columnId).toBe(4);
    });
  });

  describe('getNextAvailableRowId', () => {
    it('returns next available row id', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      rows.length = 5;
      const currentRowId = 0;
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const rowId = strategy.getNextAvailableRowIdFake(currentRowId);

      // Assert
      expect(rowId).toBe(currentRowId + 1);
    });

    it('returns first row id for the last row', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      rows.length = 5;
      const currentRowId = 4;
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const rowId = strategy.getNextAvailableRowIdFake(currentRowId);

      // Assert
      expect(rowId).toBe(0);
    });
  });

  describe('getPreviousAvailableRowId', () => {
    it('returns previous available row id', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 4;
      const strategy = new ConcreteStrategy(rows, columns);
      rows.length = 5;

      // Act
      const rowId = strategy.getPreviousAvailableRowIdFake(currentRowId);

      // Assert
      expect(rowId).toBe(currentRowId - 1);
    });

    it('returns last row id for the first row', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 0;
      const strategy = new ConcreteStrategy(rows, columns);
      rows.length = 5;

      // Act
      const rowId = strategy.getPreviousAvailableRowIdFake(currentRowId);

      // Assert
      expect(rowId).toBe(4);
    });
  });

  describe('getNextAvailableColumnId', () => {
    it('returns next available column id', async () => {
      // Arrange
      const rows = [];
      const columns = [
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ];
      const currentColumnId = 2;
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const columnId = strategy.getNextAvailableColumnIdFake(currentColumnId);

      // Assert
      expect(columnId).toBe(4);
    });

    it('returns -1 for last column', async () => {
      // Arrange
      const rows = [];
      const columns = [
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ];
      const currentColumnId = 4;
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const columnId = strategy.getNextAvailableColumnIdFake(currentColumnId);

      // Assert
      expect(columnId).toBe(-1);
    });
  });

  describe('getPreviousAvailableColumnIdFake', () => {
    it('returns previous available column id', async () => {
      // Arrange
      const rows = [];
      const columns = [
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ];
      const currentColumnId = 4;
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const columnId = strategy.getPreviousAvailableColumnIdFake(currentColumnId);

      // Assert
      expect(columnId).toBe(2);
    });

    it('returns -1 for last column', async () => {
      // Arrange
      const rows = [];
      const columns = [
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ];
      const currentColumnId = 2;
      const strategy = new ConcreteStrategy(rows, columns);

      // Act
      const columnId = strategy.getPreviousAvailableColumnIdFake(currentColumnId);

      // Assert
      expect(columnId).toBe(-1);
    });
  });
});
