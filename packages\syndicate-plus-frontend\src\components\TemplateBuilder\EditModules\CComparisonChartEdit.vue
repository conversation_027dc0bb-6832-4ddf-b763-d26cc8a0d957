<template>
  <div class="mx-auto p-20px min-w-1384px w-1384px">
    <div class="flex flex-row flex-nowrap justify-end">
      <div
        v-for="orderIndex in numberOfBlocks"
        :key="orderIndex"
        class="module-block text-wrap-word-break flex flex-col m-10px"
      >
        <c-caption-angle-image-block
          v-model="module.data[`image${orderIndex}`]"
          :dimensions="dimensions"
          :hide-caption="true"
          :max-alt-text-length="Validation.comparisonChart.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url, orderIndex)"
        />
        <div class="flex flex-col self-center">
          <q-input
            v-model="module.data[`title${orderIndex}`]"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.title_label')"
            :maxlength="Validation.comparisonChart.title.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data[`title${orderIndex}`], 'title', orderIndex)"
          />
          <q-input
            v-model="module.data[`asin${orderIndex}`]"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.asin')"
            :maxlength="Validation.comparisonChart.asin.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data[`asin${orderIndex}`], 'asin', orderIndex)"
          />
        </div>
        <q-checkbox
          v-bind="$inri.checkbox"
          :key="index"
          v-model="module.data[`highlighted${orderIndex}`].checked"
          :data-testid="module.data[`highlighted${orderIndex}`]"
          :label="$t('syndicate_plus.aplus_define_template.highlighted')"
          @click="updateModel"
        />
      </div>
    </div>
    <c-metric-list
      :model-value="module.data.metrics.metrics"
      :max-metric-length="Validation.comparisonChart.metricText.maxLength"
      :max-metric-value-length="Validation.comparisonChart.metricText.maxLength"
      @update-model="updateModel"
    />
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onUpdated } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { CMetricList, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { useComparisonChart } from '@composables/AplusTemplateBuilder';
import { Dimension } from '@customTypes/Aplus';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Variables
const numberOfBlocks = 6;
const dimensions = { width: 150, height: 300 } as Dimension;

// Composables
const { module, initData } = useComparisonChart();

// Functions
const handleUrlUpdate = (url: string, orderIndex: number) => {
  module.value.data[`image${orderIndex}`].angle = url;
  updateModel();
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, key: string, orderIndex: number) => {
  module.value.data[key + orderIndex] = fieldsStore.onDrop(value);
  updateModel();
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  initData();
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>

<style lang="scss" scoped>
.module-block {
  min-width: 150px;
}

:deep(.q-field.q-field--outlined.q-input) {
  width: 140px;
}

:deep(.c-inri-input.c-inri-input--default:not(.q-textarea) .q-field__control) {
  width: 140px;
  min-width: 140px;
}

:deep(.q-uploader__list) {
  overflow: hidden;
}

:deep(.q-field__inner .q-field__bottom) {
  margin-right: 0px;
}
</style>
