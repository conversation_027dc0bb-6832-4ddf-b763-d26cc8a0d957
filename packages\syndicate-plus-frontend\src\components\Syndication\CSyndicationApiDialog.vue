<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog"
    :title="$t('syndicate_plus.syndication.api_syndication_dialog_title')"
    :disable-confirm="confirmButtonIsDisabled"
    :disabled-confirm-tooltip="disabledConfirmTooltip"
    :confirm-button-text="confirmButtonText"
    @cancel="cancel"
    @confirm="logEventAndClick(ActionName.CONFIRM_API_SYNDICATION, performSyndication)"
  >
    <c-select
      v-model="selectedApiTemplate"
      class="w-1/2"
      :options="templates"
      :placeholder="$t('syndicate_plus.syndication.api_syndication_select_template_placeholder')"
      option-label="name"
      option-value="id"
      dense
      clearable
    />

    <q-checkbox
      v-model="isScheduled"
      v-bind="$inri.checkbox"
      :label="$t('syndicate_plus.syndication.api_syndication.schedule')"
      class="pt-2"
    />
    <div v-if="isScheduled" class="w-1/2 pt-2">
      <q-input
        v-model="scheduleName"
        v-bind="$inri.input"
        :label="$t('syndicate_plus.syndication.api_syndication.schedule_name')"
        hide-bottom-space
      />
      <div class="pt-2">
        <q-checkbox
          v-bind="$inri.checkbox"
          v-model="isSendAllProducts"
          :label="$t('syndicate_plus.syndication.api_syndication.send_all_products')"
        />
      </div>
      <div class="frequency">
        <q-radio
          v-for="frequencyType in allFrequencyTypes"
          :key="frequencyType"
          v-model="selectedFrequency"
          :val="frequencyType"
          :label="frequencyType.toLocaleLowerCase()"
          class="pt-2"
        />
      </div>
      <q-input
        v-if="isStartDateVisible"
        v-model="startDate"
        filled
        :mask="inputMask"
        :label="$t('syndicate_plus.syndication.api_syndication.start_date')"
        class="pt-2"
      >
        <template #prepend>
          <q-icon name="mdi-calendar-outline" class="cursor-pointer">
            <q-popup-proxy transition-show="scale" transition-hide="scale">
              <div class="q-pa-md">
                <div class="q-gutter-md row items-start">
                  <q-date v-model="startDate" :mask="dateTimeMask" />
                  <q-time v-model="startDate" :mask="dateTimeMask" now-btn />
                </div>
              </div>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
      <q-input
        v-if="isEndDateVisible"
        v-model="endDate"
        filled
        :mask="inputMask"
        :label="$t('syndicate_plus.syndication.api_syndication.end_date')"
      >
        <template #prepend>
          <q-icon name="mdi-calendar-outline" class="cursor-pointer">
            <q-popup-proxy transition-show="scale" transition-hide="scale">
              <div class="q-pa-md">
                <div class="q-gutter-md row items-start">
                  <q-date v-model="endDate" :mask="dateTimeMask" />
                  <q-time v-model="endDate" :mask="dateTimeMask" now-btn />
                </div>
              </div>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
      <q-btn-group v-if="isDaysVisible" class="days" spread>
        <c-btn
          v-for="day in allDays"
          :key="day"
          :label="day.slice(0, 2).toLocaleLowerCase()"
          :color="selectedDays.includes(day) ? 'primary' : 'secondary'"
          @click="() => onDayClick(day)"
        />
      </q-btn-group>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { Field, ApiSyndicationSettings } from '@customTypes';
import { ActionName, allDays, allFrequencyTypes } from '@enums';
import { computed, onBeforeMount, ref } from 'vue';
import { useAppInsightsStore } from '@stores';
import { useSyndicateViaApiSettings } from '@composables';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  templates: {
    type: Array<Field>,
    default: [] as Field[],
  },
});
const emit = defineEmits(['on-confirm']);

const { t } = useI18n && useI18n();

// Refs
const selectedApiTemplate = ref<Field | undefined>();

// Composables
const { showDialog, cancel } = useDialog();
const { logEventAndClick } = useAppInsightsStore();
const {
  disabledConfirmTooltip,
  dateTimeMask,
  inputMask,
  isScheduled,
  confirmButtonIsDisabled,
  isDaysVisible,
  isEndDateVisible,
  isStartDateVisible,
  endDate,
  scheduleName,
  isSendAllProducts,
  selectedFrequency,
  startDate,
  selectedDays,
  onDayClick,
  createSettingsModel,
} = useSyndicateViaApiSettings(selectedApiTemplate, useI18n);

// Computed
const confirmButtonText = computed(() =>
  isScheduled.value
    ? t('syndicate_plus.syndication.api_syndication.confirm_schedule')
    : t('syndicate_plus.common.confirm')
);

// Functions
const performSyndication = () => {
  const settings = {
    selectedTemplate: selectedApiTemplate.value,
    isScheduled: isScheduled.value,
    scheduledSyndicationSettings: createSettingsModel(),
  } as ApiSyndicationSettings;
  emit('on-confirm', settings);
};

// Lifecycle methods
onBeforeMount(() => {
  if (props.templates.length === 1) {
    selectedApiTemplate.value = props.templates[0];
  }
});
</script>

<style scoped lang="scss">
.frequency {
  margin-left: -9px;
}

.action-buttons {
  padding-top: 10px;
}

:deep(.q-field__bottom) {
  display: none;
}

.days {
  :deep(.c-btn) {
    padding: 4px 20px !important;
  }
}
</style>
