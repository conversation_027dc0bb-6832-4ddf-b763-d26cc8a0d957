variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'euw'
  stackType: 'dev3a'

  # Deploy dependencies
  dependsOn: 'Build_euwDev3a'
  dependsOnSecond: 'Build_euwDev3a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-dev3a-euw.productmarketingcloud.com'

  auth0Domain: 'auth-dev.syndic8.io'
  auth0ClientId: 'twD9zFV3gROvoMmRyVDq2kU6fcjZsATW'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: 'https://api.infrared.inriver.syndic8.io'
  fileServerUrl: 'https://app.infrared.inriver.syndic8.io'
  requestTradingPartnerUrl: 'https://community.inriver.com/hc/en-us/requests/new?ticket_form_id=8783121346204'

  testLocalUrl: 'https://test.productmarketingcloud.com/'
  testUserName: <EMAIL>
  testUserPassword: Password123$
  testPortalUrl: https://portal-dev4a-euw.productmarketingcloud.com/

  allowMissingOrgMapping: true

  # Application insights instrumentation key
  instrumentationKey: 'e436f52f-29b0-4281-a9de-b6f27b98c887'
