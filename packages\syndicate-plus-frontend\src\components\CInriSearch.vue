<template>
  <q-input
    v-bind="dense ? $inri.inputDense : $inri.input"
    :model-value="modelValue"
    :label="label"
    :placeholder="placeholder"
    @update:model-value="$emit('update:model-value', $event)"
  >
    <template #append>
      <q-icon
        v-if="modelValue?.length"
        name="mdi-close"
        color="grey-darker"
        class="cursor-pointer icon-close"
        size="18px"
        @click.stop="$emit('update:model-value', '')"
      />
      <q-icon
        id="product-search"
        :class="modelValue?.length ? 'cursor-pointer' : ''"
        name="inri-search"
        color="grey-dark"
        size="18px"
        @click="modelValue?.length && $emit('search')"
      />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import type { QInputProps } from 'quasar';

defineEmits(['update:model-value', 'search']);

interface Props extends QInputProps {
  modelValue: string;
  label: string;
  placeholder?: string;
  dense?: boolean;
}

withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  label: undefined,
  placeholder: undefined,
});
</script>
<style lang="scss" scoped>
#product-search {
  margin-left: 6px;
}

.icon-close {
  margin-right: 6px;
}
</style>
