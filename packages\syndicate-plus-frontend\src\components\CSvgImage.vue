<script setup lang="ts">
import { defineAsyncComponent, computed } from 'vue';

const props = defineProps({
  image: {
    type: String,
    default: '',
  },
});

function getIconComponent(image: string) {
  return defineAsyncComponent(() => {
    return import(`../images/${image}.svg?component`);
  });
}

const imageComponent = computed(() => getIconComponent(props.image));
</script>

<template>
  <component :is="imageComponent" />
</template>
