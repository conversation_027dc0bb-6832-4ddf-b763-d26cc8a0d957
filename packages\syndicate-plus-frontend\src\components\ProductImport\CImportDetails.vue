<template>
  <c-layout-with-back-button :title="title">
    <c-section>
      <q-table
        v-model:pagination="pagination"
        row-key="startDate"
        separator="cell"
        dense
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        :columns="infoColumns"
        class="hide-checkboxes mb-20px"
        :rows="importDetails?.details ? [importDetails?.details] : []"
        :loading="isFetching"
        hide-bottom
      >
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
      </q-table>
      <q-table
        v-if="hasErrors"
        class="import-details-table sticky-table-header"
        :pagination="pagination"
        dense
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        :columns="errorTableColumns"
        :rows="importDetails?.errors"
        row-key="upc"
        :rows-per-page-options="[0]"
        :loading="isFetching"
        separator="cell"
        hide-bottom
      >
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
      </q-table>
    </c-section>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppInsightsStore } from '@stores';
import { PageName } from '@enums';
import { errorTableColumns, infoColumns } from '@const';
import { useRoute } from 'vue-router';
import ImportService from '@services/ImportService';
import { ImportDetails } from '@customTypes/ImportData';

// Composables
const route = useRoute();

const { setScreenName } = useAppInsightsStore();
const { t } = useI18n && useI18n();

// Variables
const pagination = {
  page: 1,
  rowsPerPage: 0,
};

// Refs
const importDetails = ref<ImportDetails>();
const isFetching = ref<boolean>(false);

// Computed
const title = computed(() => `${t('syndicate_plus.imports')} - ${importDetails.value?.fileName}`);
const hasErrors = computed(() => !!importDetails.value?.errors?.length);

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.IMPORT_DETAILS_PAGE);
  const importId = parseInt(route.params?.id as string);
  if (!importId) {
    return;
  }

  try {
    isFetching.value = true;
    importDetails.value = await ImportService.getImportById(importId);
  } finally {
    isFetching.value = false;
  }
});
</script>

<style lang="scss" scoped>
.import-details-table {
  max-height: calc(100vh - 263px);
  margin-bottom: 200px;
}
</style>
