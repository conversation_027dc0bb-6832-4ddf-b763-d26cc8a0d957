<template>
  <c-mapping-dialog
    v-if="showMappingDialog"
    v-model:show="showMappingDialog"
    :title="selectedColumnName"
    :is-loading="columnMappingsAreLoading"
    :outbound="selectedTemplateFieldOutbound?.toOutboundTemplateField()"
    :inbounds="selectedTemplateFieldsInbound?.map((f) => f.toInboundTemplateField(coreFieldMatcher)) ?? []"
  />
  <q-tr>
    <q-th v-for="col in cols" :key="col.name" :class="col.isExtraColumn ? 'sticky extra-column-header' : ''">
      <div class="header-cell">
        <div v-if="col.label">
          <span>{{ col.label.toLocaleLowerCase() }}</span>
          <q-icon
            v-if="!col.isExtraColumn"
            name="mdi-dots-vertical"
            size="xs"
            class="action-icon"
            @click="openMappingDialog(col)"
          >
            <q-tooltip>{{ $t('syndicate_plus.syndication.mappings') }}</q-tooltip>
          </q-icon>
        </div>
      </div>
    </q-th>
  </q-tr>
</template>

<script setup lang="ts">
import { ref, PropType, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';
import { CMappingDialog } from '@components/FieldMapping';
import { useTemplatesStore, useAdapterSettingsStore } from '@stores';
import { Field } from '@customTypes';
import { useRouter } from '@composables/useRouter';
import { CoreFieldMatcher } from '@utils';

const { route } = useRouter();

const props = defineProps({
  cols: {
    type: Array<any>,
    default: [],
  },
  selectTemplateOption: {
    type: Object as PropType<Field>,
    default: {} as Field,
  },
});

const adapterSettingsStore = useAdapterSettingsStore();
const templatesStore = useTemplatesStore();

// Variables
const destinationId = parseInt(route.params.destinationId as string);
let coreFieldMatcher: CoreFieldMatcher | undefined;

// Refs
const showMappingDialog = ref<boolean>(false);
const selectedColumnName = ref<string>('');
const columnMappingsAreLoading = ref<boolean>(false);
const { coreFieldMappingSetting } = storeToRefs(adapterSettingsStore);
const { selectedTemplateFieldsInbound, selectedTemplateFieldOutbound } = storeToRefs(templatesStore);

// Functions
const openMappingDialog = async (column) => {
  showMappingDialog.value = !showMappingDialog.value;
  columnMappingsAreLoading.value = true;
  try {
    await templatesStore.ensureTemplates(props.selectTemplateOption.id, destinationId);
  } finally {
    columnMappingsAreLoading.value = false;
  }

  if (!showMappingDialog.value || !props.selectTemplateOption) {
    return;
  }

  selectedTemplateFieldsInbound.value = [];
  selectedColumnName.value = column.label;

  templatesStore.setOutboundAndInboundTemplateFields(column);
};

// Lifecycle methods
onBeforeMount(async () => {
  await adapterSettingsStore.fetchCoreFieldMapping();
  coreFieldMatcher = new CoreFieldMatcher(coreFieldMappingSetting.value?.CoreFieldMapping);
});
</script>

<style scoped style="scss">
th {
  background-color: var(--color-grey-lighter);
  padding-right: 0px;
}

th:nth-child(1),
th:nth-child(2) {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

th:nth-child(1) {
  left: 0;
  min-width: 150px;
  max-width: 150px;
}

th:nth-child(2) {
  left: 150px;
  min-width: 150px;
  max-width: 150px;
}

.header-cell {
  display: flex;
  flex-flow: row;
  align-items: center;

  span {
    padding-right: 20px;
  }

  .action-icon {
    position: absolute;
    right: 10px;
    top: 7px;
    color: var(--color-grey);
    margin-left: auto;
    cursor: pointer;
    width: 5px;

    .q-icon {
      font-size: 12px;
    }
  }
}

.sticky {
  position: -webkit-sticky;
  position: sticky;

  &.extra-column-header {
    background-color: var(--color-green-10) !important;
    z-index: 2;
  }

  &.extra-column-cell {
    background-color: #fafafa;
    z-index: 1;
  }
}
</style>
