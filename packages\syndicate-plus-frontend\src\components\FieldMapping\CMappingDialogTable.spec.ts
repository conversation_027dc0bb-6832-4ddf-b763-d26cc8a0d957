import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';
import { createI18n } from 'vue-i18n';

import CMappingDialogTable from '@components/FieldMapping/CMappingDialogTable.vue';
import CMappingLength from '@components/FieldMapping/CMappingLength.vue';
import CFieldMappingFunction from '@components/FieldMapping/CFieldMappingFunction.vue';
import { QIcon, QTable, QTd } from 'quasar';
import CTableActions from '@inriver/inri/src/components/CTableActions.vue';
import { ImportanceType } from '@enums/ImportanceType';
import { InboundTemplateField } from '@customTypes';

describe('CMappingDialogTable', () => {
  it('displays data in the inbound table', async () => {
    // Arrange
    const testMapping = getFieldMappingModel<InboundTemplateField>();
    const wrapper = mountCMappingDialogTable();
    await wrapper.setProps({
      isInbound: true,
      rows: [testMapping],
    });

    // Act
    const tbodyText = wrapper.find('tbody')?.text();

    // Assert
    expect(tbodyText).toContain(testMapping.sourceFieldName);
    expect(tbodyText).toContain(testMapping.syndicationFieldName);
    expect(tbodyText).toContain(testMapping.defaultValue);
    expect(tbodyText).toContain(testMapping.dataType);
    expect(tbodyText).toContain(testMapping.format);
  });

  it('should display data in the inbound table if fieldId is missing', async () => {
    // Arrange
    const testMapping = getFieldMappingModel<InboundTemplateField>();
    testMapping.sourceFieldId = '';
    const wrapper = mountCMappingDialogTable();
    await wrapper.setProps({
      isInbound: true,
      rows: [testMapping],
    });

    // Act
    const tbodyText = wrapper.find('tbody')?.text();

    // Assert
    expect(tbodyText).toContain(testMapping.sourceFieldName);
    expect(tbodyText).toContain(testMapping.syndicationFieldName);
    expect(tbodyText).toContain(testMapping.defaultValue);
    expect(tbodyText).toContain(testMapping.dataType);
    expect(tbodyText).toContain(testMapping.format);
  });
});

const mountCMappingDialogTable = () => {
  const i18n = createI18n({
    allowComposition: true,
  });
  const wrapper = mount(CMappingDialogTable, {
    global: {
      components: {
        'c-mapping-length': CMappingLength,
        'c-field-mapping-function': CFieldMappingFunction,
        'c-table-actions': CTableActions,
        'q-icon': QIcon,
        'q-td': QTd,
        'q-table': QTable,
      },
      plugins: [i18n],
    },
  });

  return wrapper;
};

const getFieldMappingModel = <T>(): T => {
  return {
    sourceFieldId: 'testFieldId',
    sourceFieldName: 'testSourceFieldName',
    syndicationFieldName: 'testSyndicationFieldName',
    targetFieldName: 'testTargetFieldName',
    defaultValue: 'testDefaultValue',
    functions: [],
    importanceType: ImportanceType.CONDITIONAL,
    dataType: 'string',
    format: 'testFformat',
    minCharacterLength: 0,
    maxCharacterLength: 20,
    parentOverrideField: false,
    sheetName: '',
    status: true,
    suggestedMatch: true,
  } as T;
};
