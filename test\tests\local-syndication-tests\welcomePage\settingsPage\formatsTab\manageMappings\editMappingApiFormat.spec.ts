import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';
import { ManageMappingsPage } from '@pages/welcomePage/welcomeSettings/formatsTab/manageMappings/manageMappings.page';
import { EditMappingPage } from '@pages/welcomePage/welcomeSettings/formatsTab/manageMappings/editMapping.page';

test.describe('Edit mapping API format', () => {
  const bestBuySpeakersFormat = 'best-buy - Speakers';
  const UITestProductFormatMapping = 'UiTest - mappings for best-buy - Speakers';
  const targetFeatureBullets = 'featureBullets';
  const targetFeatureBullets1Title = 'featureBullets_1_title';
  const targetFeatureBullets1Description = 'featureBullets_1_description';
  const targetFeatureBullets2Title = 'featureBullets_2_title';
  const targetFeatureBullets2Description = 'featureBullets_2_description';
  const targetProductDisclaimers = 'productDisclaimers';
  const targetProductDisclaimers1 = 'productDisclaimers_1';
  const targetProductDisclaimers2 = 'productDisclaimers_2';
  const targetSpeakerType = 'speakerType';
  const targetSpeakerType1 = 'speakerType_1';
  const targetSpeakerType2 = 'speakerType_2';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatsTabPage: FormatsTabPage;
  let mappingsPage: ManageMappingsPage;
  let editMappingPage: EditMappingPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatsTabPage = new FormatsTabPage(localPage);
    mappingsPage = new ManageMappingsPage(localPage);
    editMappingPage = new EditMappingPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatsTabPage.formatsTable, 'Formats table is not visible').toBeVisible();
    await formatsTabPage.selectFormat(bestBuySpeakersFormat).click();
    await formatsTabPage.manageMappingsButton.click();
  });

  test('Object list add and remove item. @notForPr', async () => {
    await mappingsPage.mappingName(UITestProductFormatMapping).dblclick();
    await expect(editMappingPage.target(targetFeatureBullets), 'Target field is not visible').toBeVisible();
    await editMappingPage.target(targetFeatureBullets).click();
    await expect(editMappingPage.addListItemButton, 'Add list item button is not visible').toBeVisible();
    await editMappingPage.addListItemButton.click();
    await expect(editMappingPage.target(targetFeatureBullets1Title), 'Target field is not visible').toBeVisible();
    await expect(editMappingPage.target(targetFeatureBullets1Description), 'Target field is not visible').toBeVisible();
    await editMappingPage.addListItemButton.click();
    await expect(editMappingPage.target(targetFeatureBullets2Title), 'Target field is not visible').toBeVisible();
    await expect(editMappingPage.target(targetFeatureBullets2Description), 'Target field is not visible').toBeVisible();
    await editMappingPage.removeListItemButton.click();
    await editMappingPage.removeListItemButton.click();
    await expect(editMappingPage.target(targetFeatureBullets1Title), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.target(targetFeatureBullets1Description), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.target(targetFeatureBullets2Title), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.removeListItemButton, 'Remove list item button is visible').toBeHidden();
  });

  test('String list add and remove item. @notForPr', async () => {
    await mappingsPage.mappingName(UITestProductFormatMapping).dblclick();
    await expect(editMappingPage.target(targetProductDisclaimers), 'Target field is not visible').toBeVisible();
    await editMappingPage.target(targetProductDisclaimers).click();
    await expect(editMappingPage.addListItemButton, 'Add list item button is not visible').toBeVisible();
    await editMappingPage.addListItemButton.click();
    await editMappingPage.addListItemButton.click();
    await expect(editMappingPage.target(targetProductDisclaimers1), 'Target field is not visible').toBeVisible();
    await expect(editMappingPage.target(targetProductDisclaimers2), 'Target field is not visible').toBeVisible();
    await editMappingPage.removeListItemButton.click();
    await editMappingPage.removeListItemButton.click();
    await expect(editMappingPage.target(targetProductDisclaimers1), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.target(targetProductDisclaimers2), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.removeListItemButton, 'Remove list item button is visible').toBeHidden();
  });

  test('ENUM list add and remove item. @notForPr', async () => {
    await mappingsPage.mappingName(UITestProductFormatMapping).dblclick();
    await expect(editMappingPage.target(targetSpeakerType), 'Target field is not visible').toBeVisible();
    await editMappingPage.target(targetSpeakerType).click();
    await expect(editMappingPage.addListItemButton, 'Add list item button is not visible').toBeVisible();
    await editMappingPage.addListItemButton.click();
    await editMappingPage.addListItemButton.click();
    await expect(editMappingPage.target(targetSpeakerType1), 'Target field is not visible').toBeVisible();
    await expect(editMappingPage.target(targetSpeakerType2), 'Target field is not visible').toBeVisible();
    await editMappingPage.removeListItemButton.click();
    await editMappingPage.removeListItemButton.click();
    await expect(editMappingPage.target(targetSpeakerType1), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.target(targetSpeakerType2), 'Target field is visible').toBeHidden();
    await expect(editMappingPage.removeListItemButton, 'Remove list item button is visible').toBeHidden();
  });
});
