import { computed } from 'vue';
import { expect, it, describe } from 'vitest';
import { useFilterFieldsSection } from '@composables/ProductDetails';
import { FilterField } from '@customTypes/Products';

describe('useFilterFieldsSection', () => {
  describe('selectedColumns and unselectedColumns', () => {
    it('returns correct values of selected and unselected columns list if the selectedColumnNames has changed', async () => {
      // Arrange
      const filteredColumns = computed(() => [{ name: 'col1' }, { name: 'col2' }, { name: 'col3' }] as FilterField[]);

      // Act
      const { selectedColumnNames, selectedColumns, unselectedColumns } = useFilterFieldsSection(filteredColumns);
      selectedColumnNames.value = ['col1'];

      // Assert
      expect(selectedColumns.value).toStrictEqual([{ name: 'col1' }]);
      expect(unselectedColumns.value).toStrictEqual([{ name: 'col2' }, { name: 'col3' }]);
    });

    it('maintains the correct order if a value is selected and then unselected', async () => {
      // Arrange
      const filteredColumns = computed(() => [{ name: 'col1' }, { name: 'col2' }, { name: 'col3' }] as FilterField[]);

      // Act
      const { selectedColumnNames, unselectedColumns } = useFilterFieldsSection(filteredColumns);
      selectedColumnNames.value = ['col2'];
      selectedColumnNames.value = [];

      // Assert
      expect(unselectedColumns.value).toStrictEqual(filteredColumns.value);
    });
  });

  describe('separatorIsVisible', () => {
    it('returns true if selectedColumns and unselectColumns contain values', () => {
      // Arrange
      const filteredColumns = computed(() => [{ name: 'col1' }, { name: 'col2' }, { name: 'col3' }] as FilterField[]);

      // Act
      const { separatorIsVisible, selectedColumnNames } = useFilterFieldsSection(filteredColumns);
      selectedColumnNames.value = ['col1'];

      // Assert
      expect(separatorIsVisible.value).toBeTruthy();
    });

    it('returns false if selectedColumns does not contain values', () => {
      // Arrange
      const filteredColumns = computed(() => [{ name: 'col1' }, { name: 'col2' }, { name: 'col3' }] as FilterField[]);

      // Act
      const { separatorIsVisible } = useFilterFieldsSection(filteredColumns);

      // Assert
      expect(separatorIsVisible.value).toBeFalsy();
    });

    it('returns false if unselectColumns does not contain values', () => {
      // Arrange
      const filteredColumns = computed(() => [{ name: 'col1' }, { name: 'col2' }, { name: 'col3' }] as FilterField[]);

      // Act
      const { separatorIsVisible, selectedColumnNames } = useFilterFieldsSection(filteredColumns);
      selectedColumnNames.value = ['col1', 'col2', 'col3'];

      // Assert
      expect(separatorIsVisible.value).toBeFalsy();
    });
  });

  describe('createLabel', () => {
    it('returns star for required field', () => {
      // Arrange
      const filteredColumns = computed(() => []);
      const field = { name: 'col1', displayName: 'col name 1', isRequired: true } as FilterField;
      const { createLabel } = useFilterFieldsSection(filteredColumns);

      // Act
      const label = createLabel(field);

      // Assert
      expect(label).toBe(field.displayName + '*');
    });

    it('does not return star for optional field', () => {
      // Arrange
      const filteredColumns = computed(() => []);
      const field = { name: 'col1', displayName: 'col name 1', isRequired: false } as FilterField;
      const { createLabel } = useFilterFieldsSection(filteredColumns);

      // Act
      const label = createLabel(field);

      // Assert
      expect(label).toBe(field.displayName);
    });

    it('returns columns display name in lower case', () => {
      // Arrange
      const filteredColumns = computed(() => []);
      const field = { name: 'col1', displayName: 'Col Name 1', isRequired: false } as FilterField;
      const { createLabel } = useFilterFieldsSection(filteredColumns);

      // Act
      const label = createLabel(field);

      // Assert
      expect(label).toBe(field.displayName.toLocaleLowerCase());
    });
  });
});
