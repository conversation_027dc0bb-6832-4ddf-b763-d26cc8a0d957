import { SearchV2Response } from '@core/interfaces';
import { portalFetch, proxyPortalFetch } from '@utils';

export async function searchV2(parameters: []): Promise<SearchV2Response | undefined> {
  const body = new URLSearchParams();
  body.append('type', 'advsearch');
  body.append('acceptsPagingResponse', 'false');
  body.append('parameter', JSON.stringify(parameters));
  const fetch = import.meta.env.DEV ? proxyPortalFetch : portalFetch;
  const response = await fetch('/api/entity/v2', {
    method: 'POST',
    body: body,
    headers: {},
  });

  return response?.ok ? ((await response.json()) as SearchV2Response) : undefined;
}
