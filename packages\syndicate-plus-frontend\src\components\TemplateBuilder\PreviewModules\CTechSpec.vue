<template>
  <div class="mx-auto min-w-1000px w-1000px tech-spec-preview">
    <div class="flex flex-nowrap column justify-center">
      <div class="main-headline">{{ module.data.headline1 }}</div>
      <div v-if="module.data.specs?.length" class="min-w-700px mt-10px">
        <q-table
          ref="tableRef"
          v-model:pagination="pagination"
          hide-header
          row-key="name"
          separator="horizontal"
          dense
          :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
          flat
          :columns="columns"
          class="hide-checkboxes"
          :rows="module.data.specs"
          hide-bottom
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, TechSpecData } from '@customTypes/Aplus';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Variables
const columns = [
  {
    name: 'specName',
    field: 'specName',
    label: 'specName',
    align: 'left' as const,
  },
  {
    name: 'definition',
    field: 'definition',
    label: 'definition',
    align: 'left' as const,
  },
];

const pagination = {
  page: 1,
  rowsPerPage: 0,
};

// Computed
const module = computed<ContentModule<TechSpecData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.tech-spec-preview {
  .main-headline {
    font-size: 18px;
    font-weight: bold;
  }

  :deep(td:first-child) {
    background-color: var(--color-grey-lighter);
  }
}
</style>
