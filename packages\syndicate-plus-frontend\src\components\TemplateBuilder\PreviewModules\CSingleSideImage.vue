<template>
  <div class="mx-auto min-w-1000px w-1000px single-side-image-preview">
    <div class="flex m-20px" :class="[alignLeft ? 'justify-left' : 'justify-end']">
      <div v-if="alignLeft" class="flex flex-row flex-nowrap">
        <c-caption-angle-image-block-preview
          :image-data="module?.data.image"
          image-data-class="image mb-10px ml-5px mr-20px"
          data-testid="single-side-image-left"
        />
        <div class="flex flex-col">
          <h3 class="ml-10px">{{ module.data.headline }}</h3>
          <c-html-preview :html="module.data.body" />
        </div>
      </div>
      <div v-else class="flex flex-row flex-nowrap">
        <div class="flex flex-col mr-20px">
          <h3 class="ml-10px">{{ module.data.headline }}</h3>
          <c-html-preview :html="module.data.body" />
        </div>
        <c-caption-angle-image-block-preview
          :image-data="module?.data.image"
          image-data-class="image mb-10px ml-5px mr-20px"
          data-testid="single-side-image-right"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, SingleSideImageData } from '@customTypes/Aplus';
import { CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
  alignLeft: {
    type: Boolean,
    default: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<SingleSideImageData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.single-side-image-preview {
  .bullets {
    background-color: var(--color-grey-10);
    border: 1px solid var(--color-grey-light);
  }

  :deep() {
    .image {
      object-fit: unset;
      width: 300px;
      height: 300px;
      min-width: 300px;
      min-height: 300px;
    }
  }
}
</style>
