import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';

import { CSelectionCounter } from '@components/Shared';

describe('CMappingLength', () => {
  it('displays correct number of selected items', async () => {
    // Arrange
    const selectedItems = 5;
    const totalItems = 25;
    const selectedMask = 'selected mask';
    const wrapper = mount(CSelectionCounter, {
      props: {
        selectedItems,
        totalItems,
      },
      global: {
        mocks: {
          $t: () => `${selectedMask} ${totalItems}`,
        },
      },
    });

    // Act
    const componentText = wrapper.text();

    // Assert
    const expectedText = `${selectedItems} ${selectedMask} ${totalItems}`;
    expect(componentText).toBe(expectedText);
  });

  it('displays the total numbers if no items are selected', async () => {
    // Arrange
    const selectedItems = 0;
    const totalItems = 25;
    const totalItemsText = 'products';
    const wrapper = mount(CSelectionCounter, {
      props: {
        selectedItems,
        totalItems,
        totalItemsText,
      },
      global: {
        mocks: {
          $t: () => totalItemsText,
        },
      },
    });

    // Act
    const componentText = wrapper.text();

    // Assert
    const expectedText = `${totalItems} ${totalItemsText}`;
    expect(componentText).toBe(expectedText);
  });

  it('displays the totalItems if the selectedItems is less than 0', async () => {
    // Arrange
    const selectedItems = -1;
    const totalItems = 25;
    const selectedMask = 'selected mask';
    const wrapper = mount(CSelectionCounter, {
      props: {
        selectedItems,
        totalItems,
      },
      global: {
        mocks: {
          $t: () => `${selectedMask} ${totalItems}`,
        },
      },
    });

    // Act
    const componentText = wrapper.text();

    // Assert
    const expectedText = `${totalItems}`;
    expect(componentText).toBe(expectedText);
  });
});
