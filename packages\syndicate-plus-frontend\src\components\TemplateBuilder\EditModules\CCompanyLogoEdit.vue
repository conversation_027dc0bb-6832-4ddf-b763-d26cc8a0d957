<template>
  <div class="mx-auto p-20px min-w-800px w-800px company-logo-content">
    <c-image-uploader :style="imageDimensionsStyles" @url-updated="handleUrlUpdate">
      <c-image-block
        class="image-block"
        :dimensions="dimensions"
        :init-value="module?.data?.logoUrl"
        @url-updated="handleUrlUpdate"
      />
    </c-image-uploader>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { CImageBlock, CImageUploader } from '@components/TemplateBuilder/Blocks';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { CompanyLogoData, ContentModule, Dimension } from '@customTypes/Aplus';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Variables
const dimensions = { width: 600, height: 180 } as Dimension;

// Computed
const module = computed<ContentModule<CompanyLogoData>>(() => {
  return store.getModuleByIndex(props.index);
});

const imageDimensionsStyles = computed(() => {
  return `width: ${dimensions.width}px; height: ${dimensions.height}px;`;
});

// Functions
const handleUrlUpdate = (logoUrl: string) => {
  if (!module.value) {
    return;
  }

  module.value.data = { logoUrl } as CompanyLogoData;
  store.commitChanges(props.index, module.value);
};
</script>

<style lang="scss" scoped>
.company-logo-content {
  height: 200px;
  padding: 10px 20px;
  display: flex;
  justify-content: center;

  .image-block {
    object-fit: cover;
  }
}
</style>
