import { IStopCellEditStrategy, BaseStrategy } from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';
import { PreflightColumnDto } from '@dtos';

export class TabStrategy extends BaseStrategy implements IStopCellEditStrategy {
  constructor(rows: any[], columns: PreflightColumnDto[]) {
    super(rows, columns);
  }

  getNextRowAndColumnId(currentRowId: number, currentColumnId: number): TableCellCoordinates {
    const nextColumnIdInCurrentRow = this.getNextAvailableColumnId(currentColumnId);
    const shouldGoToNewRow = nextColumnIdInCurrentRow < 0;
    if (shouldGoToNewRow) {
      return {
        columnId: this.getFirstAvailableColumnId(),
        rowId: this.getNextAvailableRowId(currentRowId),
      } as TableCellCoordinates;
    } else {
      return {
        columnId: nextColumnIdInCurrentRow,
        rowId: currentRowId,
      } as TableCellCoordinates;
    }
  }
}
