import { Ref, computed } from 'vue';
import { ColumnDataType } from '@enums';

export default function useEditableDateCell(newValue: Ref<string>, dataType: ColumnDataType) {
  // Computed
  const datePickerDisplayValue = computed(() => {
    const parsedDate = new Date(parseInt(newValue.value));
    if (isNaN(parsedDate as any)) {
      return '';
    }

    const year = parsedDate.getFullYear();
    const month = (parsedDate.getMonth() + 1).toString().padStart(2, '0');
    const day = parsedDate.getDate().toString().padStart(2, '0');

    return `${year}/${month}/${day}`;
  });

  const isDateType = computed<boolean>(() => dataType === ColumnDataType.DATE);

  return { datePickerDisplayValue, isDateType };
}
