<template>
  <c-section class="field-mapping-tab pr-0 pb-0">
    <div class="filter-panel">
      <c-select
        v-model="selectedInboundTemplate"
        :placeholder="$t('syndicate_plus.common.template')"
        :label="$t('syndicate_plus.common.template')"
        :options="inboundTemplates"
        option-label="templateName"
        option-value="id"
        @update:model-value="loadInboundData"
      />
    </div>
    <div v-if="isLoading" class="spinner">
      <c-spinner />
    </div>
    <div v-else-if="inboundTemplateFields?.length">
      <q-table
        v-if="selectedInboundTemplate"
        ref="tableRef"
        :columns="inboundMappingTableColumns"
        :rows="inboundTemplateFields"
        :pagination="{ page: 1, rowsPerPage: 0 }"
        row-key="name"
        separator="cell"
        dense
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        class="hide-checkboxes"
        hide-bottom
      >
        <template #body-cell-sourceFieldName="sourceFieldName">
          <q-td>
            {{ getSourceFieldDisplayName(sourceFieldName.row.sourceFieldName) }}
          </q-td>
        </template>
        <template #body-cell-status="props">
          <q-td>
            <c-small-square :color="props.row['status'] ? SquareColor.GREEN : SquareColor.RED">
              <q-tooltip>
                {{
                  props.row['status']
                    ? $t('syndicate_plus.mapping.mapped_field')
                    : $t('syndicate_plus.mapping.unmapped_field')
                }}
              </q-tooltip>
            </c-small-square>
          </q-td>
        </template>
        <template #body-cell-actions>
          <c-table-actions>
            <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
          </c-table-actions>
        </template>
        <template #body-cell-function="props">
          <q-td :props="props">
            <div class="field-mapping-function-class">
              <div v-for="functionObject in props.row.functions" :key="functionObject">
                <c-field-mapping-function :func="functionObject" />
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-dataType="props">
          <c-table-actions>
            {{ `${props.row['dataType']?.toLocaleLowerCase()}` }}
          </c-table-actions>
        </template>
        <template #body-cell-length="props">
          <q-td :props="props">
            <c-mapping-length
              :min-length="props.row['minCharacterLength']"
              :max-length="props.row['maxCharacterLength']"
            />
          </q-td>
        </template>
        <template #body-cell-importanceType="props">
          <q-td :props="props">
            <div class="flex flex-nowrap justify-start">
              {{ $t(`syndicate_plus.mapping.importace_type.${props.row['importanceType']}`) }}
            </div>
          </q-td>
        </template>
      </q-table>
    </div>
    <div v-else>
      <c-no-data
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_title')"
        :text="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_message')"
      />
    </div>
  </c-section>
</template>

<script setup lang="ts">
import { onBeforeMount } from 'vue';
import { useAppInsightsStore } from '@stores';
import { CFieldMappingFunction, CMappingLength } from '@components/FieldMapping';
import { CNoData, CSmallSquare } from '@components';
import { SquareColor, PageName } from '@enums';
import { inboundMappingTableColumns } from '@const';
import { getSourceFieldDisplayName } from '@helpers';
import { useInboundTemplates } from '@composables/FieldMapping';

const { setScreenName } = useAppInsightsStore();

// Composables
const { isLoading, inboundTemplates, selectedInboundTemplate, inboundTemplateFields, loadInboundData } =
  useInboundTemplates();

// Lifecycle methods
onBeforeMount(() => {
  setScreenName(PageName.INBOUND_MAPPINGS);
});
</script>

<style lang="scss" scoped>
$field-mapping-tab-panel-offset: 296px;

.spinner {
  margin: auto;
  width: min-content;
}

.filter-panel {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.field-mapping-function-class {
  display: flex;
  align-items: center;
}
</style>
