import { ref } from 'vue';
import { ContentModule, FourImageTextData } from '@customTypes/Aplus';

export default function useFourImagesAndText() {
  // Refs
  const module = ref<ContentModule<FourImageTextData>>({} as ContentModule<FourImageTextData>);

  // Functions
  const initData = () => {
    if (!Object.keys(module.value.data)?.length) {
      module.value.data = {
        headline: '',
        image1: {
          altText: '',
          angle: '',
        },
        headline1: '',
        body1: '',
        image2: {
          altText: '',
          angle: '',
        },
        headline2: '',
        body2: '',
        image3: {
          altText: '',
          angle: '',
        },
        headline3: '',
        body3: '',
        image4: {
          altText: '',
          angle: '',
        },
        headline4: '',
        body4: '',
      } as FourImageTextData;
    }
  };

  return { module, initData };
}
