import { it, describe, vi, vitest, expect, afterEach, beforeEach, Mock } from 'vitest';
import { useInterval } from '@composables';
import { LongRunningJobStatuses } from '@core/enums';
import { History } from '@core/interfaces';
import { convertTimestampToDatetime } from '@services/helper/dateHelpers';
import { useHistoryPoller } from '@core/composables/History';
import { createPinia, setActivePinia } from 'pinia';
import { getHistoryByTargetCompany } from '@core/services';

vi.mock('@composables/useInterval');
vi.mock('@core/services/historyApi');

const mockedStartInterval = vitest.fn();
const mockedStopInterval = vitest.fn();
const tradinPartnerId = 'test tp';

(useInterval as any).mockReturnValue({ startInterval: mockedStartInterval, stopInterval: mockedStopInterval });

describe('useHistoryPoller', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should not start polling if there are no unfinished jobs', async () => {
    // Arrange
    const { startPolling } = useHistoryPoller(tradinPartnerId);
    const historyRows = [
      {
        state: LongRunningJobStatuses.FINISHED,
        lastRun: convertTimestampToDatetime(Date.now()),
      },
    ] as History[];
    (getHistoryByTargetCompany as Mock).mockResolvedValue(historyRows);

    // Act
    await startPolling();

    // Assert
    expect(mockedStartInterval).not.toHaveBeenCalled();
  });

  it('should not start polling if there are unfinished jobs that are too old', async () => {
    // Arrange
    const { startPolling } = useHistoryPoller(tradinPartnerId);
    const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).getTime();
    const historyRows = [
      {
        state: LongRunningJobStatuses.QUEUED,
        lastRun: convertTimestampToDatetime(yesterday),
      },
    ] as History[];
    (getHistoryByTargetCompany as Mock).mockResolvedValue(historyRows);

    // Act
    await startPolling();

    // Assert
    expect(mockedStartInterval).not.toHaveBeenCalled();
  });

  it.skip('should start polling if there are unfinished jobs', async () => {
    // Arrange
    const { startPolling } = useHistoryPoller(tradinPartnerId);
    const historyRows = [
      {
        state: LongRunningJobStatuses.RUNNING,
        lastRun: convertTimestampToDatetime(new Date().getTime()),
      },
    ] as History[];
    (getHistoryByTargetCompany as Mock).mockResolvedValue(historyRows);

    // Act
    await startPolling();

    // Assert
    expect(mockedStartInterval).toHaveBeenCalledOnce();
  });

  it.skip('should stop polling once there are no more unfinished jobs', async () => {
    // Arrange
    const { startPolling } = useHistoryPoller(tradinPartnerId);
    const unfinishedHistoryRows = [
      {
        state: LongRunningJobStatuses.RUNNING,
        lastRun: convertTimestampToDatetime(new Date().getTime()),
      },
    ] as History[];
    const completedHistoryRows = [
      {
        state: LongRunningJobStatuses.FINISHED,
        lastRun: convertTimestampToDatetime(new Date().getTime()),
      },
    ] as History[];
    (getHistoryByTargetCompany as Mock).mockResolvedValueOnce(unfinishedHistoryRows);
    (getHistoryByTargetCompany as Mock).mockResolvedValueOnce(completedHistoryRows);

    // Act
    await startPolling();

    // Assert
    expect(mockedStartInterval).toHaveBeenCalledOnce();
    expect(mockedStopInterval).toHaveBeenCalledOnce();
  });
});
