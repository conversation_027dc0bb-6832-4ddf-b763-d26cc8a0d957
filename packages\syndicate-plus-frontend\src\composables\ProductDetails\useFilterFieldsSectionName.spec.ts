import { ref } from 'vue';
import { vi, expect, it, describe } from 'vitest';
import { useFilterFieldsSectionName } from '@composables/ProductDetails';

const testTranslation = 'test';
const useI18n = vi.fn().mockReturnValue({
  t: vi.fn().mockReturnValue(testTranslation),
});

describe('useFilterFieldsSectionName', () => {
  describe('sectionLabel', () => {
    it('returns text without counter if selected columns list is empty', async () => {
      // Arrange
      const selectedColumnNames = ref([]);

      // Act
      const { sectionLabel } = useFilterFieldsSectionName(selectedColumnNames, useI18n);

      // Assert
      expect(sectionLabel.value).toBe(testTranslation);
    });

    it('returns text with counter if selected columns list is not empty', async () => {
      // Arrange
      const selectedColumnNames = ref(['col1', 'col2']);

      // Act
      const { sectionLabel } = useFilterFieldsSectionName(selectedColumnNames, useI18n);

      // Assert
      expect(sectionLabel.value).toBe(`${testTranslation} (${selectedColumnNames.value.length})`);
    });
  });
});
