<template>
  <q-expansion-item v-model="isExpandedLocal" :label="label" header-class="section-header">
    <div class="content-wrapper">
      <slot />
    </div>
  </q-expansion-item>

  <q-separator />
</template>

<script setup lang="ts">
import { watchEffect, ref } from 'vue';

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  isExpanded: {
    type: Boolean,
    default: false,
  },
});

const isExpandedLocal = ref<boolean>();

watchEffect(() => {
  isExpandedLocal.value = props.isExpanded;
});
</script>

<style scoped lang="scss">
.q-item__section {
  padding-left: 16px;
}

.content-wrapper {
  padding: 0px 16px 16px;
}

:deep(.section-header) {
  font-weight: bold;
}
</style>
