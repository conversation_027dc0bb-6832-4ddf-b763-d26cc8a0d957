<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <div class="row q-gutter-lg w-1/2">
      <div class="col section">
        <h3>
          {{ $t('syndicate_plus.syndication.outbox.enter_filename') }}
        </h3>
        <q-input
          v-model="filename"
          v-bind="$inri.input"
          :label="$t('syndicate_plus.syndication.outbox.filename')"
          autofocus
          @keyup.enter="logEventAndClick(ActionName.DOWNLOAD_FILE, downloadFile)"
        />

        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.syndication.download')"
            :disabled="isDownloadFileButtonDisabled"
            @click="logEventAndClick(ActionName.DOWNLOAD_FILE, downloadFile)"
          >
          </c-btn>
        </div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useDialog } from '@inriver/inri';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';

// Composables
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel } = useDialog();

// Refs
const filename = ref<string>('');

// Computed
const isDownloadFileButtonDisabled = computed(() => !filename.value);

const emit = defineEmits(['download-file']);

// Functions
const downloadFile = async () => {
  isLoading.value = true;
  emit('download-file', filename.value);
};
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }

  .action-buttons {
    padding-top: 10px;
  }
}
</style>
