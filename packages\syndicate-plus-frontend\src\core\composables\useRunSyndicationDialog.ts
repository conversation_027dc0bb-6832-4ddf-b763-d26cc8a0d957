import { computed, ref, Ref, ComputedRef } from 'vue';
import { runSyndication } from '@core/services';
import {
  ChannelLinkResponse,
  CoreFormatFile,
  DynamicFormatFile,
  DynamicMappingResponse,
  JobRequestResult,
  Mapping,
  Output,
  Syndication,
} from '@core/interfaces';
import {
  CollectionTypes,
  DynamicOutputType,
  IdentifierTypes,
  MappingSourceTypes,
  MappingTypes,
  OutputDestinationTypes,
} from '@core/enums';
import { Collection, Workarea } from '@core/interfaces/Workarea';
import {
  getRelevantCoreMappingsBasedOnSyndicationsByWorkarea,
  getRelevantCoreMappingsByChannel,
  getRelevantCoreMappingsByWorkarea,
  getRelevantDynamicMappingsByChannel,
  getRelevantDynamicMappingsByWorkarea,
  getUniqueMappings,
} from './useRunSyndicationDialogHelper';
import { CoreOutputLinkResponse, DynamicOutputLinkResponse } from '@core/interfaces/Outputs';

export default function useRunSyndicationDialog(
  currentSyndications: Ref<Syndication[]> | ComputedRef<Syndication[]>,
  coreMappings: Ref<Mapping[]>,
  dynamicMappings: Ref<DynamicMappingResponse[]>,
  coreFormats: Ref<CoreFormatFile[]>,
  dynamicFormats: Ref<DynamicFormatFile[]>,
  collection: Collection<ChannelLinkResponse | Workarea>,
  dynamicAssignedOutputs: Ref<DynamicOutputLinkResponse[]>,
  coreAssignedOutputs: Ref<CoreOutputLinkResponse[]>
) {
  // Refs
  const selectedMapping = ref<Collection<Mapping | DynamicMappingResponse>>();
  const selectedOutput = ref<Output | DynamicOutputType | undefined>();
  const isLoading = ref<boolean>(false);

  // Computed
  const isChannelType = computed(() => collection.type === CollectionTypes.CHANNEL);
  const isDynamicSelectedMapping = computed(() => selectedMapping.value?.type === MappingTypes.DYNAMIC_CORE_MAPPING);
  const confirmIsDisabled = computed(() => !selectedMapping.value || !selectedOutput.value);

  const mappingsForSelectedCollection = computed(() => {
    let mappings = [] as Collection<Mapping | DynamicMappingResponse>[];
    if (isChannelType.value) {
      const channel = collection.metadata as ChannelLinkResponse;

      mappings = [
        ...getRelevantDynamicMappingsByChannel(dynamicMappings.value, dynamicFormats.value, channel),
        ...getRelevantCoreMappingsByChannel(coreMappings.value, coreFormats.value, channel),
      ] as Collection<Mapping | DynamicMappingResponse>[];
    } else {
      const workarea = collection.metadata as Workarea;

      mappings = [
        ...getRelevantDynamicMappingsByWorkarea(dynamicMappings.value, dynamicFormats.value, workarea),
        ...getRelevantCoreMappingsByWorkarea(coreMappings.value, coreFormats.value, workarea),
        ...getRelevantCoreMappingsBasedOnSyndicationsByWorkarea(currentSyndications.value, workarea),
      ] as Collection<Mapping | DynamicMappingResponse>[];
    }

    return getUniqueMappings(mappings);
  });

  const outputs = computed(() => {
    if (!selectedMapping.value) {
      return [];
    }

    if (isDynamicSelectedMapping.value) {
      const outputs = dynamicAssignedOutputs.value?.filter(
        (x) =>
          x.environmentFormatId ===
          Number((selectedMapping.value?.metadata as DynamicMappingResponse).environmentFormatId)
      );

      return [
        { ExtensionDisplayName: DynamicOutputType.API, ExtensionId: DynamicOutputType.API } as Output,
        ...outputs.map((x) => {
          return {
            ExtensionDisplayName: x.extensionName,
            ExtensionId: x.extensionId,
            OutputFormat: x.outputFormat,
          } as Output;
        }),
      ];
    }

    const assignedOutputs = coreAssignedOutputs.value?.filter(
      (x) => x.coreFormatId === (selectedMapping.value?.metadata as Mapping).FormatFileId
    );

    const filteredSyndications = currentSyndications.value.filter(
      (x) => x.MappingId === (selectedMapping.value?.metadata as Mapping)?.MappingId
    );
    const outputsFromSyndications = filteredSyndications
      .map((x) => {
        return {
          ExtensionId: x.ExtensionId,
          ExtensionDisplayName: x.ExtensionDisplayName,
          OutputFormat: x.OutputFormat,
        } as Output;
      })
      .map((item) => [item['ExtensionId'], item] as [string, Output]);

    return [
      ...assignedOutputs.map((x) => {
        return {
          ExtensionDisplayName: x.extensionName,
          ExtensionId: x.extensionId,
          OutputFormat: x.outputFormat,
        } as Output;
      }),
      ...new Map(outputsFromSyndications).values(),
    ];
  });

  // Functions
  const init = () => {
    selectedOutput.value = outputs.value[0];
  };

  const onConfirm = async (selectedEntityIds?: number[], runReview = false): Promise<JobRequestResult> => {
    const syndicationModel = {} as Syndication;
    const output = selectedOutput.value as Output;
    if (isDynamicSelectedMapping.value) {
      syndicationModel.ExtensionDisplayName = output.ExtensionDisplayName;
      const dynamicFormatFileMapping = selectedMapping.value?.metadata as DynamicMappingResponse;
      syndicationModel.ExtensionId = output?.ExtensionId;
      syndicationModel.OutputDestination =
        output?.ExtensionDisplayName === DynamicOutputType.API
          ? OutputDestinationTypes.OUTPUT_ADAPTER
          : OutputDestinationTypes.EXTENSION;
      syndicationModel.MappingSource = MappingSourceTypes.OUTPUT_ADAPTER;
      syndicationModel.MappingId = dynamicFormatFileMapping?.id;
      syndicationModel.MappingName = dynamicFormatFileMapping?.name;
      syndicationModel.Id = 0;
      syndicationModel.DynamicFormatId = Number(dynamicFormatFileMapping?.environmentFormatId);
      syndicationModel.IdentifierType = IdentifierTypes.DYNAMIC_FORMAT_ID;
      syndicationModel.EnableSKU = dynamicFormatFileMapping.EnableSKU;
    } else {
      syndicationModel.ExtensionDisplayName = output?.ExtensionDisplayName;
      const currentMapping = selectedMapping.value?.metadata as Mapping;
      const formatFileId = coreMappings.value.find((m) => m.MappingId === currentMapping.MappingId)?.FormatFileId;
      syndicationModel.Id = 0;
      syndicationModel.MappingId = currentMapping.MappingId;
      syndicationModel.MappingName = currentMapping.MappingName;
      syndicationModel.ExtensionId = output?.ExtensionId;
      syndicationModel.IdentifierType = IdentifierTypes.FILE_FORMAT_ID;
      syndicationModel.FileFormatId = formatFileId;
      syndicationModel.EnableSKU = currentMapping.EnableSKU;
    }

    if (isChannelType.value) {
      syndicationModel.ChannelId = (collection.metadata as ChannelLinkResponse)?.channelId;
      syndicationModel.EntityIds = [(collection.metadata as ChannelLinkResponse)?.channelNodeId];
    }

    if (!isChannelType.value) {
      const syndication = currentSyndications.value.find(
        (x) =>
          x.ExtensionId === (selectedOutput.value as Output)?.ExtensionId &&
          x.MappingId === (selectedMapping.value?.metadata as Mapping)?.MappingId &&
          x.WorkareaId === (collection.metadata as Workarea)?.id
      );
      if (syndication) {
        syndicationModel.Id = syndication.Id;
        syndicationModel.Name = syndication.Name;
      }
      syndicationModel.WorkareaId = (collection.metadata as Workarea)?.id;
      syndicationModel.WorkareaName = (collection.metadata as Workarea)?.text;
    }

    if (selectedEntityIds?.length) {
      syndicationModel.WorkareaId = '';
      syndicationModel.WorkareaName = '';
      syndicationModel.EntityIds = selectedEntityIds;
    }

    if (runReview) {
      syndicationModel.IsPreviewEnabled = true;
      syndicationModel.RunPreview = true;
    }

    isLoading.value = true;
    try {
      return await runSyndication(syndicationModel);
    } finally {
      isLoading.value = false;
    }
  };

  const onChangeMapping = () => {
    selectedOutput.value = outputs.value[0];
  };

  return {
    isLoading,
    confirmIsDisabled,
    mappingsForSelectedCollection,
    selectedMapping,
    outputs,
    selectedOutput,
    init,
    onConfirm,
    onChangeMapping,
  };
}
