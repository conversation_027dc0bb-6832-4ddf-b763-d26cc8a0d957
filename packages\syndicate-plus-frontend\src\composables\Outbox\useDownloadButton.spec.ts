import { it, describe, expect, vi } from 'vitest';
import { ref, Ref } from 'vue';
import { useDownloadButton } from '@composables/Outbox';
import { ProductOutboxRow } from '@customTypes';
import { OutboxStatus, SyndicationPageTabNames } from '@enums';
import { StatusModel } from '@utils/statusHelper';
import { SquareColor } from '@enums/SquareColor';

describe('useDownloadButton', () => {
  const defaultTabRef = ref(SyndicationPageTabNames.Outbox);
  const useI18n = vi.fn().mockReturnValue({
    t: vi.fn(),
  });

  [
    { tab: SyndicationPageTabNames.Collection, visible: false },
    { tab: SyndicationPageTabNames.Aplus, visible: false },
    { tab: SyndicationPageTabNames.Products, visible: false },
    { tab: SyndicationPageTabNames.Outbox, visible: true },
  ].forEach(({ tab, visible }) => {
    it(`has visible ${visible} for ${tab} tab`, async () => {
      // Arrange
      const tabRef = ref(tab);
      const row = ref(createTestRow(0, 0, OutboxStatus.COMPLETED));

      // Act
      const { isDownloadButtonVisible } = useDownloadButton(useI18n, tabRef, row);

      // Assert
      expect(isDownloadButtonVisible.value).toBe(visible);
    });
  });

  it('is not visible if no row is selected', async () => {
    // Arrange
    const row = ref(undefined);

    // Act
    const { isDownloadButtonVisible } = useDownloadButton(useI18n, defaultTabRef, row);

    // Assert
    expect(isDownloadButtonVisible.value).toBe(false);
  });

  it.each([0, 10])(
    'is visible and enabled if status is completed and there are (%i) passed records',
    async (failedRecords) => {
      // Arrange
      const getTranslationSpy = vi.fn();
      const useI18n = vi.fn().mockReturnValue({
        t: getTranslationSpy,
      });
      const passedRecords = 10;
      // const failedRecords = 5;
      const row = createTestRow(passedRecords, failedRecords, OutboxStatus.COMPLETED);

      // Act
      const { isDownloadButtonVisible, downloadButtonDisabledState } = useDownloadButton(useI18n, defaultTabRef, row);

      // Assert
      expect(isDownloadButtonVisible.value).toBe(true);
      expect(downloadButtonDisabledState.value.disabled).toBe(false);
      expect(getTranslationSpy).toHaveBeenCalledWith('syndicate_plus.syndication.download');
    }
  );

  it('is visible and disabled if status is completed and there are no passed records', async () => {
    // Arrange
    const getTranslationSpy = vi.fn();
    const useI18n = vi.fn().mockReturnValue({
      t: getTranslationSpy,
    });
    const passedRecords = 0;
    const failedRecords = 10;
    const row = createTestRow(passedRecords, failedRecords, OutboxStatus.COMPLETED);

    // Act
    const { isDownloadButtonVisible, downloadButtonDisabledState } = useDownloadButton(useI18n, defaultTabRef, row);

    // Assert
    expect(isDownloadButtonVisible.value).toBe(true);
    expect(downloadButtonDisabledState.value.disabled).toBe(true);
    expect(getTranslationSpy).toHaveBeenCalledWith('syndicate_plus.syndication.export_failed');
  });

  it.each([OutboxStatus.FAILED, OutboxStatus.IN_PROGRESS, OutboxStatus.IN_QUEUE, OutboxStatus.UNKNOWN])(
    'is visible and disabled if status is not (%s) and there are passed records',
    async (outboxStatus: OutboxStatus) => {
      // Arrange
      const getTranslationSpy = vi.fn();
      const useI18n = vi.fn().mockReturnValue({
        t: getTranslationSpy,
      });
      const passedRecords = 10;
      const failedRecords = 0;
      const row = createTestRow(passedRecords, failedRecords, outboxStatus);

      // Act
      const { isDownloadButtonVisible, downloadButtonDisabledState } = useDownloadButton(useI18n, defaultTabRef, row);

      // Assert
      expect(isDownloadButtonVisible.value).toBe(true);
      expect(downloadButtonDisabledState.value.disabled).toBe(true);
      expect(getTranslationSpy).toHaveBeenCalledWith('syndicate_plus.syndication.export_incomplete');
    }
  );
});

function createTestRow(
  passedRecords: number,
  failedRecords: number,
  outboxStatus: OutboxStatus
): Ref<ProductOutboxRow> {
  const row = {
    status: new StatusModel(outboxStatus, SquareColor.UNKNOWN),
    fileLink: 'fileLink',
    passedRecords: passedRecords,
    failedRecords: failedRecords,
  } as ProductOutboxRow;

  return ref(row);
}
