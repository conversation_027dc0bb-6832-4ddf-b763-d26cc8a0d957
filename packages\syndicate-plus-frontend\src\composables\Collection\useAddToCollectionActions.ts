import { Ref } from 'vue';
import { CollectionService } from '@services';
import { ProductFilterDto } from '@dtos';

export default function useAddToCollectionActions(isLoading: Ref<boolean>) {
  // Functions
  const addToCollection = async (
    collectionId: number,
    selectAllProducts: boolean,
    filter: ProductFilterDto,
    searchValue: string,
    groupBy: string,
    keys: string[]
  ) => {
    isLoading.value = true;
    if (selectAllProducts) {
      await CollectionService.assignAllProducts(collectionId, filter, searchValue, groupBy);
    } else {
      await CollectionService.assignIndividualProducts(collectionId, groupBy, keys);
    }

    isLoading.value = false;
  };

  return { addToCollection };
}
