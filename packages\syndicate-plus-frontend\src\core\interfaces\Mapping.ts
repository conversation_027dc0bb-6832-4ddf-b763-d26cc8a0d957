import { ImportanceType } from '@enums';

export interface Mapping {
  MappingId: number;
  MappingName: string;
  FormatFileId: number;
  EnableSKU: boolean;
}

export interface MappingDetailsResponse {
  MappingId: number;
  MappingName: string;
  WorkareaEntityTypeId: string;
  FirstRelatedEntityTypeId: string;
  FirstLinkEntityTypeId: string | null;
  SecondRelatedEntityTypeId: string;
  SecondLinkEntityTypeId: string | null;
  DefaultLanguage: string;
  MappingModelList: MappingFieldResponse[];
  ResourceFields: MappingResourceFieldResponse[];
  FormatId: number;
  EnableSKU: boolean;
}

export interface MappingResourceFieldResponse {
  Id: number;
  InRiverFieldTypeId: string | null;
  InRiverDataType: string | null;
  ConverterArgs: string | null;
  ConverterId: number | null;
  ConverterClass: string | null;
}

export interface MappingFieldResponse {
  inRiverEntityTypeId: string | null;
  inRiverFieldTypeId: string | null;
  inRiverDataType: string | null;
  FormatField: string;
  FormatFieldId: string;
  DefaultValue: string;
  ConverterArgs: string | null;
  ConverterClass: string | null;
  ConverterId: number | null;
  Recommended: boolean;
  Mandatory: boolean;
  Unique: boolean;
  FormatDataType: string;
  MinLength: number | null;
  MaxLength: number | null;
  Category: string | null;
  Enumerations: FormatEnum[];
  CvlCompleteness: boolean;
  listItemOf?: string;
}

export interface MappingDetails {
  MappingModelList: MappingField[];
}

export interface MappingField {
  sourceField: string;
  sourceFieldId: string;
  tradingPartnerField: string;
  formatFieldId: number | string;
  defaultValue: string;
  function: string;
  importanceType: ImportanceType;
  dataType: string;
  minLength: number;
  maxLength: number;
  mapped: boolean;
}

export interface FormatEnum {
  EnumKey: number;
  EnumValue: string;
  EnumerationId: number;
  FieldValue: string;
}
