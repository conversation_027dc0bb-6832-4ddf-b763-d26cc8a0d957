<template>
  <div class="syndicate-a-plus-container">
    <h3 class="pb-6">
      {{ $t('syndicate_plus.syndication.enhanced_content_dialog.syndicate_via_enhanced_content') }}
    </h3>
    <c-select
      v-model="selectedSyndicateTemplate"
      :label="$t('syndicate_plus.common.template')"
      :options="templates"
      option-value="aplusTemplateId"
      option-label="templateName"
    />
    <c-btn
      :label="$t('syndicate_plus.syndication.enhanced_content_dialog.syndicate_with_template')"
      @click="$emit('on-syndicate-template-select', selectedSyndicateTemplate)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { APlusTemplate } from '@customTypes/Aplus';

defineProps({
  templates: {
    type: Array<APlusTemplate>,
    default: [] as any[],
  },
});

defineEmits(['on-syndicate-template-select']);

// Refs
const selectedSyndicateTemplate = ref<APlusTemplate>();
</script>
<style lang="scss" scoped>
.a-plus-dialog-container {
  display: flex;
  flex-direction: row;
}
</style>
