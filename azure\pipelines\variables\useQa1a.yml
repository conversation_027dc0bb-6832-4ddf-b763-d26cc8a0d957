variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'use'
  stackType: 'qa1a'

  # Deploy dependencies
  dependsOn: 'useDev1a'
  dependsOnSecond: 'Build_useQa1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-qa1a-use.productmarketingcloud.com'

  auth0Domain: 'inriverdev.eu.auth0.com'
  auth0ClientId: 'nbL2G6DGbSzuA3PHlw8gA9WjCBtCwOUz'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: ''

  allowMissingOrgMapping: true
  
  # Application insights instrumentation key
  instrumentationKey: 'e82a07fd-f68c-484f-ac12-43c7ee855aec'