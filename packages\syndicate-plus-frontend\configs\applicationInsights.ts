import { ApplicationInsights } from '@microsoft/applicationinsights-web';
import { getEnvVariable } from '../src/utils/envVariablesManager';

const appInsights = new ApplicationInsights({
  config: {
    instrumentationKey: getEnvVariable('INSTRUMENTATION_KEY'),
    autoTrackPageVisitTime: true,
    enableAutoRouteTracking: false, // We control this ourselves using the router. Triggered in router
    disableAjaxTracking: true,
    disableFetchTracking: true,
  },
});

if (!import.meta.env.DEV) {
  appInsights.loadAppInsights();
  appInsights.addTelemetryInitializer((envelope) => {
    // It must be done with push; otherwise, it doesn't map to the context property (the one that all applications are grouping).
    // @ts-ignore
    envelope.tags.push({ 'ai.cloud.role': 'Syndicate Plus' });
  });
}

export default appInsights;
