<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <div class="row q-gutter-lg" :class="rowClass">
      <div v-if="canAddToCollection" class="col section">
        <h3>{{ $t('syndicate_plus.collections.create_collection.add_title') }}</h3>
        <c-select
          v-model="selectedCollection"
          :options="collections"
          :placeholder="$t('syndicate_plus.collections.collection')"
          :label="$t('syndicate_plus.collections.collection')"
          option-label="name"
          option-value="id"
        />
        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.common.add')"
            :disabled="addToCollectionIsDisabled"
            @click="logEventAndClick(ActionName.ADD_TO_COLLECTION, onAddToCollection)"
          >
            <q-tooltip v-if="addToCollectionIsDisabled">
              {{ $t('syndicate_plus.collections.create_collection.add_disabled_tooltip') }}
            </q-tooltip>
          </c-btn>
        </div>
      </div>
      <div class="col section">
        <h3>{{ $t('syndicate_plus.collections.create_collection.create_title') }}</h3>
        <q-input
          v-model="newCollectionName"
          v-bind="$inri.input"
          :label="$t('syndicate_plus.collections.create_collection.collection_name')"
          :autofocus="!canAddToCollection"
          @keyup.enter="logEventAndClick(ActionName.CREATE_COLLECTION, onCreateCollection)"
        />
        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.common.create')"
            :disabled="createCollectionIsDisabled"
            @click="logEventAndClick(ActionName.CREATE_COLLECTION, onCreateCollection)"
          >
            <q-tooltip v-if="createCollectionIsDisabled">
              {{ $t('syndicate_plus.collections.create_collection.create_disabled_tooltip') }}
            </q-tooltip>
          </c-btn>
        </div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue';
import { useDialog, notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { ProductFilterDto } from '@dtos';
import {
  useCreateCollection,
  useAddToCollection,
  useAddToCollectionActions,
  useCreateCollectionActions,
} from '@composables/Collection';
import { useEmitter } from '@composables';
import { useAppInsightsStore } from '@stores';
import { ActionName, SyndicationPageTabNames, SortDirection } from '@enums';
import { CollectionService } from '@services';
import { CollectionDetails } from '@customTypes';
import { useRouter } from '@composables/useRouter';

const props = defineProps({
  canAddToCollection: {
    type: Boolean,
    default: false,
  },
  groupBy: {
    type: String,
    required: true,
  },
  keys: {
    type: Array<string>,
    required: true,
  },
  selectAllProducts: {
    type: Boolean,
    default: false,
  },
  filter: {
    type: ProductFilterDto,
    default: null,
  },
  searchValue: {
    type: String,
    default: '',
  },
  tradingPartnerName: {
    type: String,
    required: true,
  },
});

// Refs
const collections = ref<CollectionDetails[]>([]);

// Computed
const rowClass = computed(() => (props.canAddToCollection ? '' : 'w-1/2'));

// Composables
const { t } = useI18n();
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel, confirmSuccess } = useDialog();
const { createCollectionIsDisabled, newCollectionName } = useCreateCollection(isLoading);
const { addToCollectionIsDisabled, selectedCollection } = useAddToCollection(isLoading);
const { createCollection } = useCreateCollectionActions(isLoading);
const { addToCollection } = useAddToCollectionActions(isLoading);
const emitter = useEmitter();
const { goToPage, route } = useRouter();

// Variables
const destinationId = parseInt(route.params.destinationId as string);
const subCatalogId = parseInt(route.params.subCatalogId as string);

// Functions
const onAddToCollection = async () => {
  if (!selectedCollection.value) {
    throw new Error('Template Id cannot be empty');
  }

  const { selectAllProducts, filter, searchValue, groupBy, keys } = props;
  await addToCollection(selectedCollection.value.id, selectAllProducts, filter, searchValue, groupBy, keys);

  emitter.emit('collection-updated');
  showSuccessToast(t('syndicate_plus.collections.create_collection.added_successfully'));
};

const onCreateCollection = async () => {
  const nameExists = await CollectionService.checkIfCollectionNameExists(
    props.tradingPartnerName,
    newCollectionName.value
  );

  if (nameExists) {
    notify.error(t('syndicate_plus.collections.notifications.name_exists'), {
      position: 'bottom-right',
    });
    return;
  }

  const collectionId = await createCollection(props.tradingPartnerName, newCollectionName.value);

  if (props.canAddToCollection) {
    const { selectAllProducts, filter, searchValue, groupBy, keys } = props;
    await addToCollection(collectionId, selectAllProducts, filter, searchValue, groupBy, keys);
  }

  emitter.emit('collection-created');
  showSuccessToast(t('syndicate_plus.collections.create_collection.created_successfully'));
};

const showSuccessToast = (message: string) => {
  confirmSuccess(null);

  notify.success(message, {
    actions: [
      {
        label: t('syndicate_plus.collections.create_collection.toast_action_prompt'),
        color: 'black',
        handler: () => {
          navigateToCollectionTab();
        },
      },
    ],
  });
};

const navigateToCollectionTab = () => {
  goToPage('syndication-page', {
    subCatalogId: subCatalogId,
    destinationId: destinationId,
    tabName: SyndicationPageTabNames.Collection,
  });
};

// Lifecycle methods
onBeforeMount(async () => {
  collections.value = await CollectionService.getTradingPartnerCollections(
    props.tradingPartnerName,
    300,
    0,
    SortDirection.ASC
  );
});
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }

  .action-buttons {
    padding-top: 10px;
  }
}
</style>
