import { describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useCreateCollection } from '@composables/Collection';

describe('useCreateCollection', () => {
  it('should calculate correct createCollectionIsDisabled value for different input', async () => {
    const testData = [
      { collectionName: '', isLoadingState: true, expectedValue: true },
      { collectionName: 'test collection', isLoadingState: true, expectedValue: true },
      { collectionName: '', isLoadingState: false, expectedValue: true },
      { collectionName: 'test collection', isLoadingState: false, expectedValue: false },
    ];

    testData.forEach(({ collectionName, isLoadingState, expectedValue }) => {
      // Arrange + Act
      const isLoading = ref(isLoadingState);
      const { newCollectionName, createCollectionIsDisabled } = useCreateCollection(isLoading);
      newCollectionName.value = collectionName;

      // Assert
      expect(createCollectionIsDisabled.value).toBe(expectedValue);
    });
  });
});
