import { it, describe, expect, vi, beforeEach } from 'vitest';
import { useOutboundTemplates } from '@composables/FieldMapping';
import { computed, ref } from 'vue';
import { createPinia, setActivePinia } from 'pinia';

describe('useOutboundTemplates', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  it('initializes with correct default values', () => {
    // Arrange + Act
    const columns = computed(() => []);
    const {
      isLoading,
      outboundTemplates,
      selectedOutboundTemplate,
      outboundTemplateSheets,
      selectedOutboundSheet,
      outboundTemplateFields,
    } = useOutboundTemplates(columns, ref([]), ref([]), 1, ref([]));

    // Assert
    expect(isLoading.value).toBeFalsy();
    expect(outboundTemplateFields.value).toEqual([]);
    expect(outboundTemplates.value).toEqual([]);
    expect(outboundTemplateSheets.value).toEqual([]);
    expect(selectedOutboundTemplate.value).toBeUndefined();
    expect(selectedOutboundSheet.value).toBeUndefined();
  });
});
