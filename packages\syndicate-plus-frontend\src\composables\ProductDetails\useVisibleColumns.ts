import { computed, Ref } from 'vue';
import { ProductDetailsFilter, FilterField } from '@customTypes/Products';

export default function useVisibleColumns(columns: Ref<FilterField[]>, filter: Ref<ProductDetailsFilter>) {
  // Computed
  const allRequiredColumnNames = computed(() => columns.value.filter((x) => x.isRequired).map((x) => x.name));
  const visibleColumns = computed(() => filter.value.visibleColumns || allRequiredColumnNames.value);

  return { visibleColumns };
}
