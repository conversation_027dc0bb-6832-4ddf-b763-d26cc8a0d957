<template>
  <div
    class="c-trading-partner-card block h-[220px] relative group no-underline"
    :class="isConnected ? 'connected' : 'opaque'"
    @click="handleCardClick(subCatalogId, destinationId)"
  >
    <div class="image-wrapper flex items-center justify-center">
      <img v-if="logo" :src="logo" class="image" />
    </div>
    <div class="text-wrapper p-2 pt-3">
      <p class="card-name leading-none mb-1">
        {{ name }}
      </p>
      <p class="card-type text-grey-dark">
        {{ type }}
      </p>

      <span v-if="isFeatureEnabled('skus')" class="sku-count">
        {{ skus ? skus : '-' }}
        <q-tooltip>
          {{
            skus
              ? $tc('syndicate_plus.trading_partners.sku_count', { count: skus })
              : $t('syndicate_plus.trading_partners.no_skus')
          }}
        </q-tooltip>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from '@composables/useRouter';
import isFeatureEnabled from '@utils/isFeatureEnabled';
import { PropType } from 'vue';
const router = useRouter();

const props = defineProps({
  id: {
    type: String as PropType<Number | String>,
    default: '',
  },
  subCatalogId: {
    type: Number,
    default: null,
  },
  destinationId: {
    type: Number,
    default: null,
  },
  isConnected: {
    type: Boolean,
    default: false,
  },
  name: {
    type: String,
    default: undefined,
  },
  skus: {
    type: Number,
    default: undefined,
  },
  type: {
    type: String,
    default: undefined,
  },
  color: {
    type: String,
    default: 'secondary',
  },
  logo: {
    type: String,
    default: undefined,
  },
  isCoreCard: {
    type: Boolean,
    default: false,
  },
});

// Functions
function handleCardClick(subCatalogId: Number | null, destinationId: Number | null) {
  if (props.isCoreCard && props.id) {
    return router.goToPage('trading-partner-page', { tradingPartnerId: props.id });
  }
  if (subCatalogId != null && destinationId != null) {
    router.goToPage('syndication-page', { subCatalogId: subCatalogId, destinationId: destinationId });
  }
}
</script>

<style scoped lang="scss">
.c-trading-partner-card {
  border: 1px solid var(--color-grey-light);
  width: 193px;
  height: 169px;

  div {
    width: 100%;
  }

  &.connected {
    border: 1px solid var(--color-green);
    cursor: pointer;
  }

  &.opaque {
    opacity: 50%;
  }

  .text-wrapper {
    height: 69px;
    .card-name {
      font-size: 14px;
      font-weight: 600;
      word-wrap: break-word;
    }

    .card-type {
      font-size: 12px;
      font-weight: 400;
    }
  }
  .image-wrapper {
    height: 100px;
    border-bottom: 1px solid var(--color-grey-light);
    padding: 28px;

    .image {
      object-fit: contain;
      height: 100%;
      width: 100%;
      max-height: 100px;
    }
  }
}
.sku-count {
  right: 10px;
  bottom: 12px;
  font-weight: 600;
  background-color: var(--color-grey-light);
  color: var(--color-grey-darkest);
  width: fit-content;
  position: absolute;
  text-align: center;
  min-width: 20px;
  min-height: 20px;
  font-size: 12px;
  vertical-align: middle;
  line-height: 20px;
  padding-left: 3px;
  padding-right: 3px;
}
</style>
