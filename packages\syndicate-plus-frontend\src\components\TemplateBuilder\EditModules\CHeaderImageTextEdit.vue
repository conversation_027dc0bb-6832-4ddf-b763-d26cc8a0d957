<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <div class="flex flex-col justify-center">
      <div>
        <c-caption-angle-image-block
          v-model="module.data.image1"
          :dimensions="dimensions"
          :hide-caption="true"
          :max-alt-text-length="Validation.headerImageText.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url)"
        />
        <div class="m-10px">
          <q-input
            v-model="module.data.headline1"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.headline')"
            :maxlength="Validation.headerImageText.headline1.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data.headline1, 'headline1')"
          />
          <c-text-editor
            v-model="module.data.body1"
            :max-length="Validation.headerImageText.body1.maxLength"
            @on-field-drop="onDrop(module.data.body1, 'body1')"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, HeaderImageTextData, Dimension } from '@customTypes/Aplus';
import { CTextEditor, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Refs
const module = ref<ContentModule<HeaderImageTextData>>({} as ContentModule<HeaderImageTextData>);

// Variables
const dimensions = { width: 970, height: 600 } as Dimension;

// Functions
const handleUrlUpdate = (url: string) => {
  module.value.data.image1.angle = url;
  updateModel();
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, key: string) => {
  module.value.data[key] = fieldsStore.onDrop(value);
  updateModel();
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      image1: {
        angle: '',
        altText: '',
      },
      headline1: '',
      body1: '',
    } as HeaderImageTextData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
