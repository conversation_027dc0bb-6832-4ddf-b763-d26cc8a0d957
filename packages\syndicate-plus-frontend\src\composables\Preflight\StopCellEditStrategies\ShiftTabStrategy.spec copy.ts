import { vi, describe, it, expect } from 'vitest';
import { ShiftTabStrategy } from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';

describe('ShiftTabStrategy', () => {
  describe('getNextRowAndColumnId', () => {
    it('returns object with next row and column id', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 1;
      const currentColumnId = 3;
      const previousColumnId = 2;
      const strategy = new ShiftTabStrategy(rows, columns);
      vi.spyOn(strategy as any, 'getPreviousAvailableColumnId').mockReturnValue(previousColumnId);

      // Act
      const result = strategy.getNextRowAndColumnId(currentRowId, currentColumnId);

      // Assert
      expect(result).to.deep.equal({
        columnId: previousColumnId,
        rowId: currentRowId,
      } as TableCellCoordinates);
    });

    it('returns object with next row and column id if getPreviousAvailableColumnId returns -1', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 1;
      const currentColumnId = 3;
      const previousColumnId = -1;
      const lastAvailableColumn = 5;
      const previousAvailableRowId = 4;
      const strategy = new ShiftTabStrategy(rows, columns);
      vi.spyOn(strategy as any, 'getPreviousAvailableColumnId').mockReturnValue(previousColumnId);
      vi.spyOn(strategy as any, 'getLastAvailableColumnId').mockReturnValue(lastAvailableColumn);
      vi.spyOn(strategy as any, 'getPreviousAvailableRowId').mockReturnValue(previousAvailableRowId);

      // Act
      const result = strategy.getNextRowAndColumnId(currentRowId, currentColumnId);

      // Assert
      expect(result).to.deep.equal({
        columnId: lastAvailableColumn,
        rowId: previousAvailableRowId,
      } as TableCellCoordinates);
    });
  });
});
