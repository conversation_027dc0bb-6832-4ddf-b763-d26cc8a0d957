<template>
  <c-section class="c-media-mapping-tab-panel">
    <div v-if="loading" class="spinner">
      <c-spinner />
    </div>
    <div v-if="!loading">
      <div v-if="hasMediaMappings" class="row">
        <c-grid class="col-8 mb-4" cols="repeat(auto-fill, minmax(200px, 200px)">
          <c-media-slot
            v-for="(mediaSlot, index) in mediaSlots"
            :key="index"
            :media-slot="mediaSlot"
            :trading-partner-name="tradingPartnerName"
          />
        </c-grid>
        <div class="col-4 media-data">
          <c-media-specification :media-specification="mediaSpecification" class="mb-12" />
          <c-media-naming-override :media-naming-override="mediaNamingOverride" />
        </div>
      </div>
      <div v-else>
        <c-no-data
          src="nothing-to-see"
          image-height="195px"
          :title="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_title')"
          :text="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_message')"
        />
      </div>
    </div>
  </c-section>
</template>
<script setup lang="ts">
import { onBeforeMount, ref } from 'vue';
import { useRoute } from 'vue-router';
import { CMediaSpecification, CMediaNamingOverride, CMediaSlot } from '@components/MediaMapping';
import { CNoData } from '@components';
import { MediaMappingService } from '@services';
import { MediaSpecification, MediaSlot } from '@customTypes/MediaMappings';
import { useProductsStore, useAppInsightsStore } from '@stores';
import { PageName } from '@enums';
import { useHasMediaMappings } from '@composables/Settings/MediaMapping';

defineProps({
  tradingPartnerName: {
    type: String,
    default: '',
  },
});

const route = useRoute();

const { getDefaultMediaMappingImage } = useProductsStore();
const { setScreenName } = useAppInsightsStore();

// Refs
const loading = ref<boolean>(true);
const mediaSpecification = ref<MediaSpecification>();
const mediaNamingOverride = ref<string>();
const mediaSlots = ref<MediaSlot[]>([]);

// Composables
const { hasMediaMappings } = useHasMediaMappings(mediaSlots);

// Functions
const loadMediaMapping = async () => {
  loading.value = true;
  const destinationId = route.params.destinationId as string;
  const mediaMapping = await new MediaMappingService().getMediaMapping(destinationId);
  loading.value = false;
  if (mediaMapping) {
    mediaSpecification.value = mediaMapping.mediaSpecification;
    mediaNamingOverride.value = mediaMapping.imageFormatOverride;
    mediaSlots.value = mediaMapping.mediaSlots;
    if (getDefaultMediaMappingImage() !== undefined && mediaSlots.value.length > 0) {
      mediaSlots.value[0].imageSrc = getDefaultMediaMappingImage();
    }
  }
  loading.value = false;
};

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.MEDIA_MAPPINGS);
  await loadMediaMapping();
});
</script>

<style lang="scss" scoped>
$media-mapping-tab-panel-offset: 241px;

.c-media-mapping-tab-panel {
  height: calc(100vh - $media-mapping-tab-panel-offset);

  .spinner {
    margin: auto;
    width: min-content;
  }

  .media-data {
    display: block;
    margin: 0 auto;
    width: 250px;
    margin-left: 10px;

    :deep(.q-table--dense .q-table) {
      th {
        font-size: 16px;
      }
      td:first-child {
        font-weight: 600;
      }

      tr {
        height: 20px;
      }

      tbody td {
        font-size: 14px;
        height: 20px;
        padding-top: 0;
        padding-bottom: 0;
      }
    }
  }

  .filter-panel {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  :deep(.media-mapping-field .q-field__label) {
    top: 0 !important;
    background: var(--input-bg-color);
    padding: 0 4px;
    left: -4px;
    z-index: 1;
  }
}
</style>
