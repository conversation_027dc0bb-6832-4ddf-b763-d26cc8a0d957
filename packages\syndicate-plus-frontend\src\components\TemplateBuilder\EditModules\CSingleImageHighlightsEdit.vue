<template>
  <div class="mx-auto p-20px min-w-1200px w-1200px">
    <div class="flex flex-row flex-nowrap">
      <div class="m-10px">
        <c-caption-angle-image-block
          v-model="module.data.image"
          :dimensions="dimensions"
          :hide-caption="true"
          :max-alt-text-length="Validation.singleImageAndHighlights.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url)"
        />
      </div>
      <div class="middle-column m-10px">
        <q-input
          v-model="module.data.headline1"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.headline')"
          :maxlength="Validation.singleImageAndHighlights.headline1.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.headline1, 'headline1')"
        />
        <q-input
          v-model="module.data.subheadline1"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.subheadline')"
          :maxlength="Validation.singleImageAndHighlights.subheadline1.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.subheadline1, 'subheadline1')"
        />
        <c-text-editor
          v-model="module.data.body1"
          :max-length="Validation.singleImageAndHighlights.body1.maxLength"
          @on-field-drop="onDrop(module.data.body1, 'body1')"
        />
        <q-input
          v-model="module.data.subheadline2"
          v-bind="$inri.input"
          class="mt-10px"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.subheadline')"
          :maxlength="Validation.singleImageAndHighlights.subheadline2.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.subheadline2, 'subheadline2')"
        />
        <c-text-editor
          v-model="module.data.body2"
          :max-length="Validation.singleImageAndHighlights.body2.maxLength"
          @on-field-drop="onDrop(module.data.body2, 'body2')"
        />
        <q-input
          v-model="module.data.subheadline3"
          v-bind="$inri.input"
          class="mt-10px"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.subheadline')"
          :maxlength="Validation.singleImageAndHighlights.subheadline3.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.subheadline3, 'subheadline3')"
        />
        <c-text-editor
          v-model="module.data.body3"
          :max-length="Validation.singleImageAndHighlights.body3.maxLength"
          @on-field-drop="onDrop(module.data.body3, 'body3')"
        />
      </div>
      <div class="right-column m-10px">
        <q-input
          v-model="module.data.headline2"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.headline')"
          :maxlength="Validation.singleImageAndHighlights.headline2.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.headline2, 'headline2')"
        />
        <c-bullet-list
          v-model="module.data.bullets"
          :max-length="Validation.singleImageAndHighlights.bullets.maxLength"
          @update-model="updateModel"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, SingleImageHighlightsData, Dimension } from '@customTypes/Aplus';
import { CBulletList, CTextEditor, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Refs
const module = ref<ContentModule<SingleImageHighlightsData>>({} as ContentModule<SingleImageHighlightsData>);

// Variables
const dimensions = { width: 300, height: 300 } as Dimension;

// Functions
const handleUrlUpdate = (url: string) => {
  module.value.data.image.angle = url;

  updateModel();
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, moduleParameter) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data[moduleParameter] = result;
    updateModel();
  }
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      body1: '',
      body2: '',
      body3: '',
      headline1: '',
      headline2: '',
      subheadline1: '',
      subheadline2: '',
      subheadline3: '',
      bullets: [],
      image: {
        altText: '',
        angle: '',
      },
    } as SingleImageHighlightsData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>

<style lang="scss" scoped>
.middle-column {
  flex-grow: 2;
}

.right-column {
  flex-grow: 1;
}

:deep(.q-uploader__list) {
  overflow: hidden;
  padding: 0px;
}
</style>
