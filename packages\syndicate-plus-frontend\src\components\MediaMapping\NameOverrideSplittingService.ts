export class NameOverrideSplittingService {
  public static Split(input: string): string[] {
    // Use a regex to capture the desired patterns
    const placeholderPattern = '(\\${[A-Z_]+})';
    const underscorePattern = '(_)';
    const alphanumericPattern = '[a-zA-Z0-9]+';

    const combinedRegex = new RegExp(`${placeholderPattern}|${underscorePattern}|${alphanumericPattern}`, 'g');

    const matches = input.match(combinedRegex);
    return matches || [];
  }

  public static RemoveMarkings(input: string[]) {
    const startPattern = '\\$\\{';
    const contentPattern = '(.*?)';
    const endPattern = '\\}';

    const regex = new RegExp(startPattern + contentPattern + endPattern, 'g');

    const output = input.map((str) => str.replace(regex, '$1'));
    return output;
  }
}
