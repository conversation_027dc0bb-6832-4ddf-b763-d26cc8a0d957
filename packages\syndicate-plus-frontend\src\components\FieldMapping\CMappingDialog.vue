<template>
  <c-dialog
    class="c-dialog"
    :title="props?.title"
    :model-value="showDialog"
    :is-loading="props?.isLoading"
    hide-confirm-button
    hide-cancel-button
    @cancel="cancel"
  >
    <c-mapping-dialog-table :is-inbound="true" :rows="inboundRows" />
    <br />
    <c-mapping-dialog-table :is-inbound="false" :rows="outboundRows" :trading-partner-name="tradingPartnerName" />
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { ref, PropType, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useSubcatalogStore } from '@stores/SubcatalogStore';
import { InboundTemplateField, OutboundTemplateField } from '@customTypes';
import CMappingDialogTable from '@components/FieldMapping/CMappingDialogTable.vue';
import { watch } from 'vue';

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  inbounds: {
    type: Object as PropType<InboundTemplateField[]>,
    default: [] as InboundTemplateField[],
  },
  outbound: {
    type: Object as PropType<OutboundTemplateField>,
    default: {} as OutboundTemplateField,
  },
});

const route = useRoute();

const { fetchSubcatalog, getSubcatalog } = useSubcatalogStore();
const { showDialog, cancel } = useDialog();

const outboundRows = ref<Array<OutboundTemplateField>>([] as Array<OutboundTemplateField>);
const inboundRows = ref<Array<InboundTemplateField>>([] as Array<InboundTemplateField>);
const tradingPartnerName = ref('');

onMounted(async () => {
  if (!props.isLoading) {
    await initializeAllMappings();
  } else {
    watch(
      () => props.isLoading,
      async () => {
        if (!props.isLoading) {
          await initializeAllMappings();
        }
      }
    );
  }
});

async function initializeAllMappings() {
  const subCatalogId = parseInt(route.params.subCatalogId as string);
  await fetchSubcatalog(subCatalogId);
  tradingPartnerName.value = getSubcatalog(subCatalogId)?.name;

  if (props.inbounds?.length == 0) {
    inboundRows.value.push({} as InboundTemplateField);
  } else {
    props.inbounds.forEach((inbound) => {
      inboundRows.value.push({
        sourceFieldId: inbound.sourceFieldId,
        sourceFieldName: inbound.sourceFieldName,
        syndicationFieldName: inbound.syndicationFieldName,
        defaultValue: inbound.defaultValue,
        functions: inbound.functions,
        importanceType: inbound.importanceType,
        dataType: inbound.dataType?.toLocaleLowerCase(),
        format: inbound.format,
        minCharacterLength: inbound.minCharacterLength,
        maxCharacterLength: inbound.maxCharacterLength,
        parentOverrideField: inbound.parentOverrideField,
      } as InboundTemplateField);
    });
  }

  props?.outbound &&
    outboundRows.value.push({
      syndicationFieldId: props.outbound.syndicationFieldId,
      syndicationFieldName: adjustFieldName(props.outbound),
      targetFieldName: props.outbound.targetFieldName,
      defaultValue: props.outbound.defaultValue,
      functions: props.outbound.functions,
      importanceType: props.outbound.importanceType,
      dataType: props.outbound.dataType?.toLocaleLowerCase(),
      format: props.outbound.format,
      minCharacterLength: props.outbound.minCharacterLength,
      maxCharacterLength: props.outbound.maxCharacterLength,
      parentOverrideField: props.outbound.parentOverrideField,
    } as OutboundTemplateField);
}

const adjustFieldName = (field: OutboundTemplateField): string => {
  if (isSourceFieldNameValid(field)) {
    return field.syndicationFieldName;
  }

  if (isFieldIdValid(field)) {
    return field.syndicationFieldId;
  }

  return '';
};

const isFieldIdValid = (field: OutboundTemplateField) =>
  !(field?.syndicationFieldId == 'NOCOLUMN' || field?.syndicationFieldId == '');

const isSourceFieldNameValid = (field: OutboundTemplateField) =>
  !(field?.syndicationFieldName == 'NOCOLUMN' || field?.syndicationFieldName == '');
</script>
