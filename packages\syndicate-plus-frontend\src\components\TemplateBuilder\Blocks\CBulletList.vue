<template>
  <q-list bordered separator class="mt-10px">
    <q-item v-for="(_, index) in model" :key="index" v-ripple clickable>
      <q-item-section side>
        <q-btn label="X" @click="removeBullet(index)" />
      </q-item-section>
      <q-item-section avatar>
        <q-input
          v-model="model[index]"
          v-bind="$inri.input"
          hide-bottom-space
          :maxlength="maxLength"
          counter
          @keyup.enter="updateBullet"
          @blur="updateBullet"
          @drop="onDrop(model[index], index)"
        />
      </q-item-section>
    </q-item>
    <q-item v-ripple clickable>
      <q-item-section side>
        <q-btn disabled label="X" />
      </q-item-section>
      <q-item-section avatar>
        <q-input
          v-model="newBullet"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.enter_bullet')"
          :maxlength="maxLength"
          counter
          @keyup.enter="addBullet"
          @blur="addBullet"
          @drop="onDropNewBullet(newBullet)"
        />
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useFieldsTabStore } from '@stores/TemplateBuilder';

const props = defineProps({
  modelValue: {
    type: Array<string>,
    default: () => ({}),
  },
  maxLength: {
    type: Number,
    default: 100,
  },
});

const fieldsStore = useFieldsTabStore();

// Emits
const emit = defineEmits(['update-model']);

// Refs
const model = ref(props.modelValue);
const newBullet = ref('');

// Functions
const addBullet = (): void => {
  if (!newBullet.value?.trim()) {
    return;
  }

  model.value.push(newBullet.value);
  newBullet.value = '';
  emit('update-model');
};

const updateBullet = (): void => {
  emit('update-model');
};

const onDropNewBullet = (value: string): void => {
  newBullet.value = fieldsStore.onDrop(value);
  addBullet();
};

const onDrop = (value: string, index: number): void => {
  model.value[index] = fieldsStore.onDrop(value);
  updateBullet();
};

const removeBullet = (index: number): void => {
  model.value.splice(index, 1);
  emit('update-model');
};
</script>

<style scoped lang="scss">
:deep(.q-field__control) {
  min-width: auto !important;
}

:deep(.q-item.q-item-type) {
  align-items: baseline;
  height: 70px;
}

:deep(.q-field__messages) {
  display: flex;
  justify-content: center;
  margin-left: 10px;
}
</style>
