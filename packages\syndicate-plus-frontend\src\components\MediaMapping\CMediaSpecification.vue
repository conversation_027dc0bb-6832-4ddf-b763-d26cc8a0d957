<template>
  <div class="c-media-specification">
    <q-table
      class="media-specification-table"
      row-key="name"
      separator="none"
      dense
      flat
      :columns="columns"
      :rows="rows"
      hide-bottom
    />
  </div>
</template>
<script setup lang="ts">
import { MediaSpecification } from '../../types/MediaMappings/mediaSpecification';
import { computed } from 'vue';

const props = defineProps({
  mediaSpecification: {
    type: Object as () => MediaSpecification,
    default: () => ({}),
  },
});

const rows = computed(() => {
  const mediaSpec = props.mediaSpecification;
  const mediaSpecRows = [] as Array<Object>;
  if (Object.keys(mediaSpec)?.length) {
    Object.keys(mediaSpec).forEach(function (key) {
      mediaSpecRows.push({
        specifications: key,
        values: mediaSpec[key],
      });
    });
  }
  return mediaSpecRows;
});

const columns = [
  {
    name: 'specifications',
    field: 'specifications',
    label: 'specifications',
    align: 'left' as const,
    style: 'width: 120px !important, max-width: 146px',
  },
  {
    name: 'values',
    field: 'values',
    label: '',
    align: 'left' as const,
    style: 'width: 120px !important',
  },
];
</script>
