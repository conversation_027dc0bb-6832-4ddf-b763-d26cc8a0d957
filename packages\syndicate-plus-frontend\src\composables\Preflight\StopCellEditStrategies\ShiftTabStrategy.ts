import { IStopCellEditStrategy, BaseStrategy } from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';
import { PreflightColumnDto } from '@dtos';

export class ShiftTabStrategy extends BaseStrategy implements IStopCellEditStrategy {
  constructor(rows: any[], columns: PreflightColumnDto[]) {
    super(rows, columns);
  }

  getNextRowAndColumnId(currentRowId: number, currentColumnId: number): TableCellCoordinates {
    const previousColumnIdInCurrentRow = this.getPreviousAvailableColumnId(currentColumnId);
    const shouldGoToPreviousRow = previousColumnIdInCurrentRow < 0;
    if (shouldGoToPreviousRow) {
      return {
        columnId: this.getLastAvailableColumnId(),
        rowId: this.getPreviousAvailableRowId(currentRowId),
      } as TableCellCoordinates;
    } else {
      return {
        columnId: previousColumnIdInCurrentRow,
        rowId: currentRowId,
      } as TableCellCoordinates;
    }
  }
}
