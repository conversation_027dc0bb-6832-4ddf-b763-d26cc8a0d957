parameters:
  - name: serviceConnection
    displayName: Azure Service Connection
    type: string
    
  - name: appName
    displayName: Application name
    type: string

  - name: stack
    displayName: Stack
    type: string

steps:    
  - task: AzureCLI@2
    displayName: "Get Deployment Token"
    inputs:
      azureSubscription: ${{ parameters.serviceConnection }}
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        secret=$(az staticwebapp secrets list --name ${{ parameters.appName }} --query "properties.apiKey" --output tsv)
        echo "##vso[task.setvariable variable=dep_token]$secret"

  - task: AzureStaticWebApp@0
    displayName: Deploy App
    inputs:
      skip_app_build: true
      app_location: /SyndicatePlusFrontendArtifact-${{ parameters.stack }}/dist
      azure_static_web_apps_api_token: $(dep_token)
      output_location: ''
      workingDirectory: $(Pipeline.Workspace)
      api_location: ''