import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';
import { createI18n } from 'vue-i18n';
import { MediaSlot } from '@customTypes/MediaMappings/mediaSlot';
import CMediaSlot from '@components/MediaMapping/CMediaSlot.vue';

describe('CMediaSlot', () => {
  const i18n = createI18n({
    allowComposition: true,
  });

  it('has tags displayed', async () => {
    // Arrange

    const mockMediaSlot: MediaSlot = {
      inriverTag: 'inriverTag1',
      tradingPartnerTag: 'tradingPartnerTag1',
    };

    const wrapper = mount(CMediaSlot, {
      props: { mediaSlot: mockMediaSlot },
      global: {
        stubs: { 'q-icon': true },
        plugins: [i18n],
      },
    });

    // Act & Assert
    expect(wrapper.text()).toContain('inriverTag1');
    expect(wrapper.text()).toContain('tradingPartnerTag1');
  });

  it('has image displayed', async () => {
    // Arrange
    const mockMediaSlot: MediaSlot = {
      inriverTag: 'inriverTag1',
      tradingPartnerTag: 'tradingPartnerTag1',
      imageSrc: 'sampleImage',
    };

    const wrapper = mount(CMediaSlot, {
      props: { mediaSlot: mockMediaSlot },
      global: {
        stubs: { 'q-icon': true },
        plugins: [i18n],
      },
    });

    // Act & Assert
    const image = wrapper.find('img');
    expect(image.exists()).toBe(true);
  });

  it('has placeholder displayed', async () => {
    // Arrange
    const mockMediaSlot: MediaSlot = {
      inriverTag: 'inriverTag1',
      tradingPartnerTag: 'tradingPartnerTag1',
    };

    const wrapper = mount(CMediaSlot, {
      props: { mediaSlot: mockMediaSlot },
      global: {
        stubs: { 'q-icon': true },
        plugins: [i18n],
      },
    });

    // Act & Assert
    const placeHolder = wrapper.find('q-icon-stub');
    expect(placeHolder.exists()).toBe(true);
  });
});
