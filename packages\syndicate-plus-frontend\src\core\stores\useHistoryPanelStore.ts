import { ref, computed } from 'vue';
import { DynamicOutputType, LongRunningJobStatuses } from '@core/enums';
import { History } from '@core/interfaces';
import { notify } from '@inriver/inri';

export function useHistoryPanelStore(t?: (key: string) => string) {
  const selectedRows = ref<History[]>([]);

  const isDeleteButtonVisible = computed(() => selectedRows.value.length > 0);
  const isDisplayErrorsVisible = computed(
    () => selectedRows.value.length === 1 && selectedRows.value[0].state === LongRunningJobStatuses.ERROR
  );
  const isDownloadButtonVisible = computed(() => !!selectedRows.value.length && !!selectedRows.value[0].fileName);

  const isDownloadResourcesButtonVisible = computed(
    () => !!selectedRows.value.length && !!selectedRows.value[0].zipFileName
  );

  const isDetailsButtonVisible = computed(
    () => selectedRows.value.length === 1 && selectedRows.value[0].outputName === DynamicOutputType.API
  );

  function displayErrors() {
    if (!selectedRows.value.length) {
      return;
    }
    const row = selectedRows.value[0];
    if (row.state !== LongRunningJobStatuses.ERROR) {
      return;
    }
    const errorMessage = row.message;
    if (errorMessage) {
      notify.info(errorMessage, { timeout: 0, position: 'center' });
    } else if (t) {
      notify.info(t('core.trading_partners.history.no_error_message_available'), { timeout: 0, position: 'center' });
    }
  }

  return {
    selectedRows,
    isDeleteButtonVisible,
    isDisplayErrorsVisible,
    isDownloadButtonVisible,
    isDetailsButtonVisible,
    isDownloadResourcesButtonVisible,
    displayErrors,
  };
}
