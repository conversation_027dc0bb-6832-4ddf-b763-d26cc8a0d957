<template>
  <c-layout-with-back-button :title="t('syndicate_plus.imports')">
    <template #left-sidebar>
      <c-tile-btn
        :icon="filterButtonIcon"
        :icon-size="20"
        :tooltip-left="filterButtonTooltip"
        @click="
          logEventAndClick(
            isFilterPanelOpen ? ActionName.CLOSE_FILTER_PANEL : ActionName.OPEN_FILTER_PANEL,
            toogleFilterPanel
          )
        "
      />
    </template>
    <template #right-sidebar>
      <c-tile-btn
        v-if="isDetailsButtonVisible"
        icon="mdi-text-box-outline"
        :tooltip-left="$t('syndicate_plus.syndication.details')"
        :icon-size="20"
        @click="logEventAndClick(ActionName.GO_TO_IMPORT_DETAILS, goToImportRow())"
      />
    </template>
    <c-product-import-filter
      v-show="isFilterPanelOpen"
      v-on-click-outside="closeFilterPanel"
      :is-fetching="productImportStore.isFetching"
      @apply-filter="applyFilter"
    />
    <c-section>
      <q-table
        ref="tableRef"
        v-model:selected="selectedRows"
        class="imports-table sticky-table-header"
        :pagination="pagination"
        dense
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        :columns="columns"
        :rows="imports"
        row-key="statsId"
        :rows-per-page-options="[0]"
        separator="cell"
        hide-bottom
        virtual-scroll
        :loading="productImportStore.isFetching"
        :virtual-scroll-item-size="31"
        :virtual-scroll-sticky-size-start="31"
        @virtual-scroll="onScroll"
        @row-click="onRowClick"
        @row-dblclick="
          (_, row) => logEventAndClick(ActionName.GO_TO_IMPORT_DETAILS, onRowDoubleClick(_, row, goToImportRow))
        "
      >
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
      </q-table>
    </c-section>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, computed, onUnmounted, nextTick } from 'vue';
import { vOnClickOutside } from '@vueuse/components';
import { useI18n } from 'vue-i18n';
import { useAppInsightsStore } from '@stores';
import { useProductImportStore } from '@stores/ProductImport';
import { PageName, ActionName } from '@enums';
import { ImportData } from '@customTypes/ImportData';
import { columns } from '@const';
import { useRouter, useFilterPanel } from '@composables';
import { useFilterButton } from '@composables/Buttons';
import { useSingleRowSelect } from '@composables/Common';
import { CProductImportFilter } from '@components/ProductImport/Filters';
import { ProductImportFilter } from '@customTypes/Products';

// Computed
const isDetailsButtonVisible = computed(() => !!selectedRows.value?.length);
const imports = computed(() => productImportStore.imports);

// Composables
const router = useRouter();
const { setScreenName, logEventAndClick } = useAppInsightsStore();
const productImportStore = useProductImportStore();
const { t } = useI18n();
const { selectedRows, onRowClick, onRowDoubleClick } = useSingleRowSelect<ImportData>();
const { isFilterPanelOpen, closeFilterPanel, toogleFilterPanel } = useFilterPanel();
const { filterButtonIcon, filterButtonTooltip } = useFilterButton(useI18n, null as any, isFilterPanelOpen);

// Variables
const pagination = {
  page: 1,
  rowsPerPage: 0,
};

// Functions
const goToImportRow = () => {
  !!isDetailsButtonVisible.value && router.goToPage('import-details', { id: selectedRows.value[0].statsId });
};

const onScroll = async (details): Promise<void> => {
  const { to, ref } = details;
  const isPageBottom = to === imports.value.length - 1;
  if (!productImportStore.isFetching && !productImportStore.isLastPage && isPageBottom) {
    await productImportStore.fetchImports();
    nextTick(() => {
      ref.refresh();
    });
  }
};

const applyFilter = async (filter: ProductImportFilter): Promise<void> => {
  selectedRows.value = [];
  await productImportStore.applyFilter(filter);
};

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.IMPORTS_PAGE);
  await productImportStore.fetchImports();
});

onUnmounted(() => productImportStore.reset());
</script>

<style lang="scss" scoped>
.imports-table {
  max-height: calc(100vh - 145px);
  margin-bottom: 200px;
}
</style>
