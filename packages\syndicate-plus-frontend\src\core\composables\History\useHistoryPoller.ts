import { useInterval } from '@composables';
import { useHistoryStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { LongRunningJobStatuses } from '@core/enums';
import { onUnmounted } from 'vue';

export default function useHistoryPoller(tradingPartnerId: string) {
  // 720 retries with 5 seconds interval => retry for one hour
  const attempts = 720;
  const intervalMs = 5000;

  const historyStore = useHistoryStore();

  // Refs
  const { history } = storeToRefs(historyStore);
  const { startInterval, stopInterval } = useInterval(attempts, intervalMs, refreshData);

  // Functions
  async function refreshData() {
    await historyStore.fetchDataSilently(tradingPartnerId);
    if (!hasNewUnfinishedJobs()) {
      stopInterval();
    }
  }

  const startPolling = async () => {
    await historyStore.fetch(tradingPartnerId, true);
    hasNewUnfinishedJobs() && startInterval();
  };

  const hasNewUnfinishedJobs = (): boolean => {
    const currentDate = new Date();
    const oneHourAgoDate = new Date(currentDate.getTime() - 60 * 60 * 1000);

    return history.value?.some(
      (historyRow) =>
        (historyRow.state === LongRunningJobStatuses.RUNNING || historyRow.state === LongRunningJobStatuses.QUEUED) &&
        new Date(historyRow.lastRun) > oneHourAgoDate
    );
  };

  onUnmounted(() => stopInterval());

  return { startPolling };
}
