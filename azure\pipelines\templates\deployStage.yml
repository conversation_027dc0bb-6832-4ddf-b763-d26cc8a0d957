parameters:
  - name: stack
    type: string

  - name: "disableApplicationDeploy"
    type: "boolean"

stages:
  - stage: "${{ parameters.stack }}"
    variables:
      - template: "../variables/${{ parameters.stack }}.yml"
    displayName: "${{ parameters.stack }}"
    dependsOn:
      - ${{ variables.dependsOn }}
      - ${{ variables.dependsOnSecond }}
    jobs:
      - template: "deployJobs.yml"
        parameters:
          serviceConnection: ${{ variables.serviceConnection }}
          stack: ${{ parameters.stack }}
          appName: pmc2-${{ variables.region }}-${{ variables.stackType }}-stapp-${{ variables.productName }}
          environment: pmc2-${{ variables.region }}-${{ variables.stackType }}-stapp-${{ variables.productName }}
          disableApplicationDeploy: ${{ parameters.disableApplicationDeploy }}
