import { Ref } from 'vue';
import { CollectionService } from '@services';

export default function useCreateCollectionActions(isLoading: Ref<boolean>) {
  // Functions
  const createCollection = async (tradingPartnerName: string, name: string) => {
    isLoading.value = true;
    const collectionId = await CollectionService.createCollection(tradingPartnerName, name);
    isLoading.value = false;

    return collectionId;
  };

  return { createCollection };
}
