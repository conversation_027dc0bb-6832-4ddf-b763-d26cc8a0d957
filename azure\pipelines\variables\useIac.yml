variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'use'
  stackType: 'iac'

  # Deploy dependencies
  dependsOn: 'Build_useIac'
  dependsOnSecond: 'Build_useIac'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'pmc2-use-iac-stapp-syndicate-plus-frontend'

  auth0Domain: 'inriverdev.eu.auth0.com'
  auth0ClientId: 'nbL2G6DGbSzuA3PHlw8gA9WjCBtCwOUz'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: ''

  allowMissingOrgMapping: true
    
  # Application insights instrumentation key
  instrumentationKey: '9ed1589e-e5d6-4d92-8a56-46df6f3d6e83'