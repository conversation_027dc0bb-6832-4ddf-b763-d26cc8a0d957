<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog"
    :title="$t('syndicate_plus.syndication.outbox.manage_scheduled_syndications')"
    hide-confirm-button
    @cancel="cancel"
  >
    <q-inner-loading :showing="isLoading" color="primary" class="z-10 inner-loading">
      <c-spinner size="40" class="spinner" />
    </q-inner-loading>
    <c-confirm-delete-dialog
      v-if="showConfirmDeleteDialog"
      v-model:show="showConfirmDeleteDialog"
      :text="
        t('syndicate_plus.syndication.outbox.delete_scheduled_syndication_confirm', { jobName: jobToDelete?.taskName })
      "
      @confirm="() => logEventAndClick(ActionName.DELETE_SCHEDULED_SYNDICATION, onConfirmDelete())"
    />
    <q-table
      :pagination="{
        page: 1,
        rowsPerPage: 0,
      }"
      flat
      dense
      hide-bottom
      separator="cell"
      :columns="sheduledSyndicationsTableColumns"
      :rows="scheduledSyndications"
      :hide-header="false"
    >
      <template #body-cell-actions="scope">
        <q-td>
          <a class="action-link" @click="onDeleteClick(scope.row)">
            {{ $t('syndicate_plus.common.delete') }}
          </a>
        </q-td>
      </template>
    </q-table>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { onBeforeMount, ref } from 'vue';
import { ActionName } from '@enums';
import { useAppInsightsStore } from '@stores';
import { ApiSyndicateHttpService } from '@httpservices';
import { sheduledSyndicationsTableColumns } from '@const';
import { ScheduledSyndication } from '@customTypes';
import { CConfirmDeleteDialog } from '@components/Shared';

const props = defineProps({
  destinationId: {
    type: Number,
    required: true,
  },
});

const { t } = useI18n();

// Refs
const isLoading = ref<boolean>(false);
const showConfirmDeleteDialog = ref<boolean>(false);
const scheduledSyndications = ref<ScheduledSyndication[]>([]);
const jobToDelete = ref<ScheduledSyndication>();

// Composables
const { showDialog, cancel } = useDialog();
const { logEventAndClick } = useAppInsightsStore();

// Functions
const fetch = async () => {
  isLoading.value = true;
  try {
    scheduledSyndications.value = await ApiSyndicateHttpService.getScheduledSyndications(props.destinationId, 1000, 1);
  } finally {
    isLoading.value = false;
  }
};

const onDeleteClick = (job: ScheduledSyndication) => {
  jobToDelete.value = job;
  showConfirmDeleteDialog.value = true;
};

const onConfirmDelete = async () => {
  if (!jobToDelete.value) {
    return;
  }

  isLoading.value = true;
  try {
    await ApiSyndicateHttpService.delete(jobToDelete.value.scheduledId);
  } finally {
    isLoading.value = false;
    showConfirmDeleteDialog.value = false;
  }

  await fetch();
};

// Lifecycle methods
onBeforeMount(async () => fetch());
</script>

<style scoped lang="scss">
.action-link {
  cursor: pointer;
  text-decoration: underline;
}
</style>
