import { RelationLink, RelationLinkResponse } from '@core/interfaces';
import { portalFetch, proxyPortalFetch } from '@utils';

export async function fetchEntityTypeRelations(
  entityType: string,
  direction: 'inbound' | 'outbound'
): Promise<RelationLink[]> {
  const url = `/api/relationtype?entityTypeId=${entityType}&direction=${direction}`;
  const fetch = import.meta.env.DEV ? proxyPortalFetch : portalFetch;
  const response = await fetch(url);
  const links = response?.ok ? ((await response.json()) as RelationLinkResponse[]) : [];

  return links.map((x) => {
    return {
      id: x.Id,
      sourceEntityTypeId: x.SourceEntityTypeId,
    } as RelationLink;
  });
}
