import { portalFetch, proxyPortalFetch } from '@utils';
import { FunctionModel } from '@core/interfaces';

export const fetchFunctions = async (): Promise<FunctionModel[]> => {
  const url = `/api/syndication/customfunctions/getall`;
  const errorMessage = `Error fetching functions`;
  try {
    const response = import.meta.env.DEV ? await proxyPortalFetch(url) : await portalFetch(url);
    if (!response) {
      console.error(errorMessage);
      throw new Error(errorMessage);
    }
    return await response.json();
  } catch (error) {
    console.error(errorMessage, error);
    throw new Error(errorMessage);
  }
};

export const validateCustomFunction = async (script: string): Promise<string> => {
  const url = '/api/syndication/customfunctions/parse';
  const errorMessage = 'could not parse script';
  try {
    const response = import.meta.env.DEV
      ? await proxyPortalFetch(url, { method: 'POST', body: JSON.stringify(script) })
      : await portalFetch(url, { method: 'POST', body: JSON.stringify(script) });
    return response === null ? '' : await response.json();
  } catch (error) {
    console.error(errorMessage, error);
    return errorMessage;
  }
};

export const saveCustomFunction = async (functionModel: FunctionModel): Promise<string> => {
  const url = '/api/syndication/customfunctions';
  const errorMessage = 'could not save custom function';
  try {
    const response = import.meta.env.DEV
      ? await proxyPortalFetch(url, { method: 'POST', body: JSON.stringify(functionModel) })
      : await portalFetch(url, { method: 'POST', body: JSON.stringify(functionModel) });
    if (response.status === 409) {
      return `a function named ${functionModel.Name} already exists`;
    }
    return response?.ok ? '' : errorMessage;
  } catch (error) {
    console.error(errorMessage, error);
    return errorMessage;
  }
};

export const deleteCustomFunction = async (functionId: number): Promise<number | null> => {
  const url = `/api/syndication/customfunctions/delete/${functionId}`;
  const errorMessage = 'error deleting custom function';
  try {
    const response = import.meta.env.DEV
      ? await proxyPortalFetch(url, { method: 'DELETE' })
      : await portalFetch(url, { method: 'DELETE' });
    return response?.status;
  } catch (error) {
    console.error(errorMessage, error);
    return null;
  }
};
