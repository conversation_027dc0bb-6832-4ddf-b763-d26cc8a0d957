import { vi, describe, it, expect } from 'vitest';
import { EnterStrategy } from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';

describe('EnterStrategy', () => {
  describe('getNextRowAndColumnId', () => {
    it('returns object with next row and column id', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 1;
      const nextRowId = 2;
      const currentColumnId = 2;
      const strategy = new EnterStrategy(rows, columns);
      vi.spyOn(strategy as any, 'getNextAvailableRowId').mockReturnValue(nextRowId);

      // Act
      const result = strategy.getNextRowAndColumnId(currentRowId, currentColumnId);

      // Assert
      expect(result).to.deep.equal({
        columnId: currentColumnId,
        rowId: nextRowId,
      } as TableCellCoordinates);
    });
  });
});
