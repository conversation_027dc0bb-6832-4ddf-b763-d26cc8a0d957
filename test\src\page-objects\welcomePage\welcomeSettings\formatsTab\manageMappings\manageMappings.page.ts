import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class ManageMappingsPage extends BasePage {
  private page: Page;
  readonly table: Locator;
  readonly mappingName = (text: string): Locator => this.page.getByRole('cell', { name: text, exact: true });
  readonly addMapping: Locator;
  readonly editMapping: Locator;
  readonly deleteMapping: Locator;
  readonly deleteMappingNotification = (notificationText: string): Locator =>
    this.page.locator('.q-notification__message').filter({ hasText: notificationText });
  // Add mapping page
  readonly mappingNameInput: Locator;
  readonly entityTypeDropDown = (name: string): Locator => this.page.getByText(name, { exact: true });
  readonly saveNewMapping: Locator;
  // Edit mapping page
  readonly saveButton: Locator;
  readonly cancelButton: Locator;
  readonly mapAutoButton: Locator;
  readonly unmapButton: Locator;
  readonly goBackButton: Locator;
  readonly fieldsDropdown: Locator;
  readonly source = (fieldName: string): Locator => this.page.locator('.source').filter({ hasText: fieldName });
  readonly target = (target: string): Locator => this.page.locator('.target').filter({ hasText: target });
  readonly getRow = (fieldName: string): Locator => this.page.getByRole('row', { name: fieldName }).first();
  readonly saveChangesDialog: Locator;
  // leave dialog locators
  readonly dialogHeader: Locator;
  readonly leaveDialogOk: Locator;
  readonly leaveDialogCancel: Locator;
  readonly dialogCloseButton: Locator;

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.table = this.page.locator('.q-table');
    this.addMapping = page.getByLabel('add mapping');
    this.editMapping = page.getByLabel('edit');
    this.deleteMapping = page.getByLabel('delete');
    this.mappingNameInput = page.getByLabel('name');
    this.saveNewMapping = page.getByLabel('save mapping');

    this.saveButton = page.getByLabel('save');
    this.cancelButton = page.getByLabel('cancel');
    this.mapAutoButton = page.getByLabel('map automatically');
    this.unmapButton = page.getByLabel('unmap source field');
    this.goBackButton = page.getByLabel('go back');
    this.fieldsDropdown = page.locator('.q-item__section').getByText('fields');
    this.saveChangesDialog = page.getByText('changes saved successfully');
    this.leaveDialogOk = page.getByRole('button', { name: 'ok' });
  }

  async deleteMappingIfExists(name: string): Promise<void> {
    const mappingExists = await this.mappingName(name).isVisible();
    await this.table.waitFor({ state: 'visible' });
    if (mappingExists) {
      await this.mappingName(name).click();
      await this.deleteMapping.click();
    }
  }
}
