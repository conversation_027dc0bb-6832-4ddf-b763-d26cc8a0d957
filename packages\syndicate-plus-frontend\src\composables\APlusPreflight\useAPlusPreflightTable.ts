import { ref } from 'vue';
import { PreflightService } from '@services';
import { EventType } from '@enums';
import { APlusTemplate } from '@customTypes/Aplus';
import { useAplusColumnsBuilder } from '@composables/APlusPreflight';

export default function useAPlusPreflightTable(emit: any, subCatalogId: number, destinationId: number) {
  // Refs
  const rows = ref<Array<any>>([]);
  const isLoading = ref(false);
  const streamId = ref<string>('');

  // Composables
  const { buildColumns, columns } = useAplusColumnsBuilder(destinationId);

  // Functions
  const performPreflight = async (selectedTemplate: APlusTemplate) => {
    if (!selectedTemplate || !Object.keys(selectedTemplate).length) {
      return;
    }

    isLoading.value = true;
    buildColumns(selectedTemplate.templateName);
    streamId.value = await PreflightService.connectToEventStream((receivedData) =>
      handleReceivedEvent(receivedData, selectedTemplate)
    );
  };

  const handleReceivedEvent = async (receivedData: string, selectedTemplate: APlusTemplate) => {
    const parsedEvents = PreflightService.parseEventObjectsFromEventLines(receivedData);
    const sessionEstablished = parsedEvents.find((x) => x.event === EventType.UUID);
    const sessionFinished = parsedEvents.find((x) => x.event === EventType.Finished);
    const sessionExport = parsedEvents.find((x) => x.event === EventType.Export);

    if (sessionEstablished) {
      await PreflightService.performAPlusPreflight(subCatalogId, destinationId, selectedTemplate, streamId.value);
    }

    if (sessionExport) {
      parsedEvents
        .filter((e) => e.event === EventType.Export)
        .forEach((e) => {
          buildRows(e.data);
        });
    }

    if (sessionFinished) {
      !hasErrors() && emit('on-enable-syndicate-button');
      await finishPreflight();
    }
  };

  const hasErrors = () => !!rows.value.find((x) => !!Object.keys((x as any).errors).length);

  const finishPreflight = async () => {
    isLoading.value = false;
    await PreflightService.closeStream();
  };

  const buildRows = (dataEvents: []): void => {
    dataEvents.forEach((x) => rows.value.push(x));
  };

  return { isLoading, columns, rows, performPreflight };
}
