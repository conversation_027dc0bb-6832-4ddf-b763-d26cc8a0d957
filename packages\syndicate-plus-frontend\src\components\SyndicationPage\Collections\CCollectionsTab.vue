<template>
  <c-section>
    <div v-if="isPageLoading" class="spinner">
      <c-spinner />
    </div>
    <div v-else-if="collections?.length">
      <c-collections-table
        data-id="collections-table"
        :collections="collections"
        :is-loading="isTableLoading"
        :last-page="lastPage"
        @fetch="getCollections"
        @on-row-click="onCollectionSelected"
        @go-to-collection="navigateToCollection"
      />
    </div>
    <div v-else>
      <c-no-data
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.collections.no_data.no_collections_title')"
        :text="$t('syndicate_plus.collections.no_data.no_collections_message')"
      />
    </div>
  </c-section>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, onUnmounted, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useI18n } from 'vue-i18n';
import { useAppInsightsStore } from '@stores';
import { useCollectionsStore } from '@stores/CollectionsStore';
import { CCollectionsTable } from '@components/SyndicationPage/Collections/index';
import { PageName } from '@enums';
import { CollectionDetails } from '@customTypes/CollectionDetails';
import { useEmitter } from '@composables';
import { notify } from '@inriver/inri';
import { CNoData } from '@components';

const props = defineProps({
  tradingPartnerName: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(['on-row-click', 'go-to-collection-page']);
defineExpose({ deleteCollection });

const { setScreenName } = useAppInsightsStore();
const collectionsStore = useCollectionsStore();

const { collections, isLoading, lastPage } = storeToRefs(collectionsStore);

// Refs
const isPageLoading = ref(false);
const selectedCollection = ref<CollectionDetails | null>();

// Computed
const isTableLoading = computed(() => isLoading.value);

// Composables
const emitter = useEmitter();
const { t } = useI18n();

// Functions
const getCollections = async () => {
  await collectionsStore.fetchCollections(props.tradingPartnerName);
};

const clearStoreAndFetchCollections = async () => {
  collectionsStore.clearStore();
  await collectionsStore.fetchCollections(props.tradingPartnerName);
};

const onCollectionSelected = (collection: CollectionDetails) => {
  selectedCollection.value = collection;
  emits('on-row-click', collection);
};

async function deleteCollection() {
  if (!selectedCollection.value) {
    return;
  }

  const collectionName = selectedCollection.value.name;
  const response = await collectionsStore.deleteCollection(selectedCollection.value);

  if (response?.type === 'ok' && response?.message === 'Row deleted') {
    emitter.emit('collection-deleted', selectedCollection.value);
    notify.success(t('syndicate_plus.collections.delete_collection_success', { collectionName }), {
      position: 'bottom-right',
    });
  }

  await clearStoreAndFetchCollections();
}

const navigateToCollection = (collection: CollectionDetails): void => {
  emits('go-to-collection-page', collection);
};

// Events
emitter.on('collection-created', async () => await clearStoreAndFetchCollections());
emitter.on('collection-updated', async () => await clearStoreAndFetchCollections());
emitter.on('collection-renamed', async () => await clearStoreAndFetchCollections());
emitter.on('delete-collection', async () => await deleteCollection());

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.COLLECTIONS);
  isPageLoading.value = true;
});

onUnmounted(() => {
  emitter.all.delete('collection-created');
  emitter.all.delete('collection-updated');
  emitter.all.delete('collection-renamed');
});

watch(
  () => props.tradingPartnerName,
  async () => {
    if (props.tradingPartnerName) {
      isPageLoading.value = true;
      await clearStoreAndFetchCollections();
      isPageLoading.value = false;
    }
  }
);
</script>

<style lang="scss" scoped>
.spinner {
  margin: auto;
  width: min-content;
}
</style>
