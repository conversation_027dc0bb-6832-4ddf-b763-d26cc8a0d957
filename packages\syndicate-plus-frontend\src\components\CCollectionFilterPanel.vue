<template>
  <div class="filter-panel">
    <q-tabs
      v-model="tab"
      class="bg-white text-grey-7"
      active-color="primary"
      active-class="active-tab"
      indicator-color="transparent"
      align="justify"
      no-caps
    >
      <q-tab :name="FilterTabNames.VIEW_TAB" :label="$t('syndicate_plus.common.view.views')" />
    </q-tabs>
    <q-tab-panels v-model="tab">
      <q-tab-panel :name="FilterTabNames.VIEW_TAB" class="panel-content p-0">
        <c-separated-expansion-item
          :is-expanded="expandedProductTypes"
          :label="$t('syndicate_plus.syndication.view.product_presentation')"
        >
          <c-filter-radio-options-section
            :options="filterOptions"
            :selected-item="selectedGroupByViewRef"
            @apply-filter="applyGroupByFilter"
          />
        </c-separated-expansion-item>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, ComputedRef } from 'vue';
import { FilterFieldOption } from '@customTypes';
import { useCollectionProductsStore } from '@stores/CollectionsStore';
import { getViewGroupByOptions, getDefaultGroupByOption } from '@const/groupByOptions';
import { useRoute } from 'vue-router';
import { CFilterRadioOptionsSection } from '@components';
import { CSeparatedExpansionItem } from '@components/Shared';
import deepEqual from '@utils/deepEqual';
import { FilterTabNames } from '@enums';

// Composables
const route = useRoute();
const { getFilter, applyGroupByFilter: applyGroupBy } = useCollectionProductsStore();

// Computed fields
const filterOptions: ComputedRef<FilterFieldOption[]> = computed(() => {
  return getViewGroupByOptions();
});

// Refs
const tab = ref(FilterTabNames.VIEW_TAB);
const expandedProductTypes = ref(true);
const selectedGroupByViewRef = ref<FilterFieldOption>(
  getFilter().groupBy !== undefined ? getFilter().groupBy : getDefaultGroupByOption()
);

// Variables
const { collectionId } = route.params;
let lastFilter: FilterFieldOption | undefined;

// Functions
function applyGroupByFilter(filter: FilterFieldOption): void {
  selectedGroupByViewRef.value = filter;
  if (!deepEqual(filter, lastFilter)) {
    applyGroupBy(filter, collectionId as string);
    lastFilter = filter;
  }
}
</script>

<style lang="scss" scoped>
.filter-panel {
  position: absolute;
  width: 400px;
  height: calc(100vh - 52.5px);
  z-index: 1;
  background-color: var(--on-primary-color);
  box-shadow: 2px 0 10px 0 var(--color-border);

  .panel-content {
    height: calc(100vh - 175px);
  }
}
</style>
