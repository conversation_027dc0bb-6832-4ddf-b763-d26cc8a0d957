import { computed, Ref } from 'vue';
import { ProductOutboxRow } from '@customTypes';
import { OutboxStatus, SyndicationPageTabNames } from '@enums';

export default function useDownloadButton(
  useI18n: Function,
  tab: Ref<SyndicationPageTabNames>,
  selectedRow: Ref<ProductOutboxRow | undefined>
) {
  // Variables
  const { t } = useI18n();

  // Computed
  const isDownloadButtonVisible = computed(() => tab.value === SyndicationPageTabNames.Outbox && !!selectedRow.value);

  const downloadButtonDisabledState = computed(() => {
    if (selectedRow?.value?.status && selectedRow.value.status.outboxStatus === OutboxStatus.CANCELED) {
      return { disabled: true, tooltip: t('syndicate_plus.syndication.export_canceled') };
    }
    if (selectedRow?.value?.status && selectedRow.value.status.outboxStatus != OutboxStatus.COMPLETED) {
      return { disabled: true, tooltip: t('syndicate_plus.syndication.export_incomplete') };
    }
    if (selectedRow?.value?.passedRecords === 0) {
      return { disabled: true, tooltip: t('syndicate_plus.syndication.export_failed') };
    }
    if (!selectedRow?.value?.fileLink) {
      return { disabled: true, tooltip: '' };
    }
    return { disabled: false, tooltip: t('syndicate_plus.syndication.download') };
  });

  return { isDownloadButtonVisible, downloadButtonDisabledState };
}
