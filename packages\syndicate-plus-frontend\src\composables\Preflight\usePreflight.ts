import { computed, ref, Ref } from 'vue';
import { PreflightService, GridService } from '@services';
import { PreflightColumnHelper } from '@services/helper';
import { EventType, HeaderType } from '@enums';
import preflightInsights from '@insights/PreflightInsights';
import { Field } from '@customTypes';
import { HttpAuth0Client } from '@httpservices';
import { useEnvironmentSettings } from '@composables';
import { ProductFilterDto } from '@dtos';
import { populateProblemMetadata } from '@services/Preflight/ExportEventService';

export function usePreflight(
  groupBy: string,
  productFilter: Ref<ProductFilterDto>,
  searchValue: Ref<string | undefined>,
  destinationId: number,
  subCatalogId: number,
  allProductsSelected: boolean,
  productsIdsArray: Ref<string[]>
) {
  // Refs
  const loading = ref<boolean>(false);
  const columns = ref<Array<any>>([]);
  const streamId = ref<string>('');
  const selectedTemplate = ref<any>();
  const rows = ref<Array<any>>([]);
  const isExportVisible = ref<boolean>(false);

  // Computed fields
  const { isEditEnabled } = useEnvironmentSettings();
  const isEditButtonVisible = computed<boolean>(() => {
    return !loading.value && selectedTemplate.value && isEditEnabled.value;
  });

  const isFilterEditViewVisible = computed<boolean>(
    () => !loading.value && selectedTemplate.value && isEditEnabled.value
  );

  // Variables
  let startTime: number | null = null;

  // Functions
  const performPreflight = async (template) => {
    if (!template) {
      return;
    }

    loading.value = true;
    selectedTemplate.value = template;
    buildColumns(destinationId, template.id);
    streamId.value = await PreflightService.connectToEventStream(handleReceivedEvent);
  };

  const buildColumns = async (destinationId: number, templateId: string): Promise<void> => {
    columns.value = [];

    const headers = await GridService.getHeaders(HeaderType.EXPORT, destinationId, templateId);
    if (headers && headers.length) {
      headers.forEach((header) => {
        if (!!header.headerName) {
          const newColumn = {
            name: header.headerName,
            label: header.headerName,
            align: 'left',
            field: header.field,
            mandatory: header.required ?? false,
            recommended: header.recommended ?? false,
            headerStyle: 'background-color: var(--color-grey-lighter);',
            internalField: header.internalField,
            editable: header.override,
          };
          columns.value.push(newColumn);
        }
      });

      columns.value.unshift(PreflightColumnHelper.createStatusColumn());
      columns.value.unshift(PreflightColumnHelper.createUpcColumn());
      columns.value.unshift(PreflightColumnHelper.createSkuColumn());
    }
  };

  const handleReceivedEvent = async (receivedData: string) => {
    const template = selectedTemplate.value;
    if (!template) {
      return await finishPreflight();
    }

    const parsedEvents = PreflightService.parseEventObjectsFromEventLines(receivedData);

    const sessionEstablished = parsedEvents.find((x) => x.event === EventType.UUID);
    const sessionFinished = parsedEvents.find((x) => x.event === EventType.Finished);
    const sessionExport = parsedEvents.find((x) => x.event === EventType.Export);

    if (sessionEstablished) {
      sendInitialRequest(template);
    }

    if (sessionExport) {
      parsedEvents
        .filter((e) => e.event === EventType.Export)
        .forEach((e) => {
          buildRows(e.data);
        });
    }

    if (sessionFinished) {
      await finishPreflight(!!sessionFinished);
    }
  };

  const finishPreflight = async (sessionFinished = false) => {
    loading.value = false;
    await PreflightService.closeStream();
    isExportVisible.value = sessionFinished;
    let timeTaken = 0;

    if (startTime) {
      const endTime = new Date().getTime();
      timeTaken = endTime - startTime;
    }

    await preflightInsights.ReportPreflightEnded(timeTaken, rows.value?.length, streamId.value, sessionFinished);
  };

  const sendInitialRequest = async (template: Field) => {
    startTime = new Date().getTime();
    const orgId = await HttpAuth0Client.getInstance().getUserOrganizationId();
    await preflightInsights.ReportPreflightStart(orgId, destinationId, streamId.value);
    if (!allProductsSelected) {
      await PreflightService.preflightSpecificProducts(
        subCatalogId,
        destinationId,
        template.id,
        productsIdsArray.value,
        streamId.value,
        groupBy
      );
    } else {
      await PreflightService.preflightAllInQuery(
        subCatalogId,
        destinationId,
        template.id,
        streamId.value,
        productFilter.value,
        searchValue.value ?? '',
        groupBy
      );
    }
  };

  const buildRows = (dataEvents: []): void => {
    dataEvents.forEach((x) => populateProblemMetadata(x));
    dataEvents.forEach((x) => rows.value.push(x));
  };

  const clearPreviousResult = (): void => {
    rows.value = new Array<any>();
    selectedTemplate.value = null;
  };

  return {
    streamId,
    columns,
    rows,
    loading,
    isEditButtonVisible,
    isExportVisible,
    performPreflight,
    finishPreflight,
    clearPreviousResult,
    isFilterEditViewVisible,
  };
}
