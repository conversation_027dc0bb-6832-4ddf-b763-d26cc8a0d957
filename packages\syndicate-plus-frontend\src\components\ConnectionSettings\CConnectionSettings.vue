<template>
  <section class="c-inri-section">
    <q-input
      v-for="setting in settingsList"
      :key="setting.name"
      v-model="setting.value"
      :label="setting.displayName?.toLocaleLowerCase()"
      :type="setting.type"
      v-bind="$inri.input"
      hide-bottom-space
      class="w-1/2 max-w-xl"
      @update:model-value="updateValue"
    />
  </section>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue';
import { PluginProperty } from '@customTypes/ApiSettings';

const props = defineProps({
  settings: {
    type: Array<PluginProperty>,
    default: [],
  },
});

const emit = defineEmits(['update-value']);

// Refs
const settingsList = ref<PluginProperty[]>([]);

// Functions
const updateValue = () => {
  emit('update-value', settingsList.value);
};

// Lifecycle methods
watch(
  () => props.settings,
  async () => {
    settingsList.value = props.settings;
  }
);
</script>

<style scoped lang="scss"></style>
