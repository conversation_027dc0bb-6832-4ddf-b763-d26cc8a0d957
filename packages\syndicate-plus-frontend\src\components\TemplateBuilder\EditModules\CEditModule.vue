<template>
  <div class="builder-module">
    <div class="header font-bold">
      <q-icon name="mdi-drag-vertical" class="handle cursor-pointer pr-5px" size="20px" />
      <div>{{ getDisplayName(moduleType) }}</div>
      <q-btn
        class="delete-button float-right"
        icon="mdi-close"
        size="sm"
        flat
        round
        @click.stop="() => deleteBuilderModule(index)"
      />
    </div>
    <div class="builder-module-content">
      <c-company-logo-edit v-if="moduleType === ModuleTypes.STANDARD_COMPANY_LOGO" :index="index" />
      <c-images-and-text-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_FOUR_IMAGE_TEXT"
        :index="index"
        :number-of-blocks="4"
      />
      <c-images-and-text-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_THREE_IMAGES_AND_TEXT"
        :index="index"
        :number-of-blocks="3"
      />
      <c-image-sidebar-edit v-else-if="moduleType === ModuleTypes.STANDARD_IMAGE_SIDEBAR" :index="index" />
      <c-image-text-overlay-edit v-else-if="moduleType === ModuleTypes.STANDARD_IMAGE_TEXT_OVERLAY" :index="index" />
      <c-header-image-text-edit v-else-if="moduleType === ModuleTypes.STANDARD_HEADER_IMAGE_TEXT" :index="index" />
      <c-four-image-text-quadrant-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_FOUR_IMAGE_TEXT_QUADRANT"
        :index="index"
      />
      <c-multiple-image-text-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_MULTIPLE_IMAGES_AND_TEXT"
        :index="index"
      />
      <c-product-description-text-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_PRODUCT_DESCRIPTION_TEXT"
        :index="index"
      />
      <c-single-image-spec-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_IMAGE_SPECS_DETAIL"
        :index="index"
      />
      <c-single-side-image-edit v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_SIDE_IMAGE_LEFT" :index="index" />
      <c-single-side-image-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_SIDE_IMAGE_RIGHT"
        :index="index"
        :align-left="false"
      />
      <c-single-image-highlights-edit
        v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_IMAGE_AND_HIGHLIGHTS"
        :index="index"
      />
      <c-tech-spec-edit v-else-if="moduleType === ModuleTypes.STANDARD_TECH_SPECS" :index="index" />
      <c-text-edit v-else-if="moduleType === ModuleTypes.STANDARD_TEXT" :index="index" />
      <c-comparison-chart-edit v-else-if="moduleType === ModuleTypes.STANDARD_COMPARISON_CHART" :index="index" />
      <div v-else class="content">{{ moduleType }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ModuleTypes } from '@enums/Aplus';
import {
  CCompanyLogoEdit,
  CImagesAndTextEdit,
  CImageSidebarEdit,
  CImageTextOverlayEdit,
  CHeaderImageTextEdit,
  CFourImageTextQuadrantEdit,
  CMultipleImageTextEdit,
  CProductDescriptionTextEdit,
  CSingleImageSpecEdit,
  CSingleSideImageEdit,
  CSingleImageHighlightsEdit,
  CTechSpecEdit,
  CTextEdit,
  CComparisonChartEdit,
} from '@components/TemplateBuilder/EditModules';
import { getDisplayName } from '@helpers';

defineProps({
  moduleType: {
    type: String,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
  isDragging: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['delete-builder-module']);

// Functions
const deleteBuilderModule = (index: number) => {
  emit('delete-builder-module', index);
};
</script>
<style lang="scss" scoped>
$large-block-min-height: 200px;

.builder-module {
  min-height: $large-block-min-height;
  border: 1px solid var(--color-grey);
  margin-bottom: 10px;

  &.edit-mode {
    border: 1px solid var(--color-green);

    .header {
      background-color: var(--color-green-10);
    }
  }

  .header {
    height: 40px;
    padding: 10px;
    display: flex;
    position: relative;
    background-color: var(--color-grey-10);

    .delete-button {
      transition: all 0.5s;
      position: absolute;
      right: 5px;
      top: 5px;
    }
  }

  .builder-module-content {
    overflow: auto;
  }
}
</style>
