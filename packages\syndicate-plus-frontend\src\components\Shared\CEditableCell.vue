<template>
  <div v-if="isEditMode">
    <q-input
      class="editable-cell"
      :model-value="displayValue"
      borderless
      hide-bottom-space
      item-aligned
      autofocus
      @keydown="validateValue"
      @paste="validateValue"
      @update:model-value="setValue"
      @focus="startEditing"
      @keydown.enter.exact.prevent="() => stopEditing(StopCellEditType.Enter)"
      @keydown.shift.enter.exact.prevent="() => stopEditing(StopCellEditType.ShiftEnter)"
      @keydown.tab.exact.prevent="() => stopEditing(StopCellEditType.Tab)"
      @keydown.shift.tab.exact.prevent="() => stopEditing(StopCellEditType.ShiftTab)"
      @keydown.esc.exact.prevent="() => stopEditing(StopCellEditType.Esc)"
      @blur="() => stopEditing(StopCellEditType.Blur)"
    >
      <template v-if="isDateType" #append>
        <q-icon
          ref="icon"
          name="mdi-calendar-outline"
          class="cursor-pointer"
          color="grey-dark"
          size="19px"
          @click="datePickerIsOpen = true"
        />
      </template>
      <q-menu v-if="isDateType" v-model="datePickerIsOpen" no-parent-event>
        <q-date
          :model-value="datePickerDisplayValue"
          @update:model-value="
            (value) => {
              datePickerIsOpen = false;
              setValue(value);
              stopEditing(StopCellEditType.Blur);
            }
          "
        />
      </q-menu>
    </q-input>
  </div>
  <div v-else>
    {{ displayValue }}
  </div>
</template>

<script setup lang="ts">
import { watchEffect, PropType, ref } from 'vue';
import { StopCellEditType, ColumnDataType } from '@enums';
import { useEditableCell, useEditableDateCell } from '@composables/Common';

const emits = defineEmits(['stop-editing']);

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  isEditMode: {
    type: Boolean,
    default: false,
  },
  dataType: {
    type: String as PropType<ColumnDataType>,
    default: ColumnDataType.STRING,
  },
});

// Refs
const initialValue = ref<string>(props.value);
const newValue = ref<string>(props.value);
const datePickerIsOpen = ref(false);
const isInputFocused = ref(false);

// Composables
const { setValue, validateValue, displayValue } = useEditableCell(newValue, isInputFocused, props.dataType);
const { datePickerDisplayValue, isDateType } = useEditableDateCell(newValue, props.dataType);

// Functions
const startEditing = () => (isInputFocused.value = true);

const stopEditing = (type: StopCellEditType) => {
  isInputFocused.value = false;
  if (type === StopCellEditType.Blur && datePickerIsOpen.value) {
    return;
  }

  if (type === StopCellEditType.Esc) {
    newValue.value = initialValue.value;
  }

  emits('stop-editing', newValue.value, initialValue.value, type);
};

// Lifecycle methods
watchEffect(() => {
  newValue.value = props.value;
});
</script>

<style lang="scss" scoped>
.editable-cell {
  height: 19.5px;
  font-size: 13px;
  line-height: 21px !important;
  color: var(--color-grey-darkest) !important;
  letter-spacing: normal;

  .q-field--item-aligned {
    padding: 0px;
  }

  :deep(.q-field__control) {
    height: 19.5px;
  }

  :deep(.q-field__control-container) {
    height: 19.5px;
  }

  :deep(.q-field__native) {
    color: var(--color-grey-darkest);
    letter-spacing: normal;
  }

  :deep(.q-field__marginal) {
    height: 19.5px;
    padding-top: 3px;
  }
}
</style>
