import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';
import { QCard } from 'quasar';

import CMediaNamingOverride from '@components/MediaMapping/CMediaNamingOverride.vue';

describe('CMediaNamingOverride', () => {
  it('checks if has valid naming override pattern', async () => {
    // Arrange
    const wrapper = mount(CMediaNamingOverride, {
      props: { mediaNamingOverride: '${SKU}_${COLOR}${INDEX_TAG}${EXT}_' },
    });

    // Act
    const cards = wrapper.findAllComponents(QCard);

    // Assert
    expect(cards.length).toBe(6);
  });

  it('checks if has no cards if mediaNamingOverride is empty string', async () => {
    // Arrange
    const wrapper = mount(CMediaNamingOverride, {
      props: { mediaNamingOverride: '' },
    });

    // Act
    const cards = wrapper.findAllComponents(QCard);

    // Assert
    expect(cards.length).toBe(0);
  });
});
