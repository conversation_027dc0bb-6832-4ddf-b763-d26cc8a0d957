import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { CorePage } from '@pages/core/core.page';
import { CoreSettingsPage } from '@pages/core/coreSettings/coreSettings.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';
import { ManageMappingsPage } from '@pages/welcomePage/welcomeSettings/formatsTab/manageMappings/manageMappings.page';
import { EditMappingPage } from '@pages/welcomePage/welcomeSettings/formatsTab/manageMappings/editMapping.page';

test.describe('Edit mapping', () => {
  const UITestFormat = 'UITest format - product format';
  const bestBuyFormat = 'Best Buy';
  const UITestMapping = 'UITest - ProductFormatMapping';
  const bestBuyTemplate = 'UITest-bestbuy-mapping';
  const sourceProductName = 'ProductName';
  const sourceCode = 'ProductCode';
  const sourceName = 'ProductName';
  const sourceShortDesc = 'ProductShortDescription';
  const targetCode = 'Product Code';
  const targetName = 'Name';
  const targetMultiValueCVL1 = 'Multi value CVL 1';
  const targetMadeIn = 'Made in';
  const targetDescription = 'Description';
  const basicProdInfo = 'Basic Product Information';
  const recommended = 'recommended';
  const mapped = 'mapped';
  const unmapped = 'unmapped';
  const enumDialogTargetValueMen = 'Men';
  const enumDialogTargetValueWomen = 'Women';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let corePage: CorePage;
  let coreSettingsPage: CoreSettingsPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatsTabPage: FormatsTabPage;
  let mappingsPage: ManageMappingsPage;
  let editMappingPage: EditMappingPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    corePage = new CorePage(localPage);
    coreSettingsPage = new CoreSettingsPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatsTabPage = new FormatsTabPage(localPage);
    mappingsPage = new ManageMappingsPage(localPage);
    editMappingPage = new EditMappingPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatsTabPage.formatsTable, 'Formats table is not visible').toBeVisible();
    await formatsTabPage.selectFormat(UITestFormat).click();
    await formatsTabPage.manageMappingsButton.click();
  });

  test('Map and unmap fields @notForPr', async () => {
    await mappingsPage.mappingName(UITestMapping).click();
    await expect(mappingsPage.editMapping, 'Edit mapping button is not visible').toBeVisible();
    await expect(mappingsPage.deleteMapping, 'Delete mapping button is not visible').toBeVisible();
    await mappingsPage.editMapping.click();
    // Map fields
    await expect(async () => {
      await editMappingPage.mapAutoButton.click();
      await expect(coreSettingsPage.sourceField(sourceCode), 'Code field is not visible').toBeVisible({
        timeout: 2000,
      });
    }, 'Field is not mapped').toPass();
    await editMappingPage.saveButton.click();
    await expect(editMappingPage.saveChangesToaster, 'Toast is not visible').toBeVisible();
    await expect(editMappingPage.saveChangesToaster, 'Toast is visible').toBeHidden();
    // Unmap fields
    await editMappingPage.goBackButton.click();
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await editMappingPage.target(targetCode).click();
    await editMappingPage.unmapButton.click();
    await editMappingPage.target(targetName).click();
    await editMappingPage.unmapButton.click();
    await editMappingPage.saveButton.click();
    await expect(editMappingPage.saveChangesToaster, 'Changes saved dialog is not visible').toBeVisible();
    // Verified unmapped fields
    await editMappingPage.goBackButton.click();
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await expect(coreSettingsPage.sourceField(sourceCode), 'Code field is not visible').toBeHidden();
    await expect(coreSettingsPage.sourceField(sourceName), 'Name field is not visible').toBeHidden();
  });

  test('Category filter @notForPr', async () => {
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await editMappingPage.selectCategory(basicProdInfo);
    await expect(editMappingPage.target(targetCode), 'Code target is not visible').toBeVisible();
    await expect(editMappingPage.target(targetName), 'Name target is not visible').toBeVisible();
    await expect(editMappingPage.target(targetMadeIn), 'ShortDesc target should not be visible').toBeHidden();
  });

  test('Field type filter @notForPr', async () => {
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await editMappingPage.selectFieldType(recommended);
    await expect(editMappingPage.target(targetCode), 'Code target should not be visible').not.toBeVisible();
    await expect(editMappingPage.target(targetDescription), 'Description target is visible').toBeVisible();
  });

  test('Mapped state filter @notForPr', async () => {
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await editMappingPage.selectState(mapped);
    await expect(editMappingPage.source(sourceCode), 'Code source is not visible').toBeHidden();
    await expect(editMappingPage.source(sourceName), 'Name source is not visible').toBeHidden();
    await expect(editMappingPage.source(sourceShortDesc), 'ShortDesc source should not be visible').toBeVisible();
  });

  test('Unmapped state filter @notForPr', async () => {
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await editMappingPage.selectState(unmapped);
    await expect(editMappingPage.target(targetDescription), 'Description field is visible').toBeHidden();
    await expect(editMappingPage.source(targetCode), 'Code field is not visible').toBeVisible();
    await expect(editMappingPage.source(targetName), 'Name field is not visible').toBeVisible();
  });

  test('Map enumeration menu @notForPr', async () => {
    await mappingsPage.mappingName(UITestMapping).dblclick();
    await editMappingPage.target(targetMultiValueCVL1).click();
    await editMappingPage.mapEnumerationButton.click();
    await expect.soft(editMappingPage.enumDialogHeader, 'Enum dialog header is not visible').toBeVisible();
    await expect(editMappingPage.enumDialogSaveButton, 'Save button is not visible').toBeVisible();
    await expect(editMappingPage.enumDialogAutomapButton, 'Automap button is not visible').toBeVisible();
    await expect(editMappingPage.enumDialogCancelButton, 'Cancel button is not visible').toBeVisible();
    await editMappingPage.enumDialogTarget(0).click();
    await editMappingPage.selectFromList(enumDialogTargetValueMen).click();
    await editMappingPage.enumDialogSaveButton.click();
    await expect(editMappingPage.enumDialogHeader, 'Enum dialog header should close after saving changes').toBeHidden();
    // Verify enum mapping was saved and change value.
    await expect(async () => {
      await editMappingPage.mapEnumerationButton.click();
      await expect(
        editMappingPage.enumDialogTargetWithText(enumDialogTargetValueMen, 0),
        'Enum value is not visible'
      ).toBeVisible({ timeout: 1000 });
    }, 'Enum value is not visible').toPass();
    await editMappingPage.enumDialogTargetWithText(enumDialogTargetValueMen, 0).click();
    await editMappingPage.selectFromList(enumDialogTargetValueWomen).click();
    await editMappingPage.enumDialogSaveButton.click();
  });

  // Known bug #108994
  test.fail('API format shows mappings @notForPr', async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.openFormat(bestBuyFormat);
    await corePage.clickSettingButton();
    await coreSettingsPage.selectTemplate(bestBuyTemplate); //Known issue: Template is not seen. Bug n 108994.
    await expect(coreSettingsPage.sourceField(sourceProductName), 'name field is not visible').toBeVisible();
  });
});
