import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';
import CMediaSpecification from '@components/MediaMapping/CMediaSpecification.vue';

describe('CMediaSpecification', () => {
  it('checks component properties', async () => {
    // Arrange
    const CMediaSpecificationProps = {
      imageWidth: '200px',
      imageHeight: '300px',
      swatchWidth: '50px',
      swatchHeight: '75px',
      backgroundColour: '#FFFFFF',
    };

    const wrapper = mount(CMediaSpecification, {
      props: { mediaSpecification: CMediaSpecificationProps },
    });

    // Act & Assert
    const rows = wrapper.findAll('tbody tr');

    expect(rows[0].findAll('td')[1].text() === '200px').toBe(true);
    expect(rows[1].findAll('td')[1].text() === '300px').toBe(true);
    expect(rows[2].findAll('td')[1].text() === '50px').toBe(true);
    expect(rows[3].findAll('td')[1].text() === '75px').toBe(true);
    expect(rows[4].findAll('td')[1].text() === '#FFFFFF').toBe(true);
  });
});
