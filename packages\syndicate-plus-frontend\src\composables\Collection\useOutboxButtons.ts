import { computed, Ref } from 'vue';
import { OutboxStatus, SquareColor, SyndicationPageTabNames } from '@enums';
import { ProductOutboxRow } from '@customTypes';
import outboxHttpService from '@httpservices/Outbox/OutboxHttpService';
import { notify } from '@inriver/inri';
import isFeatureEnabled from '@utils/isFeatureEnabled';

export default function useOutboxButtons(
  tab: Ref<SyndicationPageTabNames>,
  selectedOutboxRow: Ref<ProductOutboxRow | undefined>,
  useI18n?: Function
) {
  let t = (key: string) => {
    return key;
  };
  if (useI18n) {
    t = useI18n().t;
  }

  const isCancelOutboxJobButtonVisible = computed(() => {
    if (selectedOutboxRow?.value?.status === undefined || tab.value !== SyndicationPageTabNames.Outbox) {
      return false;
    }
    return (
      selectedOutboxRow.value.status.outboxStatus === OutboxStatus.IN_PROGRESS ||
      selectedOutboxRow.value.status.outboxStatus === OutboxStatus.IN_QUEUE ||
      (selectedOutboxRow.value.status.outboxStatus === OutboxStatus.SCHEDULED && isFeatureEnabled('cancel-syndication'))
    );
  });

  async function cancelJob() {
    if (selectedOutboxRow.value?.catalogExportId === undefined) {
      throw new Error('no outbox row selected');
    }

    try {
      if (selectedOutboxRow.value.status?.outboxStatus === OutboxStatus.SCHEDULED) {
        await outboxHttpService.cancelScheduledJob(selectedOutboxRow.value?.catalogExportId);
      } else {
        await outboxHttpService.cancelJob(selectedOutboxRow.value?.catalogExportId);
      }
    } catch (error) {
      notify.error(t('syndicate_plus.syndication.outbox.cancel_process_failed'), {
        position: 'bottom-right',
      });
      return;
    }

    selectedOutboxRow.value.status = { outboxStatus: OutboxStatus.CANCELED, squareColor: SquareColor.RED };
    notify.success(t('syndicate_plus.syndication.outbox.cancel_process_success'));
  }

  return {
    isCancelOutboxJobButtonVisible: isCancelOutboxJobButtonVisible,
    cancelJob,
  };
}
