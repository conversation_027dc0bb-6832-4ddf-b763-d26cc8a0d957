import { it, describe, expect, vi } from 'vitest';
import { ref } from 'vue';
import { SyndicationPageTabNames, ProductGrouping } from '@enums';
import { Product } from '@customTypes';
import { useExportBelowTheFoldButton } from '@composables/ExportBelowTheFold';
import { ProductFilterDto } from '@dtos';

describe('useExportBelowTheFoldButton', () => {
  const useI18n = vi.fn().mockReturnValue({
    t: vi.fn(),
  });

  [
    { tab: SyndicationPageTabNames.Collection, visible: false },
    { tab: SyndicationPageTabNames.Aplus, visible: false },
    { tab: SyndicationPageTabNames.Products, visible: true },
    { tab: SyndicationPageTabNames.Outbox, visible: false },
  ].forEach(({ tab, visible }) => {
    it(`has visible ${visible} for ${tab} tab`, async () => {
      // Arrange
      vi.stubEnv('VITE_FEATURE_BELOW-THE-FOLD', 'true');
      const tabRef = ref(tab);
      const selectedProducts = createProducts();
      const productFilter = createProductFilter(ProductGrouping.CUSTOM);

      // Act
      const { isExportBelowTheFoldButtonVisible } = useExportBelowTheFoldButton(
        useI18n,
        tabRef,
        selectedProducts,
        productFilter
      );

      // Assert
      expect(isExportBelowTheFoldButtonVisible.value).toBe(visible);
    });
  });

  it('is not visible if no product is selected', async () => {
    // Arrange
    vi.stubEnv('VITE_FEATURE_BELOW-THE-FOLD', 'true');
    const tabRef = ref(SyndicationPageTabNames.Products);
    const selectedProducts = ref([]);
    const productFilter = createProductFilter(ProductGrouping.CUSTOM);

    // Act
    const { isExportBelowTheFoldButtonVisible } = useExportBelowTheFoldButton(
      useI18n,
      ref(tabRef),
      selectedProducts,
      productFilter
    );

    // Assert
    expect(isExportBelowTheFoldButtonVisible.value).toBe(false);
  });

  [
    { productGrouping: ProductGrouping.CUSTOM, visible: true },
    { productGrouping: ProductGrouping.NAME, visible: false },
    { productGrouping: ProductGrouping.SKU, visible: false },
    { productGrouping: ProductGrouping.UPC, visible: false },
  ].forEach(({ productGrouping, visible }) => {
    it(`has visible ${visible} for ${productGrouping} tab`, async () => {
      // Arrange
      vi.stubEnv('VITE_FEATURE_BELOW-THE-FOLD', 'true');
      const tabRef = ref(SyndicationPageTabNames.Products);
      const selectedProducts = createProducts();
      const productFilter = createProductFilter(productGrouping);

      // Act
      const { isExportBelowTheFoldButtonVisible } = useExportBelowTheFoldButton(
        useI18n,
        tabRef,
        selectedProducts,
        productFilter
      );

      // Assert
      expect(isExportBelowTheFoldButtonVisible.value).toBe(visible);
    });
  });
});

function createProductFilter(productGrouping: ProductGrouping) {
  const productFilter = new ProductFilterDto([]);
  productFilter.groupBy.id = productGrouping;

  return ref(productFilter);
}

function createProducts() {
  const products: Product[] = [
    {
      id: '1',
      name: 'Product1',
      imageUrl: 'image_url_1.jpg',
      sku: ['sku1', 'sku2'],
      upc: 'upc1',
      brandName: 'Brand1',
      isUpdatedSinceLastSyndication: false,
      color: 'Red',
      customGroup: 'CustomGroup1',
      isAplus: false,
    } as Product,
    {
      id: '2',
      name: 'Product2',
      imageUrl: 'image_url_2.jpg',
      sku: ['sku3', 'sku4'],
      upc: 'upc2',
      brandName: 'Brand2',
      isUpdatedSinceLastSyndication: true,
      color: 'Blue',
      customGroup: 'CustomGroup2',
      isAplus: false,
    } as Product,
  ];

  return ref(products);
}
