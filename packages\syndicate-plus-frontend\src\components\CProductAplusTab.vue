<template>
  <div class="c-aplus-tab">
    <c-no-data
      v-if="failedToLoadIframe"
      src="fika"
      image-height="195px"
      :title="$t('syndicate_plus.service_unavailable.title')"
      :text="$t('syndicate_plus.service_unavailable.text')"
      class="no-data-component pt-4"
    />
    <c-no-data
      v-else-if="!initDone"
      src="building"
      image-height="195px"
      :title="$t('syndicate_plus.syndication.aplus_editor.initializing')"
      :text="$t('syndicate_plus.syndication.aplus_editor.initializing_description')"
    >
      <q-spinner-dots class="no-data-title-slot" color="primary" size="20" />
    </c-no-data>
    <iframe
      v-show="initDone"
      id="iframe-test"
      ref="editorIFrame"
      class="content-editor"
      width="100%"
      :src="iframeUrl"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeMount } from 'vue';
import { CNoData } from '@components';
import { useRoute } from 'vue-router';
import { getEnvVariable } from '../utils/envVariablesManager';
import { getCurrentOrganizationId } from '@services/DataFlow/CurrentOrganizationService';

const route = useRoute();

// Refs
const editorIFrame = ref(null);
const initDone = ref(false);
const iframeUrl = ref('');
const failedToLoadIframe = ref(false);

// Variables
let timeoutId;

onBeforeMount(async () => {
  const orgId = await getCurrentOrganizationId();
  const destinationId = parseInt(route.params.subCatalogId as string);
  iframeUrl.value = `${getEnvVariable(
    'FILE_SERVER_URL'
  )}/org/${orgId}/channel/${destinationId}/aplus-content/modules?from_inriver=true`;
});

onMounted(() => {
  window.addEventListener('message', receiveMessage, false);
  setTimeout(() => {
    if (!initDone.value) {
      sendMessageToIframe({
        url: `${getEnvVariable('STATIC_WEB_APP_URL')}/AplusEditorOverride.css`,
      });
    }
  }, 10000);
  timeoutId = setTimeout(() => {
    if (!initDone.value) {
      failedToLoadIframe.value = true;
    }
  }, 30000);
});

onUnmounted(() => {
  window.removeEventListener('message', receiveMessage, false);
  clearTimeout(timeoutId);
});

// Functions
const sendMessageToIframe = (message) => {
  const iframe = document.getElementById('iframe-test') as HTMLIFrameElement;
  if (!initDone.value && iframe) {
    iframe.contentWindow?.postMessage(message, '*');
  }
};

const receiveMessage = (event) => {
  if (event.data === 'init-done') {
    sendMessageToIframe({
      url: `${getEnvVariable('STATIC_WEB_APP_URL')}/AplusEditorOverride.css`,
    });
    setTimeout(() => {
      initDone.value = true;
      clearTimeout(timeoutId);
    }, 600);
    window.removeEventListener('message', receiveMessage, false);
  }
};
</script>

<style lang="scss" scoped>
.content-editor {
  width: 100%;
  height: calc(100vh - 196px);
}
.no-data-title-slot {
  padding-top: 9px;
  display: inline;
}
</style>
