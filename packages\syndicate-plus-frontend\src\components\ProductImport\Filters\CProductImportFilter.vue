<template>
  <div class="filter-panel">
    <q-tabs
      v-model="tab"
      class="bg-white text-grey-7"
      active-color="primary"
      active-class="active-tab"
      indicator-color="transparent"
      align="justify"
      no-caps
    >
      <q-tab :name="FilterTabNames.FILTERS_TAB" :label="$t('syndicate_plus.common.filter.filters')" />
    </q-tabs>
    <q-tab-panels v-model="tab">
      <q-tab-panel :name="FilterTabNames.FILTERS_TAB" class="panel-content p-0">
        <q-inner-loading :showing="isFetching" size="40" color="primary">
          <c-spinner color="primary" size="40" />
        </q-inner-loading>
        <c-content-type-filter-section :is-expanded="expanded.contentType" @apply-filter="applyContentTypeFilter" />
        <c-status-filter-section :is-expanded="expanded.statuses" @apply-filter="applyStatusFilter" />
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { FilterTabNames, ProductImportStatus, ProductImportContentType } from '@enums';
import { ProductImportFilter, ProductImportExpandedFilterSections } from '@customTypes/Products';
import { CStatusFilterSection, CContentTypeFilterSection } from '@components/ProductImport/Filters';

defineProps({
  isFetching: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['apply-filter']);

// Refs
const tab = ref(FilterTabNames.FILTERS_TAB);
const filter = ref<ProductImportFilter>();
const expanded = ref({
  statuses: true,
  contentType: true,
} as ProductImportExpandedFilterSections);

// Functions
const applyStatusFilter = (status?: ProductImportStatus): void => {
  filter.value = {
    ...filter.value,
    status,
  };
  applyFilterSelections(filter.value);
};

const applyContentTypeFilter = (contentType?: ProductImportContentType): void => {
  filter.value = {
    ...filter.value,
    contentType,
  };
  applyFilterSelections(filter.value);
};

const applyFilterSelections = (filter: ProductImportFilter): void => {
  emit('apply-filter', filter);
};
</script>

<style scoped lang="scss">
.filter-panel {
  position: absolute;
  width: 400px;
  height: calc(100vh - 52.5px);
  z-index: 3;
  background-color: var(--on-primary-color);
  box-shadow: 2px 0 10px 0 var(--color-border);

  .panel-content {
    height: calc(100vh - 175px);
  }
}

.q-inner-loading {
  z-index: 10;
}
</style>
