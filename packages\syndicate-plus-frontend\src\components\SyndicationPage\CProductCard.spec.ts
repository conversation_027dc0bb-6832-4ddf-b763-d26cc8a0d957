import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import CSmallCard from '@inriver/inri/src/components/cards/CSmallCard.vue';
import CProductCard from '@components/SyndicationPage/CProductCard.vue';
import { Product } from '@customTypes';
import { QBadge, QIcon } from 'quasar';
import { ProductGrouping } from '@enums';

describe('ProductCard', () => {
  function mountCProductCard(product: Product, productGrouping?: ProductGrouping) {
    return mount(CProductCard, {
      props: {
        product,
        isSelected: false,
        groupBy: productGrouping ?? ProductGrouping.SKU,
      },
      global: {
        components: {
          'c-small-card': CSmallCard,
          'q-badge': QBadge,
          'q-icon': QIcon,
        },
        stubs: {
          'c-context-menu': true,
        },
        mocks: {
          $t: (msg: string) => msg,
        },
      },
    });
  }

  it('renders the name of the product', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: 'text.jpg',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    expect(wrapper.find('.text').text()).to.include(product.name);
  });

  it('renders the image of the product', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: 'text.jpg',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const imageWrapper = wrapper.find('.image');
    expect(imageWrapper.exists()).toBeTruthy();
  });

  it('doesn not render the image of the product', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const imageWrapper = wrapper.find('.image');
    expect(imageWrapper.exists()).toBeFalsy();
  });

  it('renders the brandName of the product', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-123',
      sku: [],
      brandName: 'Test brand name',
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    expect(wrapper.find('.c-product-card').text()).to.include(product.brandName);
  });

  it('renders the "updated" if it is true', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: true,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const productCardUpdated = wrapper.find('.updated');
    expect(productCardUpdated.exists()).toBe(true);
  });

  it('does not render the sku of the product if it is undefined', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const description = wrapper.find('.description');
    expect(description.exists()).toBeFalsy();
  });

  it('does not render the brandName of the product if it is undefined', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const productCardText = wrapper.find('.c-product-card').text();
    expect(productCardText).not.toContain('undefined');
  });

  it('does not render the "updated" if it is false', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-123',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: false,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const productCardUpdated = wrapper.find('.updated');
    expect(productCardUpdated.exists()).toBe(false);
  });

  it('renders the "aplus" badge if isAplus is true', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-124',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: true,
      isAplus: true,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const productCardIsAplus = wrapper.find('.aplus');
    expect(productCardIsAplus.exists()).toBe(true);
  });

  it('does not render the "aplus" badge if isAplus is false', async () => {
    // Arrange
    const product: Product = {
      name: 'Mock Name',
      imageUrl: '',
      id: 'mock-id-124',
      sku: [],
      brandName: undefined,
      isUpdatedSinceLastSyndication: true,
      isAplus: false,
    };

    const wrapper = mountCProductCard(product);
    // Act

    // Assert
    const productCardIsAplus = wrapper.find('.aplus');
    expect(productCardIsAplus.exists()).toBe(false);
  });

  describe('correctly renders description field', () => {
    it('renders the sku of the product', async () => {
      // Arrange
      const product: Product = {
        name: 'Mock Name',
        imageUrl: '',
        id: 'mock-id-123',
        sku: ['test sku value'],
        brandName: 'Test brand name',
        isUpdatedSinceLastSyndication: false,
        isAplus: false,
      };

      // Act
      const wrapper = mountCProductCard(product, ProductGrouping.SKU);

      // Assert
      expect(wrapper.find('.description').text()).to.include(product.sku[0]);
    });

    it('renders the upc of the product', async () => {
      // Arrange
      const product: Product = {
        name: 'Mock Name',
        imageUrl: '',
        id: 'mock-id-123',
        upc: 'mockUpd',
        sku: ['test sku value'],
        brandName: 'Test brand name',
        isUpdatedSinceLastSyndication: false,
        isAplus: false,
      };

      // Act
      const wrapper = mountCProductCard(product, ProductGrouping.UPC);

      // Assert
      expect(wrapper.find('.description').text()).to.include(product.upc);
    });

    it('renders the custom group of the product', async () => {
      // Arrange
      const product: Product = {
        name: 'Mock Name',
        imageUrl: '',
        id: 'mock-id-123',
        upc: 'mockUpd',
        sku: ['test sku value', 'another Sku'],
        brandName: 'Test brand name',
        isUpdatedSinceLastSyndication: false,
        isAplus: false,
        customGroup: 'custom group 1',
      };

      // Act
      const wrapper = mountCProductCard(product, ProductGrouping.CUSTOM);

      // Assert
      expect(wrapper.find('.description').text()).to.include(product.customGroup);
    });
  });
});
