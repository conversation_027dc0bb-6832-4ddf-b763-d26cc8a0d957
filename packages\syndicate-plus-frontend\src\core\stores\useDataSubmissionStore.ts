import { defineStore } from 'pinia';
import { ref } from 'vue';
import { fetchDataSubmissions, fetchDataSubmissionEntities } from '@core/services/dataSubmissionsApi';
import { DataSubmission, DataSubmissionEntitiesResponse, DataSubmissionEntity } from '@core/interfaces';

export const useDataSubmissionStore = defineStore('useDataSubmission', () => {
  // State for the data submissions table
  const dataSubmissions = ref<DataSubmission[]>([]);
  const submissionsPage = ref(1);
  const submissionsPageSize = 100;
  const isLoadingSubmissions = ref(false);
  const isLastPage = ref(false);
  const selectedSubmissionId = ref<number | null>(null);

  // State for the data submission entries table
  const dataSubmissionEntities = ref<DataSubmissionEntity[]>([]);
  const entitiesPage = ref(1);
  const entitiesPageSize = 50;
  const isLoadingEntities = ref(false);
  const isLastEntitiesPage = ref(false);

  const loadDataSubmissions = async (jobId: number | undefined = undefined) => {
    if (isLoadingSubmissions.value || isLastPage.value) {
      return;
    }

    if (submissionsPage.value === 1) {
      dataSubmissions.value = [];
      isLastPage.value = false;
    }

    isLoadingSubmissions.value = true;
    try {
      const response = await fetchDataSubmissions(submissionsPage.value, submissionsPageSize, jobId);
      if (submissionsPage.value === 1) {
        dataSubmissions.value = response.dataSubmissions;
      } else {
        dataSubmissions.value = [...dataSubmissions.value, ...response.dataSubmissions];
      }
      if (response.dataSubmissions.length < submissionsPageSize) {
        isLastPage.value = true;
      } else {
        submissionsPage.value += 1;
      }
    } finally {
      isLoadingSubmissions.value = false;
    }
  };

  const loadDataSubmissionEntities = async (dataSubmissionId: number): Promise<void> => {
    if (isLoadingEntities.value || !dataSubmissionId) {
      return;
    }

    if (entitiesPage.value === 1) {
      dataSubmissionEntities.value = [];
      isLastEntitiesPage.value = false;
    }

    isLoadingEntities.value = true;
    try {
      const response = (await fetchDataSubmissionEntities(
        dataSubmissionId,
        entitiesPage.value,
        entitiesPageSize
      )) as DataSubmissionEntitiesResponse;
      const entities = response.entities as DataSubmissionEntity[];
      if (entitiesPage.value === 1) {
        dataSubmissionEntities.value = entities;
      } else {
        dataSubmissionEntities.value = [...dataSubmissionEntities.value, ...entities];
      }
      if (entities.length < entitiesPageSize) {
        isLastEntitiesPage.value = true;
      } else {
        entitiesPage.value += 1;
      }
    } finally {
      isLoadingEntities.value = false;
    }
  };

  const selectSubmission = async (dataSubmissionId: number) => {
    selectedSubmissionId.value = dataSubmissionId;
    entitiesPage.value = 1;
    dataSubmissionEntities.value = [];
    await loadDataSubmissionEntities(dataSubmissionId);
  };

  return {
    dataSubmissions,
    submissionsPage,
    submissionsPageSize,
    isLoadingSubmissions,
    dataSubmissionEntities,
    entitiesPage,
    entitiesPageSize,
    isLoadingEntities,
    selectedSubmissionId,
    loadDataSubmissions,
    loadDataSubmissionEntities,
    selectSubmission,
    isLastPage,
    isLastEntitiesPage,
  };
});
