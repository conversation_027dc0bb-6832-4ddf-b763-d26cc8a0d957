import { it, describe, expect } from 'vitest';
import { ref } from 'vue';
import { useEditableDateCell } from '@composables/Common';
import { ColumnDataType } from '@enums';

describe('useEditableDateCell', () => {
  describe('datePickerDisplayValue', () => {
    it('returns the date picker value in the YYYY/MM/DD format', async () => {
      // Arrange
      const newValue = ref('');
      const testCases = [
        { value: '1709757800000', expectedValue: '2024/03/06' },
        { value: 'a', expectedValue: '' },
        { value: '', expectedValue: '' },
      ];

      testCases.forEach(({ value, expectedValue }) => {
        // Act
        const { datePickerDisplayValue } = useEditableDateCell(newValue, ColumnDataType.DATE);
        newValue.value = value;

        // Assert
        expect(datePickerDisplayValue.value).toBe(expectedValue);
      });
    });
  });

  describe('isDateType', () => {
    it('returns correct value depending on the data type', async () => {
      // Arrange
      const newValue = ref('');
      const testCases = [
        { dataType: ColumnDataType.CURRENCY, expectedValue: false },
        { dataType: ColumnDataType.DATE, expectedValue: true },
        { dataType: ColumnDataType.STRING, expectedValue: false },
      ];

      testCases.forEach(({ dataType, expectedValue }) => {
        // Act
        const { isDateType } = useEditableDateCell(newValue, dataType);

        // Assert
        expect(isDateType.value).toBe(expectedValue);
      });
    });
  });
});
