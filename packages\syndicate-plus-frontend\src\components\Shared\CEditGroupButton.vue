<template>
  <c-tile-btn
    v-if="isVisible"
    :icon="isEditingMode ? 'mdi-content-save-outline' : 'mdi-pencil-outline'"
    :icon-size="20"
    :tooltip-left="
      isEditingMode ? saveText || $t('syndicate_plus.common.save') : editText || $t('syndicate_plus.common.edit')
    "
    @click="logEventAndClick(isEditingMode ? ActionName.SAVE_EDIT : ActionName.START_EDIT, toggleEditMode)"
  />
  <c-tile-btn
    v-if="isEditingMode"
    icon="mdi-close"
    :icon-size="20"
    :tooltip-left="$t('syndicate_plus.common.cancel')"
    @click="logEventAndClick(ActionName.CANCEL_EDIT, cancel)"
  />
</template>

<script setup lang="ts">
import { ActionName } from '@enums';

import { useAppInsightsStore } from '@stores';

const { logEventAndClick } = useAppInsightsStore();

const props = defineProps({
  isEditingMode: {
    type: Boolean,
    default: false,
  },
  isVisible: {
    type: Boolean,
    default: false,
  },
  editText: {
    type: String,
    required: false,
  },
  saveText: {
    type: String,
    required: false,
  },
});
const emit = defineEmits(['toggle-edit-mode', 'save', 'cancel-editing']);

// Functions
const toggleEditMode = () => {
  const isEditingMode = !props.isEditingMode;

  if (isEditingMode === false) {
    emit('save');
  } else {
    emit('toggle-edit-mode', isEditingMode);
  }
};

const cancel = () => {
  const isEditingMode = false;
  emit('toggle-edit-mode', isEditingMode);
  emit('cancel-editing', isEditingMode);
};
</script>
