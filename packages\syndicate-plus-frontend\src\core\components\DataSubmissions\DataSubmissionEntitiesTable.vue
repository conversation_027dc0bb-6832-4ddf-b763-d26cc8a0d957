<template>
  <div>
    <q-table
      ref="entitiesTable"
      v-model:selected="selectedEntity"
      class="submission-entities-table sticky-table-header"
      :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
      flat
      dense
      hide-bottom
      separator="cell"
      :pagination="{
        page: 1,
        rowsPerPage: 0,
      }"
      :rows="entities"
      row-key="id"
      :columns="submissionEntriesColumns"
      :loading="isLoading"
      virtual-scroll
      :virtual-scroll-item-size="48"
      :virtual-scroll-sticky-size-start="48"
      @virtual-scroll="onVirtualScroll"
      @row-click="onRowClick"
      @row-dblclick="onRowDoubleClick"
    >
      <template #loading>
        <q-inner-loading showing color="primary" class="inner-loading">
          <c-spinner color="primary" size="40" />
        </q-inner-loading>
      </template>
      <template #body-cell-failureMessage="props">
        <q-td>{{ props.row.apiFailureResponse ? YesNo.YES : YesNo.NO }}</q-td>
      </template>
    </q-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRef } from 'vue';
import { DataSubmissionEntity } from '@core/interfaces';
import { submissionEntriesColumns } from '@core/const';
import { YesNo } from '@core/enums';

const props = defineProps({
  entities: {
    type: Array as () => DataSubmissionEntity[],
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  dataSubmissionId: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(['update:selected', 'load-more', 'show-details']);

// Refs
const selectedEntity = ref<DataSubmissionEntity[]>([]);

// Functions
const onRowDoubleClick = (_: Event, row: DataSubmissionEntity): void => {
  // On double-click, open the dialog if there's an error message
  if (row.apiFailureResponse) {
    const newRowValue = row;
    selectedEntity.value = [newRowValue];
    emit('update:selected', newRowValue);
    emit('show-details');
  }
};

const onVirtualScroll = ({ to }: { to: number }) => {
  const entities = toRef(props, 'entities');
  if (to >= entities.value.length - 5 && !props.isLoading && props.dataSubmissionId) {
    emit('load-more');
  }
};

const onRowClick = (_: Event, row: DataSubmissionEntity): void => {
  const newRowValue = selectedEntity.value.includes(row) ? null : row;
  selectedEntity.value = newRowValue ? [newRowValue] : [];
  emit('update:selected', newRowValue || null);
};
</script>

<style lang="scss" scoped>
.submission-entities-table {
  max-height: 350px;
  min-height: 120px;
}
</style>
