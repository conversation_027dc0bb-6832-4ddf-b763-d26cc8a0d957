:root {
  --color-grey-light: #ddd;
  --color-grey-lighter: #eaeaea;
  --color-black: #000;
  --color-grey-10: #f3f3f3;
  --surface-color: #fff;
  --color-grey-dark: #888;
  --color-border: var(--color-grey-light);
  --color-bordored: var(--color-border);
}

/* Windi */
.ml-5px {
  margin-left: 5px;
}

.ml-10px {
  margin-left: 10px;
}

.text-center {
  text-align: center;
}

.mr-20px {
  margin-right: 20px;
}

.mb-10px {
  margin-bottom: 10px;
}

.h-360px {
  height: 360px;
}

.p-5px {
  padding: 5px;
}

.font-bold {
  font-weight: 700;
}

.max-h-200px {
  max-height: 200px;
}

.w-800px {
  width: 800px;
}
.mr-30px {
  margin-right: 30px;
}

.w-300px {
  width: 300px;
}
.h-190px {
  height: 190px;
}

.pl-10px {
  padding-left: 10px;
}

.m-10px {
  margin: 10px;
}

.min-w-\[300px\] {
  min-width: 300px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.h-400px {
  height: 400px;
}

.font-bold {
  font-weight: 700;
}

.min-w-800px {
  min-width: 800px;
}
.pt-10px {
  padding-top: 10px;
}

.w-1000px {
  width: 1000px;
}

.min-w-1000px {
  min-width: 1000px;
}

.w-100px {
  width: 100px;
}

.h-100px {
  height: 100px;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.ml-5px {
  margin-left: 5px;
}

.mb-10px {
  margin-bottom: 10px;
}

.italic {
  font-style: italic;
}

p {
  margin: 0 0 16px;
}

.m-10px {
  margin: 10px;
}

.p-20px {
  padding: 20px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.justify-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.w-1384px {
  width: 1384px;
}

.min-w-1384px {
  min-width: 1384px;
}
.no-wrap {
  flex-wrap: nowrap;
}

.justify-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}
.flex-nowrap {
  -ms-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  flex-wrap: nowrap;
}
.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}
.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
}
.flex-row {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  -webkit-flex-direction: row;
  flex-direction: row;
}
.justify-end {
  justify-content: flex-end;
}

.column {
  flex-direction: column;
}
.p-10px {
  padding: 10px;
}

.m-5px {
  margin: 5px;
}
.w-150px {
  width: 150px;
}
.h-300px {
  height: 300px;
}

.px-10px {
  padding-left: 10px;
  padding-right: 10px;
}

.min-w-700px {
  min-width: 700px;
}

.mt-10px {
  margin-top: 10px;
}

img {
  border-style: none;
}

/* Quasar */
.q-table--flat {
  box-shadow: none;
}

.q-table__container {
  position: relative;
}

.q-table__card {
  color: #000;
  background-color: #fff;
  border-radius: 0;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2), 0 2px 2px rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.12);
}

.q-table__container > div:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

.q-table__container > div:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.q-table__card .q-table__middle {
  flex: 1 1 auto;
}

.scroll,
.scroll-x,
.scroll-y {
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

.scroll {
  overflow: auto;
}

.q-table__middle {
  max-width: 100%;
}

.q-table {
  width: 100%;
  max-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.q-table tbody {
  vertical-align: middle;
}

.q-table tbody td {
  font-size: 13px;
  position: relative;
}

.q-table--dense .q-table thead tr,
.q-table--dense .q-table tbody tr,
.q-table--dense .q-table tbody td {
  height: 28px;
}

.q-table thead,
.q-table tr,
.q-table th,
.q-table td {
  border-color: var(--color-border);
}

.q-table--dense .q-table th:first-child,
.q-table--dense .q-table td:first-child {
  padding-left: 10px;
}

.q-table--dense .q-table th:first-child,
.q-table--dense .q-table td:first-child {
  padding-left: 16px;
}

.q-table--dense .q-table th,
.q-table--dense .q-table tbody td {
  height: 31px;
  padding: 4px 10px;
}

.q-table--dense .q-table thead tr,
.q-table--dense .q-table tbody tr,
.q-table--dense .q-table tbody td {
  height: 28px;
}

.q-table--dense .q-table th,
.q-table--dense .q-table td {
  padding: 4px 8px;
}

.q-table tbody td {
  font-size: 13px;
  position: relative;
}

.q-table thead,
.q-table tr,
.q-table th,
.q-table td {
  border-color: var(--color-border);
}

.q-table--no-wrap th,
.q-table--no-wrap td {
  white-space: nowrap;
}

.q-table thead,
.q-table td,
.q-table th {
  border-style: solid;
  border-width: 0;
}

.q-table th,
.q-table td {
  padding: 7px 16px;
  background-color: inherit;
}

.text-left {
  text-align: left;
}

.py-10px {
  padding-top: 10px;
  padding-bottom: 10px;
}

.mt-20px {
  margin-top: 20px;
}

.py-0px {
  padding-top: 0px;
  padding-bottom: 0px;
}

.min-h-25px {
  min-height: 25px;
}

.no-wrap {
  flex-wrap: nowrap;
}

.row {
  display: flex;
}

.q-item {
  color: inherit;
  transition: color 0.3s, background-color 0.3s;
  position: relative;
}

.pr-7px {
  padding-right: 7px;
}

/* Components */
.overflow-hidden {
  overflow: hidden !important;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

.container {
  width: 100%;
}

.font-bold {
  font-weight: 700;
}

h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

h3 {
  font-size: 18px;
  font-weight: 600;
  line-height: 1.45;
  letter-spacing: normal;
}

.builder-module {
  min-height: 200px;
  margin-bottom: 12px;
  overflow: auto;
}

.min-height-auto {
  min-height: auto;
}

.aplus-preview-module .text-wrap-word-break {
  text-wrap: wrap;
  word-break: break-word;
}

/* .company-logo-preview */
.company-logo-preview .company-logo-content {
  height: 200px;
  padding: 10px 20px;
  display: flex;
  justify-content: center;
}

.company-logo-preview .company-logo-content img {
  display: block;
  height: 180px;
  width: 600px;
  min-width: 600px;
  object-fit: cover;
}

/* .comparison-chart-preview */
.comparison-chart-preview .comparison-chart-block-title {
  width: 180px;
}

.comparison-chart-preview img {
  display: block;
  object-fit: cover;
}

.comparison-chart-preview td:first-child {
  background-color: var(--color-grey-lighter);
  font-weight: bold;
}

.comparison-chart-preview td {
  width: 180px;
  word-break: break-word;
  text-wrap: wrap;
  overflow-wrap: anywhere;
}

.comparison-chart-preview .highlighted {
  border: 0.95px solid var(--color-black);
  transform: scale(1);
  box-shadow: 0px 4px 8px rgba(1, 1, 1, 0.75);
}

/* .four-images-text-quadrant-preview */
.four-images-text-quadrant-preview .four-image .image {
  object-fit: cover;
  width: 135px;
  min-width: 135px;
  height: 135px;
  min-height: 135px;
}

.four-images-text-quadrant-preview .description {
  padding: 0px;
}

.four-images-text-quadrant-preview img {
  display: block;
  object-fit: cover;
}

.four-images-text-quadrant-preview .headline {
  font-size: 18px;
  padding-left: 30px;
  font-weight: bold;
  width: 335px;
}

/* .header-image-text-preview */
.header-image-text-preview {
  padding: 10px 20px;
}

.header-image-text-preview .image {
  min-width: 970px;
  width: 970px;
  height: 600px;
  object-fit: cover;
}

.header-image-text-preview img {
  display: block;
  object-fit: cover;
}

/* .images-and-text-preview */
.images-and-text-preview .module-block.three-image {
  width: 310px;
  min-width: 310px;
}

.images-and-text-preview .three-image .image {
  width: 300px;
  height: 300px;
}

.images-and-text-preview img {
  display: block;
  object-fit: cover;
}

.images-and-text-preview .headline {
  font-size: 18px;
  font-weight: bold;
  width: 335px;
}

.images-and-text-preview .module-block.four-image {
  width: 230px;
  min-width: 230px;
}

.images-and-text-preview .four-image .image {
  width: 220px;
  height: 220px;
}

/* .images-sidebar-preview */
.images-sidebar-preview .left-column {
  width: 300px;
}

.images-sidebar-preview .middle-column {
  width: 380px;
}

.images-sidebar-preview .bullets {
  background-color: var(--color-grey-10);
  border: 1px solid var(--color-grey-light);
}

.images-sidebar-preview .right-column {
  width: 230px;
  border-left: 1px solid var(--color-grey-light);
}

/* .image-text-overlay-preview */
.image-text-overlay-preview {
  overflow: hidden;
}

.image-text-overlay-preview .container {
  position: relative;
  width: 970px;
  height: 300px;
}

.image-text-overlay-preview .container img {
  object-fit: cover;
}

.image-text-overlay-preview img {
  display: block;
  object-fit: cover;
}

.image-text-overlay-preview .container .overlay {
  max-width: 400px;
  overflow: hidden;
  position: absolute;
  right: 50px;
  top: 20%;
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--surface-color);
  padding: 10px;
}

/* .multiple-image-text-preview */
.multiple-image-text-preview .image {
  object-fit: cover;
  width: 300px;
  height: 300px;
}

.multiple-image-text-preview img {
  display: block;
  object-fit: cover;
}

.multiple-image-text-preview .headline {
  font-size: 18px;
  padding: 10px;
  padding-left: 30px;
  font-weight: bold;
}

/* .text-preview */
.text-preview .main-headline {
  font-size: 18px;
  font-weight: bold;
}

/* .single-images-highlight-preview */
.single-images-highlight-preview .left-column {
  width: 300px;
}

.single-images-highlight-preview .image {
  object-fit: unset;
  width: 300px;
  height: 300px;
  min-width: 300px;
  min-height: 300px;
}

.single-images-highlight-preview img {
  display: block;
}

.single-images-highlight-preview .middle-column {
  width: 380px;
}

.single-images-highlight-preview .headline {
  font-size: 14px;
  font-weight: bold;
}

.single-images-highlight-preview .subheadline {
  font-size: 13px;
  color: var(--color-grey-dark);
  margin-top: 10px;
  margin-left: 10px;
}

.single-images-highlight-preview .bullets {
  background-color: var(--color-grey-10);
  border: 1px solid var(--color-grey-light);
  margin-top: 0px;
}

.single-images-highlight-preview .right-column {
  width: 230px;
}

/* .single-image-spec-preview */
.single-image-spec-preview .main-headline {
  font-size: 18px;
  font-weight: bold;
}

.single-image-spec-preview .left-column {
  width: 300px;
}

.single-image-spec-preview .image {
  width: 300px;
  height: 300px;
  min-width: 300px;
  min-height: 300px;
  object-fit: unset;
}

.single-image-spec-preview img {
  display: block;
}

.single-image-spec-preview .middle-column {
  width: 380px;
}

.single-image-spec-preview .headline {
  font-size: 14px;
  font-weight: bold;
}

.single-image-spec-preview .subheadline {
  font-size: 13px;
  color: var(--color-grey-dark);
}

.single-image-spec-preview .right-column {
  width: 230px;
}

.single-image-spec-preview .bullet-list {
  background-color: var(--surface-color);
  border: none;
  margin-top: 0px;
}

/* .single-side-image-preview */
.single-side-image-preview .image {
  object-fit: unset;
  width: 300px;
  height: 300px;
  min-width: 300px;
  min-height: 300px;
}

.single-side-image-preview img {
  display: block;
}

/* .tech-spec-preview */
.tech-spec-preview .main-headline {
  font-size: 18px;
  font-weight: bold;
}

.tech-spec-preview .q-table__container {
  position: relative;
}

.tech-spec-preview .q-table__container > div:last-child {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
}

.tech-spec-preview .q-table__container > div:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}

.tech-spec-preview .q-table__card .q-table__middle {
  flex: 1 1 auto;
}

.tech-spec-preview .q-table {
  width: 100%;
  max-width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.tech-spec-preview .q-table tbody {
  vertical-align: middle;
}

.tech-spec-preview .q-table--dense .q-table tbody tr {
  height: 28px;
}

.tech-spec-preview td:first-child {
  background-color: var(--color-grey-lighter);
}

.tech-spec-preview .q-table--dense .q-table td:first-child {
  padding-left: 10px;
}

.tech-spec-preview .q-table--horizontal-separator tbody tr:not(:last-child) > td {
  border-bottom-width: 1px;
}

.tech-spec-preview .q-table--dense .q-table th,
.tech-spec-preview .q-table--dense .q-table tbody td {
  height: 31px;
  padding: 4px 10px;
}
