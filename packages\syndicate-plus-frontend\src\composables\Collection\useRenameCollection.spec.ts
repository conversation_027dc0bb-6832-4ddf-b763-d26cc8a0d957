import { describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useRenameCollection } from '@composables/Collection';

describe('useRenameCollection', () => {
  it('should set the old value as default', async () => {
    // Arrange
    const oldCollectionName = 'old name';
    const collectionId = 1;
    const isLoading = ref(false);

    // Act
    const { newCollectionName } = useRenameCollection(isLoading, oldCollectionName, collectionId);

    // Assert
    expect(newCollectionName.value).toBe(oldCollectionName);
  });

  it('should calculate correct renameCollectionIsDisabled value for different input', async () => {
    // Arrange
    const testData = [
      { oldCollectionName: 'old name', collectionName: '', isLoadingState: true, expectedValue: true },
      { oldCollectionName: 'old name', collectionName: 'test collection', isLoadingState: true, expectedValue: true },
      { oldCollectionName: 'old name', collectionName: '', isLoadingState: false, expectedValue: true },
      { oldCollectionName: 'old name', collectionName: 'test collection', isLoadingState: false, expectedValue: false },
      { oldCollectionName: 'old name', collectionName: 'old name', isLoadingState: false, expectedValue: true },
      { oldCollectionName: 'old name', collectionName: 'Old name', isLoadingState: false, expectedValue: false },
      {
        oldCollectionName: 'old name',
        collectionName: '    old name    ',
        isLoadingState: false,
        expectedValue: true,
      },
    ];

    testData.forEach(({ oldCollectionName, collectionName, isLoadingState, expectedValue }) => {
      const collectionId = 1;
      const isLoading = ref(false);
      const { newCollectionName, renameCollectionIsDisabled } = useRenameCollection(
        isLoading,
        oldCollectionName,
        collectionId
      );

      // Act
      newCollectionName.value = collectionName;
      isLoading.value = isLoadingState;

      // Assert
      expect(renameCollectionIsDisabled.value).toBe(expectedValue);
    });
  });
});
