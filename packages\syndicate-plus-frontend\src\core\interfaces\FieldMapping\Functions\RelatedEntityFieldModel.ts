import { EntityTypeRelations } from '@core/enums';
import { EntityType, InriverFieldType, Relative } from '@core/interfaces';
import { Language } from '@customTypes/AppData';
import { RelatedEntityResponse } from './RelatedEntity';

// Related entity field function form model
export interface RelatedEntityFieldModel {
  mainEntityType: EntityType;
  relations: RelationsModel[];
  entity: RelatedEntityResponse;
  field: InriverFieldType;
  language?: Language;
}

export interface RelationsModel {
  relation: EntityTypeRelations;
  relative?: Relative;
}

export interface RelationsObject {
  mainEntityTypeId: string;
  relations: Relation;
  fieldTypeId: string;
  language: string;
  entityId: string;
}

export interface Relation {
  [key: number]: {
    direction: EntityTypeRelations;
    dropdownValue: string;
  };
}

export interface DropdownValue {
  entityTypeId: string;
  linkEntityTypeId: string;
}
