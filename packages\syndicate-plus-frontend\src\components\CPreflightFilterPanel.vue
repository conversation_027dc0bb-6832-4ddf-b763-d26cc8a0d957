<template>
  <div class="filter-panel">
    <q-tab-panel name="filter" class="p-0 w-[calc(100%)]">
      <c-separated-expansion-item
        :is-expanded="isExpanded"
        :label="$t('syndicate_plus.preflight.filter.errorsAndWarningsTitle')"
      >
        <q-checkbox
          :model-value="localFilter.columnsWithErrors"
          :label="$t('syndicate_plus.preflight.filter.columnsWithErrors')"
          v-bind="$inri.checkbox"
          @click="localFilter.columnsWithErrors = !localFilter.columnsWithErrors"
        />
        <br />
        <q-checkbox
          :model-value="localFilter.columnsWithWarnings"
          :label="$t('syndicate_plus.preflight.filter.columnsWithWarnings')"
          v-bind="$inri.checkbox"
          @click="localFilter.columnsWithWarnings = !localFilter.columnsWithWarnings"
        />
        <br />
        <q-checkbox
          :model-value="localFilter.rowsWithErrors"
          :label="$t('syndicate_plus.preflight.filter.rowsWithErrors')"
          v-bind="$inri.checkbox"
          @click="localFilter.rowsWithErrors = !localFilter.rowsWithErrors"
        />
        <br />
        <q-checkbox
          :model-value="localFilter.rowsWithWarnings"
          :label="$t('syndicate_plus.preflight.filter.rowsWithWarnings')"
          v-bind="$inri.checkbox"
          @click="localFilter.rowsWithWarnings = !localFilter.rowsWithWarnings"
        />
      </c-separated-expansion-item>
      <c-separated-expansion-item
        v-if="isFilterEditViewVisible"
        :is-expanded="expandedEditFilters"
        :label="$t('syndicate_plus.preflight.editable_fields.title')"
      >
        <c-filter-radio-options-section
          :class="isFilterAvailable ? '' : 'disabled'"
          :options="expandedEditFiltersOptions"
          :selected-item="selectedEditFilterOption"
          @apply-filter="applyEditFilter"
        />
      </c-separated-expansion-item>
      <c-separated-expansion-item
        :is-expanded="expandedMandatoryFilters"
        :label="$t('syndicate_plus.preflight.mandatory_fields.title')"
      >
        <c-filter-radio-options-section
          :options="expandedMandatoryFiltersOptions"
          :selected-item="selectedMandatoryFilterOption"
          @apply-filter="applyMandatoryFilter"
        />
      </c-separated-expansion-item>
    </q-tab-panel>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, Ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { PreflightFilter } from '../types/preflightFilter';
import { EditViewFilterFieldOption, MandatoryFieldsFilterOption } from '@customTypes';
import { CSeparatedExpansionItem } from '@components/Shared';
import { CFilterRadioOptionsSection } from '@components';
import { EditableTableViewFilterType, TableViewFilterType } from '@enums';

const { t } = useI18n();

const isExpanded = ref(true);
const expandedEditFilters = ref(true);
const expandedMandatoryFilters = ref(true);

const props = defineProps({
  preflightFilter: {
    type: Object as () => PreflightFilter,
    default: () => ({}),
  },
  isFilterAvailable: {
    type: Boolean,
    default: true,
  },
  isFilterEditViewVisible: {
    type: Boolean,
    default: false,
  },
});

const localFilter = ref({ ...props.preflightFilter });
const expandedEditFiltersOptions = computed(() => {
  return [
    {
      id: EditableTableViewFilterType.SHOW_ALL,
      label: t('syndicate_plus.preflight.editable_fields.show_all_fields'),
    },
    {
      id: EditableTableViewFilterType.SHOW_ONLY_EDITABLE,
      label: t('syndicate_plus.preflight.editable_fields.only_show_editable_fields'),
    },
  ];
});

const expandedMandatoryFiltersOptions = computed(() => {
  return [
    {
      id: TableViewFilterType.SHOW_ALL,
      label: t('syndicate_plus.preflight.mandatory_fields.show_all_fields'),
    },
    {
      id: TableViewFilterType.SHOW_ONLY_MANDATORY,
      label: t('syndicate_plus.preflight.mandatory_fields.show_only_mandatory_fields'),
    },
    {
      id: TableViewFilterType.SHOW_ONLY_RECOMMENDED,
      label: t('syndicate_plus.preflight.mandatory_fields.show_only_recommended_fields'),
    },
  ];
});

const selectedEditFilterOption: Ref<EditViewFilterFieldOption> = ref(
  expandedEditFiltersOptions.value.find((x) => x.id === EditableTableViewFilterType.SHOW_ALL) ||
    expandedEditFiltersOptions.value[0]
);

const selectedMandatoryFilterOption: Ref<MandatoryFieldsFilterOption> = ref(
  expandedMandatoryFiltersOptions.value.find((x) => x.id === TableViewFilterType.SHOW_ALL) ||
    expandedMandatoryFiltersOptions.value[0]
);

const emit = defineEmits(['filterChangedEvent', 'applyEditFilter', 'applyMandatoryFilter']);

watch(localFilter.value, (option) => emit('filterChangedEvent', option), {
  immediate: true,
  deep: true,
});

const applyEditFilter = (selectedItem: EditViewFilterFieldOption): void => {
  selectedEditFilterOption.value = selectedItem;
  emit('applyEditFilter', selectedItem);
};

const applyMandatoryFilter = (selectedOption: MandatoryFieldsFilterOption): void => {
  selectedMandatoryFilterOption.value = selectedOption;
  emit('applyMandatoryFilter', selectedOption);
};
</script>

<style lang="scss" scoped>
.filter-panel {
  position: absolute;
  width: 350px;
  min-width: 350px;
  height: 100vh;
  z-index: 100;
  background-color: var(--on-primary-color);
  box-shadow: 2px 0 10px 0 var(--color-border);
}

.disabled {
  pointer-events: none;
}
</style>
