import { ref } from 'vue';
import { ContentModule, FourImageTextQuadrantData } from '@customTypes/Aplus';

export default function useFourImageTextQuadrant() {
  // Refs
  const module = ref<ContentModule<FourImageTextQuadrantData>>({} as ContentModule<FourImageTextQuadrantData>);

  // Functions
  const initData = () => {
    if (!Object.keys(module.value.data)?.length) {
      module.value.data = {
        image1: {
          altText: '',
          angle: '',
        },
        headline1: '',
        body1: '',
        image2: {
          altText: '',
          angle: '',
        },
        headline2: '',
        body2: '',
        image3: {
          altText: '',
          angle: '',
        },
        headline3: '',
        body3: '',
        image4: {
          altText: '',
          angle: '',
        },
        headline4: '',
        body4: '',
      } as FourImageTextQuadrantData;
    }
  };

  return { module, initData };
}
