<template>
  <div class="info-container" :class="customClass">
    <p class="info-title">
      {{ title }}
    </p>
    <span class="info-description" v-html="description" />
    <c-tile-btn
      small
      class="close-button"
      :class="customBtnClass"
      :color="btnBgColor"
      icon="mdi-close"
      :tooltip="$t('syndicate_plus.common.close')"
      @click="$emit('closeInfoBlock')"
    />
  </div>
</template>
<script setup lang="ts">
import { computed, PropType } from 'vue';
import { InfoColor } from '@enums';

const props = defineProps({
  bgColor: {
    type: String as PropType<InfoColor>,
    default: '',
  },
  borderColor: {
    type: String as PropType<InfoColor>,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
});

defineEmits(['closeInfoBlock']);

const customClass = computed(() => (props.bgColor ? `bg-${props.bgColor} ${props.borderColor}` : ''));
const customBtnClass = computed(() => (props.bgColor ? `btn-${props.bgColor}` : ''));
const btnBgColor = computed(() => {
  if (props.bgColor === InfoColor.LIGHTORANGE) {
    return 'rgb(255, 168, 116, 0.4)';
  }
  return props.bgColor ? `${props.bgColor}` : '--surface-color';
});
</script>
<style lang="scss" scoped>
.info-container {
  padding-left: 26px;
  padding-top: 18px;
  padding-right: 10px;
  padding-bottom: 18px;
  margin-bottom: 8px;
  min-height: 80px;
  position: relative;

  .info-title {
    font-weight: bold;
    margin: 0 0 2px 0;
  }

  .close-button {
    position: absolute;
    top: 10px;
    right: 10px;
  }
}

.bg-orange-light {
  background-color: rgb(255, 168, 116, 0.4);
}

.btn-orange-light {
  color: var(--color-orange);
}

.orange {
  border-left: 4px solid var(--color-orange);
}
</style>
