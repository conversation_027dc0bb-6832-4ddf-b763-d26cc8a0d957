import { beforeEach, describe, expect, it, Mock, vi, vitest } from 'vitest';
import { ref } from 'vue';
import { useEditPriceButton } from '@composables/Buttons';
import { ProductGrouping, SyndicationPageTabNames } from '@enums';
import { useRouter } from '@composables/useRouter';

vi.mock('@composables/useRouter');
vi.mock('@inriver/inri');

const useI18n = vitest.fn();
useI18n.mockReturnValue({ t: vi.fn() });

describe('useEditPriceButton', async () => {
  const mockRouter = {
    goToPage: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as Mock).mockReturnValue(mockRouter);
  });

  describe('isEditPriceButtonVisible', async () => {
    it('shows view price button correctly', async () => {
      const testCases = [
        { activeTab: SyndicationPageTabNames.Products, numberOfProducts: 4, isVisible: true },
        { activeTab: SyndicationPageTabNames.Products, numberOfProducts: 0, isVisible: false },
        { activeTab: SyndicationPageTabNames.Collection, numberOfProducts: 3, isVisible: false },
        { activeTab: SyndicationPageTabNames.Products, numberOfProducts: 10, isVisible: true },
        { activeTab: SyndicationPageTabNames.Outbox, numberOfProducts: 0, isVisible: false },
        { activeTab: null, numberOfProducts: 0, isVisible: false },
        { activeTab: null, numberOfProducts: 1, isVisible: true },
      ];

      testCases.forEach(({ activeTab, numberOfProducts, isVisible }) => {
        // Arrange
        const tab = ref<SyndicationPageTabNames | null>(SyndicationPageTabNames.Collection);
        const numberOfSelectedProducts = ref(0);
        const destinationId = 1;
        const subCatalogId = 2;
        const { isEditPriceButtonVisible } = useEditPriceButton(
          useI18n,
          tab,
          numberOfSelectedProducts,
          destinationId,
          subCatalogId
        );

        // Act
        tab.value = activeTab;
        numberOfSelectedProducts.value = numberOfProducts;

        // Assert
        expect(isEditPriceButtonVisible.value).toBe(isVisible);
      });
    });
  });

  describe('isGoToEditPriceAllowed', async () => {
    it('enables price button correctly', async () => {
      const testCases = [
        { numberOfProducts: -1, isEnabled: false },
        { numberOfProducts: 0, isEnabled: false },
        { numberOfProducts: 1, isEnabled: true },
        { numberOfProducts: 5, isEnabled: true },
        { numberOfProducts: 6, isEnabled: false },
      ];

      testCases.forEach(({ numberOfProducts, isEnabled }) => {
        // Arrange
        const tab = ref(SyndicationPageTabNames.Collection);
        const numberOfSelectedProducts = ref(0);
        const destinationId = 1;
        const subCatalogId = 2;
        const { isGoToPriceAllowed } = useEditPriceButton(
          useI18n,
          tab,
          numberOfSelectedProducts,
          destinationId,
          subCatalogId
        );

        // Act
        numberOfSelectedProducts.value = numberOfProducts;

        // Assert
        expect(isGoToPriceAllowed.value).toBe(isEnabled);
      });
    });
  });

  describe('goToEditPricePage', async () => {
    it('calls goToPage correctly', async () => {
      const testCases = [
        {
          selectedProductNames: ['a', 'b'],
          expectedQuery: {
            productNames: 'a,b',
            skus: '',
          },
        },
        {
          selectedProductNames: ['a', 'b', 'c'],
          expectedQuery: {
            productNames: 'a,b,c',
            skus: '',
          },
        },
        {
          selectedProductNames: ['a', 'b', 'c', 'd'],
          expectedQuery: {
            productNames: 'a,b,c,d',
            skus: '',
          },
        },
      ];

      testCases.forEach(({ selectedProductNames, expectedQuery }) => {
        // Arrange
        const tab = ref(SyndicationPageTabNames.Products);
        const numberOfSelectedProducts = ref(selectedProductNames.length);
        const destinationId = 1;
        const subCatalogId = 2;
        const { goToEditPricePage } = useEditPriceButton(
          useI18n,
          tab,
          numberOfSelectedProducts,
          destinationId,
          subCatalogId
        );

        // Act
        goToEditPricePage(ProductGrouping.SKU, [], selectedProductNames);

        // Assert
        expect(mockRouter.goToPage).toHaveBeenCalledWith('price-page', { destinationId, subCatalogId }, expectedQuery);
      });
    });
  });
});
