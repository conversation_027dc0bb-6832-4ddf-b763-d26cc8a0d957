variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'use'
  stackType: 'test1a'

  # Deploy dependencies
  dependsOn: 'Build_useTest1a'
  dependsOnSecond: 'Build_useTest1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-test1a-use.productmarketingcloud.com'

  auth0Domain: 'auth-dev.syndic8.io'
  auth0ClientId: 'ZmDqG4j7MFQVr2yougZHmhPOZAoZlGAy'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: 'https://api.syndic8.io'
  fileServerUrl: 'https://app.syndic8.io'

  allowMissingOrgMapping: false
  
  # Application insights instrumentation key
  instrumentationKey: 'a44afb16-69be-4869-a6aa-a6f483c61e3a'

  # Infrared override
  infraredAuth0Domain: 'auth-dev.syndic8.io'
  infraredAuth0ClientId: 'twD9zFV3gROvoMmRyVDq2kU6fcjZsATW'
  infraredAuth0Connection: 'inriver-prod-openid'
  infraredApiUrl: 'https://api.infrared.inriver.syndic8.io'
  infraredFileServerUrl: 'https://app.infrared.inriver.syndic8.io'
  infraredInstrumentationKey: 'a44afb16-69be-4869-a6aa-a6f483c61e3a'
  infraredEnvs: ''
