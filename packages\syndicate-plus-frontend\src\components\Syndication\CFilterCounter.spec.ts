import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';

import { CFilterCounter } from '@components';
import { QBadge } from 'quasar';

describe('CFilterCounter', () => {
  it('should not display if parameter isVisible is false', async () => {
    // Arrange
    const wrapper = mount(CFilterCounter, {
      props: {
        isVisible: false,
        labelText: 10,
      },
    });

    // Act + Assert
    expect(wrapper.find('.circle-container').exists()).toBe(false);
  });

  it('should not display if parameter label is 0', async () => {
    // Arrange
    const wrapper = mount(CFilterCounter, {
      props: {
        isVisible: true,
        labelText: 0,
      },
    });

    // Act + Assert
    expect(wrapper.find('.circle-container').exists()).toBe(false);
  });

  it('should display filter', async () => {
    // Arrange
    const wrapper = mount(CFilterCounter, {
      props: {
        isVisible: true,
        labelText: 10,
      },
      global: {
        components: QBadge,
      },
    });

    // Act + Assert
    expect(wrapper.find('.q-badge').text()).toBe('10');
  });
});
