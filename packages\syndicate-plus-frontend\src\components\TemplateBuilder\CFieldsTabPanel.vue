<template>
  <div>
    <c-inri-search
      v-model="searchValue"
      class="c-inri-custom-search"
      :class="customSearchClass"
      dense
      :placeholder="$t('syndicate_plus.common.filter.search')"
      @search="search"
      @keydown.enter.prevent="search"
      @update:model-value="onSearchValueChanged"
      @clear="clearSearch"
    />

    <div v-if="!loadingFields">
      <draggable
        v-if="currentTemplateFields?.length"
        v-model="currentTemplateFields"
        :disabled="fieldsMoveIsDisabled"
        :group="{
          name: 'templateFields',
          pull: 'clone',
          put: false,
        }"
        :sort="false"
        item-key="syndicationFieldId"
        ghost-class="builder-ghost"
        @start="onDragStart"
      >
        <template #item="{ element }">
          <div class="field-item" :class="{ 'cursor-pointer': !fieldsMoveIsDisabled }">
            {{ element.syndicationFieldId }} ({{ getSourceFieldDisplayName(element.sourceFieldName) }})
          </div>
        </template>
      </draggable>
      <div v-else>{{ $t('syndicate_plus.aplus_define_template.no_mapping_fields') }}</div>
    </div>
    <div v-else-if="loadingFields">
      <q-inner-loading showing color="primary" class="spinner">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, toRef, onBeforeMount, computed } from 'vue';
import { storeToRefs } from 'pinia';
import draggable from 'vuedraggable';
import { SearchState } from '@enums';
import { CInriSearch } from '@components';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { useTemplatesStore } from '@stores/TemplatesStore';
import { useAdapterSettingsStore } from '@stores';
import { DataFlowService } from '@services/DataFlow';
import { HttpAuth0Client } from '@httpservices/HttpAuth0Client';
import { TemplateDefinition, TemplateMappingEntry, InboundTemplateField } from '@customTypes';
import { getSourceFieldDisplayName } from '@helpers';
import { CoreFieldMapping } from '@customTypes/Extension';
import { CoreFieldMatcher } from '@utils';

const props = defineProps({
  isPreview: {
    type: Boolean,
    default: false,
  },
  isFieldsTabEnabled: {
    type: Boolean,
    default: false,
  },
  tradingPartnerName: {
    type: String,
    default: '',
  },
});

const store = useTemplateBuilderStore();
const fieldsTabStore = useFieldsTabStore();
const { draggedField } = storeToRefs(fieldsTabStore);

// Refs
const isPreviewMode = toRef(props, 'isPreview');
const isFieldsEnabled = toRef(props, 'isFieldsTabEnabled');
const templateFields = ref<[] | InboundTemplateField[]>();
const selectedTemplate = ref<TemplateDefinition>();
const inboundTemplates = ref<TemplateDefinition[]>([]);
const loadingFields = ref<boolean>(false);
const searchValue = ref('');
const searchState = ref(SearchState.SearchNotStarted);
const filteredFields = ref<[] | InboundTemplateField[]>();

// Computed
const fieldsMoveIsDisabled = computed(
  () => isPreviewMode.value || !isFieldsEnabled.value || !store.hasContentModules()
);

const currentTemplateFields = computed(() => (searchValue.value ? filteredFields.value : templateFields.value));

const customSearchClass = computed(() => {
  const currentSearchState = searchState.value;
  switch (currentSearchState) {
    case SearchState.SearchNotStarted:
      return '';
    case SearchState.SearchPending:
      return 'search-pending';
    case SearchState.SearchFinished:
      return 'search-finish';
    default:
      return '';
  }
});

// Composables
const { fetchTemplates, fetchTemplatesDefinitions, getTemplateById, getTemplateDefinitionsByTemplateIds } =
  useTemplatesStore();
const adapterSettingsStore = useAdapterSettingsStore();
const { coreFieldMappingSetting } = storeToRefs(useAdapterSettingsStore());

// Functions
const onDragStart = (e) => {
  if (!currentTemplateFields.value) {
    return;
  }

  draggedField.value = currentTemplateFields.value[e.oldIndex];
};

const loadData = async (): Promise<void> => {
  try {
    if (selectedTemplate.value !== undefined) {
      const orgId = await HttpAuth0Client.getInstance().getUserOrganizationId();
      await fetchTemplates(selectedTemplate.value.id.toString(), orgId);
      const template = getTemplateById(selectedTemplate.value.id.toString());
      const templateFieldsWithDuplicates = toTemplateFields(
        template.fields,
        coreFieldMappingSetting.value?.CoreFieldMapping
      )?.filter((field) => field.sourceFieldName && field.sourceFieldName !== 'NOCOLUMN');
      templateFields.value = [
        ...new Map(templateFieldsWithDuplicates?.map((item) => [item.syndicationFieldId, item])).values(),
      ];
    }
  } finally {
    loadingFields.value = false;
  }
};

async function search() {
  if (loadingFields.value) {
    await delay(500);
    await search();
    return;
  }
  performSearch(searchValue.value);
}

async function delay(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function onSearchValueChanged(): void {
  filteredFields.value = [];
  if (!searchValue.value) {
    updateSearchState(SearchState.SearchNotStarted);
    search();
    return;
  }
  updateSearchState(SearchState.SearchPending);
  search();
}

function clearSearch() {
  searchValue.value = '';
}

function updateSearchState(searchStateNew: SearchState): void {
  searchState.value = searchStateNew;
}

function performSearch(searchValue: string) {
  if (!searchValue.trim() || !templateFields.value) {
    return;
  }

  updateSearchState(SearchState.SearchPending);
  filteredFields.value = templateFields.value.filter(
    (field) =>
      field.syndicationFieldId.toLowerCase().includes(searchValue.toLowerCase()) ||
      field.sourceFieldName.toLowerCase().includes(searchValue.toLowerCase())
  );
}

const toTemplateFields = (
  fields?: TemplateMappingEntry[],
  coreFieldMapping?: CoreFieldMapping
): InboundTemplateField[] | undefined => {
  const coreFieldMatcher = new CoreFieldMatcher(coreFieldMapping);

  return fields
    ?.filter((f) => f.outputField?.name && !f.hasNoColumnValue())
    .map((m) => m.toInboundTemplateField(coreFieldMatcher))
    .filter((templateField, index, self) => {
      return (
        self.findIndex(
          (v) =>
            v.syndicationFieldName === templateField.syndicationFieldName && v.sheetName === templateField.sheetName
        ) === index
      );
    });
};

// Lifecycle methods
onBeforeMount(async () => {
  if (isFieldsEnabled.value) {
    loadingFields.value = true;
    try {
      await fetchTemplatesDefinitions();
      const dataFlowTemplateIds = await DataFlowService.getDataFlowTemplateIds();
      if (!dataFlowTemplateIds.length) {
        loadingFields.value = false;
        return;
      }

      inboundTemplates.value = getTemplateDefinitionsByTemplateIds(dataFlowTemplateIds);
      inboundTemplates.value.sort((a, b) => {
        // Check if either string contains "master" (case-insensitive)
        const aHasMaster = a.templateName.toLowerCase().includes('master');
        const bHasMaster = b.templateName.toLowerCase().includes('master');

        // If a has "master" and b doesn't, a comes first (-1)
        if (aHasMaster && !bHasMaster) {
          return -1;
        }
        // If b has "master" and a doesn't, b comes first (1)
        if (!aHasMaster && bHasMaster) {
          return 1;
        }
        // If both have "master" or neither do, sort alphabetically
        return a.templateName.localeCompare(b.templateName);
      });
      if (!inboundTemplates.value?.length) {
        loadingFields.value = false;
        return;
      }

      selectedTemplate.value = inboundTemplates.value[0];
      await adapterSettingsStore.fetchCoreFieldMapping();
      await loadData();
    } catch (error) {
      console.error(error);
      loadingFields.value = false;
      templateFields.value = [];
    }
  }
});
</script>

<style lang="scss" scoped>
.field-item {
  position: relative;
  padding: 7px;
  border: 1px dashed var(--color-grey);
  border-radius: 8px;
  background-color: var(--color-grey-10);
  margin-bottom: 10px;
  text-wrap: wrap;
  word-break: break-word;
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);
    margin-bottom: 10px;

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    padding-left: 10px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }

  &.search-pending {
    .q-field__append span {
      color: var(--color-grey-darkest);
    }

    .icon-close {
      color: var(--color-grey-dark);
    }
  }

  &.search-finish {
    .icon-close {
      color: var(--color-grey-darkest);
    }
  }
}

.spinner {
  top: 80px;
}
</style>
