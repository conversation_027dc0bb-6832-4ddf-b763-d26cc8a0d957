import { ref, Ref, computed, ComputedRef } from 'vue';
import { StopCellEditType } from '@enums';
import { DataToUpdateModel } from '@customTypes';
import { useTableScroll } from '@composables/Preflight';
import {
  IStopCellEditStrategy,
  EnterStrategy,
  ShiftEnterStrategy,
  ShiftTabStrategy,
  TabStrategy,
} from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';

export default function useStopCellEdit(
  tableRef: Ref<any>,
  rows: Ref<any>,
  columns: Ref<any>,
  displayedColumns?: ComputedRef<string[]>
) {
  // Variables
  const defaultValue = -1;

  // Refs
  const nextColumnId = ref(defaultValue);
  const nextRowId = ref(defaultValue);

  const { scrollToCell, scrollToRow } = useTableScroll(tableRef);

  // Computed
  const fitleredColumns = computed(() =>
    displayedColumns?.value
      ? columns.value.filter((column: any) => displayedColumns.value.includes(column.name))
      : columns.value
  );

  // Functions
  const calculateNextRowAndColumn = (data: DataToUpdateModel, type: StopCellEditType) => {
    const strategies = {
      [StopCellEditType.Enter]: EnterStrategy,
      [StopCellEditType.ShiftEnter]: ShiftEnterStrategy,
      [StopCellEditType.Tab]: TabStrategy,
      [StopCellEditType.ShiftTab]: ShiftTabStrategy,
    };

    if (!strategies.hasOwnProperty(type)) {
      return;
    }

    const strategy = new strategies[type](rows.value, fitleredColumns.value) as IStopCellEditStrategy;
    const nextCellCoordinates = strategy.getNextRowAndColumnId(data.rowId, data.columnIndex);

    setNextCoordinates(nextCellCoordinates);
    setFocus(type);
  };

  const setNextCoordinates = (nextCoordinates: TableCellCoordinates) => {
    const { rowId, columnId } = nextCoordinates;

    nextRowId.value = rowId;
    nextColumnId.value = columnId;
  };

  const setFocus = (type: StopCellEditType) => {
    if (type !== StopCellEditType.Blur && type !== StopCellEditType.Esc) {
      scrollToRow(nextRowId.value);
    }

    if (type === StopCellEditType.Tab || type === StopCellEditType.ShiftTab) {
      scrollToCell(nextColumnId.value, nextRowId.value);
    }
  };

  return { nextColumnId, nextRowId, calculateNextRowAndColumn };
}
