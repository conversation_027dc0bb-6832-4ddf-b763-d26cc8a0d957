<template>
  <div class="mx-auto p-20px min-w-1040px w-1040px">
    <div class="flex flex-row flex-nowrap">
      <div class="left-column m-10px">
        <c-caption-angle-image-block
          v-model="module.data.imageCaption"
          :dimensions="dimensionsLeft"
          :caption-max-length="Validation.imageSidebar.imageCaption.maxLength"
          :max-alt-text-length="Validation.imageSidebar.imageCaptionAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url, false)"
        />
      </div>
      <div class="middle-column m-10px">
        <q-input
          v-model="module.data.headline"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.headline')"
          :maxlength="Validation.imageSidebar.headline.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.headline, 'headline')"
        />
        <q-input
          v-model="module.data.subheadline"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.subheadline')"
          :maxlength="Validation.imageSidebar.subheadline.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data.subheadline, 'subheadline')"
        />
        <c-text-editor
          v-model="module.data.body"
          :max-length="Validation.imageSidebar.body.maxLength"
          @on-field-drop="onDrop(module.data.body, 'body')"
        />
        <c-bullet-list
          v-model="module.data.bullets"
          :max-length="Validation.imageSidebar.bullets.maxLength"
          @update-model="updateModel"
        />
      </div>
      <div class="right-column m-10px">
        <c-caption-angle-image-block
          v-model="module.data.sidebarImageCaption"
          :dimensions="dimensionsRight"
          :caption-max-length="Validation.imageSidebar.sidebarImageCaption.maxLength"
          :max-alt-text-length="Validation.imageSidebar.sidebarImageCaptionAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url, true)"
        />
        <c-text-editor
          v-model="module.data.sidebarBody.content[0].content[0].text"
          :max-length="Validation.imageSidebar.sidebarBody.maxLength"
          @on-field-drop="onDropSidebarBody(module.data.sidebarBody.content[0].content[0].text)"
        />
        <c-bullet-list
          v-model="module.data.sidebarBullets"
          :max-length="Validation.imageSidebar.sidebarBullets.maxLength"
          @update-model="updateModel"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { ComponentType } from '@enums/Aplus';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, ImageSidebarData, Dimension } from '@customTypes/Aplus';
import { CCaptionAngleImageBlock, CBulletList, CTextEditor } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Variables
const dimensionsLeft = { width: 300, height: 400 } as Dimension;
const dimensionsRight = { width: 350, height: 175 } as Dimension;

// Refs
const module = ref<ContentModule<ImageSidebarData>>({} as ContentModule<ImageSidebarData>);

// Functions
const handleUrlUpdate = (url: string, isSidebar: boolean) => {
  if (isSidebar) {
    module.value.data.sidebarImageCaption.angle = url;
  } else {
    module.value.data.imageCaption.angle = url;
  }

  updateModel();
};

const onDrop = (value: string, moduleParameter) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data[moduleParameter] = result;
    updateModel();
  }
};

// TODO: Refactor this function
const onDropSidebarBody = (value: string) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data.sidebarBody.content[0].content[0].text = result;
    updateModel();
  }
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      body: '',
      bullets: [],
      headline: '',
      imageCaption: {
        altText: '',
        angle: '',
        caption: '',
      },
      sidebarBody: {
        content: [
          {
            attrs: {
              align: null,
            },
            content: [{ text: '', type: ComponentType.TEXT }],
            type: ComponentType.PARAGRAPH,
          },
        ],
        type: ComponentType.DOC,
      },
      sidebarBullets: [],
      sidebarImageCaption: {
        altText: '',
        angle: '',
        caption: '',
      },
      subheadline: '',
    } as ImageSidebarData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
<style lang="scss" scoped>
.left-column {
  flex-grow: 1;
}

.middle-column {
  flex-grow: 2;
}

.right-column {
  flex-grow: 1;
}
</style>
