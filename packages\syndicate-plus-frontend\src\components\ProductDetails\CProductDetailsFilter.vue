<template>
  <div class="filter-panel">
    <q-tabs
      v-model="tab"
      class="bg-white text-grey-7"
      active-color="primary"
      active-class="active-tab"
      indicator-color="transparent"
      align="justify"
      no-caps
    >
      <q-tab :name="FilterTabNames.FILTERS_TAB" :label="$t('syndicate_plus.common.filter.filters')" />
    </q-tabs>
    <q-tab-panels v-model="tab">
      <q-tab-panel :name="FilterTabNames.FILTERS_TAB" class="panel-content p-0">
        <c-fields-filter-section
          :is-expanded="expanded.fields"
          :columns="columns"
          :show-internal="showInternalNames"
          :is-columns-loading="isColumnsLoading"
          @apply-fields-filter="applyFieldsFilter"
        />
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref, toRef } from 'vue';
import { FilterTabNames } from '@enums';
import { ProductDetailsFilter, ExpandedFilterSections, FilterField } from '@customTypes/Products';
import { CFieldsFilterSection } from '@components/ProductDetails';

const props = defineProps({
  columns: {
    type: Array<FilterField>,
    default: [] as FilterField[],
  },
  isColumnsLoading: {
    type: Boolean,
    default: false,
  },
  showInternal: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['apply-filter']);

// Refs
const tab = ref(FilterTabNames.FILTERS_TAB);
const filter = ref<ProductDetailsFilter>();
const expanded = ref({
  fields: true,
} as ExpandedFilterSections);
const showInternalNames = toRef(props, 'showInternal');

// Functions
const applyFieldsFilter = (columns: string[]): void => {
  filter.value = {
    ...filter.value,
    visibleColumns: columns,
  };
  applyFilterSelections(filter.value);
};

const applyFilterSelections = (filter: ProductDetailsFilter): void => {
  emit('apply-filter', filter);
};
</script>

<style scoped lang="scss">
.filter-panel {
  position: absolute;
  width: 400px;
  height: calc(100vh - 52.5px);
  z-index: 3;
  background-color: var(--on-primary-color);
  box-shadow: 2px 0 10px 0 var(--color-border);

  .panel-content {
    height: calc(100vh - 175px);
  }
}
</style>
