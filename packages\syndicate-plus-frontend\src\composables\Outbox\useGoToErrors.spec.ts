import useGoToErrors from '@composables/Outbox/useGoToErrors';
import { ref } from 'vue';
import { describe, expect, it, vitest } from 'vitest';

import { useRouter } from '@composables/useRouter';
import { OutboxRowType } from '@customTypes/OutboxRowType';
import { SyndicationPageTabNames } from '@enums';

vitest.mock('@composables/useRouter');

(useRouter as any).mockReturnValue({ goToPage: vitest.fn() });

const { goToPage } = useRouter();

const outboxTab = ref(SyndicationPageTabNames.Outbox);
const productsTab = ref(SyndicationPageTabNames.Products);
describe('useGoToErrors', () => {
  describe('goToErrors', () => {
    it('should throw error if no row selected', () => {
      const selectedRow = ref(undefined);
      const { goToErrors } = useGoToErrors(outboxTab, selectedRow);
      expect(() => goToErrors()).toThrowError();
    });

    it('should go to page if row selected', () => {
      const selectedRow = ref({ id: 50 });
      const { goToErrors } = useGoToErrors(outboxTab, selectedRow);
      goToErrors();
      expect(goToPage).toHaveBeenCalled();
    });
  });

  describe('showGoToErrors', () => {
    it('should be false if no row selected', () => {
      const selectedRow = ref(undefined);
      const { showGoToErrors } = useGoToErrors(outboxTab, selectedRow);
      expect(showGoToErrors.value).toBeFalsy();
    });

    it('should be false if selected row has no errors', () => {
      const selectedRow = ref({ id: 50, failedRecords: 0 });
      const { showGoToErrors } = useGoToErrors(outboxTab, selectedRow);
      expect(showGoToErrors.value).toBeFalsy();
    });

    it('should be false if selected row is type PluginExportProduct and has no errors', () => {
      const selectedRow = ref({ id: 50, failedRecords: 0, type: OutboxRowType.PluginExportProduct });
      const { showGoToErrors } = useGoToErrors(outboxTab, selectedRow);
      expect(showGoToErrors.value).toBeFalsy();
    });

    it('should be true if selected row is type PluginExportProduct and has errors', () => {
      const selectedRow = ref({ id: 50, failedRecords: 5, type: OutboxRowType.PluginExportProduct });
      const { showGoToErrors } = useGoToErrors(outboxTab, selectedRow);
      expect(showGoToErrors.value).toBeTruthy();
    });

    it('should be false if selected row is type PluginExportProduct and has errors but not in Outbox Tab', () => {
      const selectedRow = ref({ id: 50, failedRecords: 5, type: OutboxRowType.PluginExportProduct });
      const { showGoToErrors } = useGoToErrors(productsTab, selectedRow);
      expect(showGoToErrors.value).toBeFalsy();
    });

    it('should be true if selected row is type FileMedia and has errors', () => {
      const selectedRow = ref({ id: 50, failedRecords: 5, type: OutboxRowType.FileMedia });
      const { showGoToErrors } = useGoToErrors(outboxTab, selectedRow);
      expect(showGoToErrors.value).toBeFalsy();
    });
  });
});
