import { Locator, Page } from '@playwright/test';
import { BasePage } from '@pages/basePage.page';

export class AssignCollectionsPage extends BasePage {
  private page: Page;
  readonly channelTab: Locator;
  readonly workareaTab: Locator;
  readonly channelList: Locator;
  readonly collectionOption = (name: string): Locator => this.page.getByText(name, { exact: true });
  readonly getCollectionByText = (name: string): Locator => this.page.locator('.item-text').filter({ hasText: name });
  readonly dropZone: Locator;
  readonly addedCollection = (name: string): Locator => this.table.locator('.q-chip').filter({ hasText: name });
  readonly saveButton: Locator;
  readonly cancelButton: Locator;
  readonly goBackButton: Locator;
  readonly channelsSpinner: Locator;
  readonly formatsSpinner: Locator;
  readonly table: Locator;

  constructor(page: Page) {
    super(page);
    this.page = page;
    this.channelTab = this.page.locator('.q-tab').filter({ hasText: 'channels' });
    this.workareaTab = this.page.locator('.q-tab').filter({ hasText: 'workareas' });
    this.channelList = this.page.getByTestId('channels');
    this.dropZone = this.page.locator('.dropzone');
    this.saveButton = this.page.getByLabel('save');
    this.cancelButton = this.page.getByLabel('cancel');
    this.goBackButton = this.page.getByLabel('go back');
    this.channelsSpinner = this.page.getByTestId('channels-spinner');
    this.formatsSpinner = this.page.getByTestId('formats-spinner');
    this.table = this.page.getByTestId('assign-collection-table');
  }

  async selectChannel(channel: string): Promise<void> {
    await this.channelList.click();
    await this.collectionOption(channel).click();
  }

  async dragAndDropCollection(node: string): Promise<void> {
    await this.getCollectionByText(node).dragTo(this.dropZone, { force: true });
  }

  async waitForPageToLoad(): Promise<void> {
    await this.channelsSpinner.waitFor({ state: 'hidden' });
    await this.formatsSpinner.waitFor({ state: 'hidden' });
  }

  async removeCollectionLink(nodeName: string): Promise<void> {
    const linkLocator = this.page.locator('.q-chip').filter({ hasText: nodeName });
    const removeButton = linkLocator.locator('.q-chip__icon--remove');

    await removeButton.first().click();
  }
}
