import { beforeEach, describe, expect, it, vi } from 'vitest';

import { AccessService, FeatureService, NavigationService } from '@services';
import { mount } from '@vue/test-utils';
import GoToAdminButton from '@components/StartPage/GoToAdminButton.vue';

import CTileBtn from '@inriver/inri/src/components/CTileBtn.vue';
import CSpinner from '@inriver/inri/src/components/CSpinner.vue';
import CSlideEffect from '@inriver/inri/src/components/CSlideEffect.vue';
import CTooltip from '@inriver/inri/src/components/CTooltip.vue';
import { setActivePinia, createPinia } from 'pinia';
import { getCurrentOrganizationId } from '@services/DataFlow/CurrentOrganizationService';

const components = {
  'c-tile-btn': CTileBtn,
  'c-spinner': CSpinner,
  'c-slide-effect': CSlideEffect,
  'c-tooltip': CTooltip,
};
vi.mock('@services/AccessService');
vi.mock('@services/FeatureService');
vi.mock('@services/NavigationService');
vi.mock('@services/DataFlow/CurrentOrganizationService');
describe('GoToAdminButton', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  describe('Navigation', () => {
    let component = undefined;

    beforeEach(() => {
      AccessService.hasSyndicatePlusAdminAccess.mockReturnValue(true);
      FeatureService.isSyndicatePlusAdminEnabled.mockReturnValue(true);
      component = mount(GoToAdminButton, {
        components,
      });
    });

    it('adds org-id query param if org id exists', async () => {
      (getCurrentOrganizationId as any).mockResolvedValue(5);
      await component.trigger('click');

      expect(NavigationService.open).toHaveBeenCalledWith('/app/syndicateplus?org-id=5');
    });

    it('adds no query param if org id is undefined', async () => {
      (getCurrentOrganizationId as any).mockResolvedValue(undefined);
      await component.trigger('click');

      expect(NavigationService.open).toHaveBeenCalledWith('/app/syndicateplus');
    });
  });

  describe('Visibility', () => {
    it('should show if feature is enabled and permission exists', () => {
      AccessService.hasSyndicatePlusAdminAccess.mockReturnValue(true);
      FeatureService.isSyndicatePlusAdminEnabled.mockReturnValue(true);

      const component = mount(GoToAdminButton, {
        components,
      });

      expect(component.find('button').exists()).toBeTruthy();
    });

    it('should not show if feature is not enabled', () => {
      AccessService.hasSyndicatePlusAdminAccess.mockReturnValue(true);
      FeatureService.isSyndicatePlusAdminEnabled.mockReturnValue(false);

      const component = mount(GoToAdminButton, {
        components,
      });

      expect(component.find('button').exists()).toBeFalsy();
    });

    it('should not show if permission is not given', () => {
      AccessService.hasSyndicatePlusAdminAccess.mockReturnValue(false);
      FeatureService.isSyndicatePlusAdminEnabled.mockReturnValue(true);

      const component = mount(GoToAdminButton, {
        components,
      });

      expect(component.find('button').exists()).toBeFalsy();
    });
  });
});
