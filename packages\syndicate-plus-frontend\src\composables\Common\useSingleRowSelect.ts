import { ref, Ref } from 'vue';

export default function useSingleRowSelect<T>() {
  // Refs
  const selectedRows = ref<T[]>([]) as Ref<T[]>;

  // Functions
  const onRowClick = (_, row: T): void => {
    const newRowValue = selectedRows.value?.includes(row) ? null : row;
    selectedRows.value = newRowValue ? [newRowValue] : [];
  };

  const onRowDoubleClick = (_, row: T, callback?: Function): void => {
    selectedRows.value = [row];
    callback && callback();
  };

  return { selectedRows, onRowClick, onRowDoubleClick };
}
