import { describe, it, vi, beforeEach, expect, Mock } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { useRouter } from '@composables/useRouter';
import { useRoute } from 'vue-router';
import CFieldMappingFunction from '@components/FieldMapping/CFieldMappingFunction.vue';
import suggestionService from '@services/SuggestionService';

vi.mock('@composables/useRouter');
vi.mock('vue-router');
vi.mock('@services/SuggestionService');

describe('FieldMappingFunction', () => {
  let mockRouter;
  let mockRoute;
  const useRouterNew = useRouter as Mock;
  const useRouteMock = useRoute as Mock;

  // @ts-expect-error type
  suggestionService.get.mockResolvedValue([]);

  beforeEach(() => {
    mockRouter = {
      goToPage: vi.fn(),
    };
    useRouterNew.mockReturnValue(mockRouter);
    mockRoute = {
      params: [{ destinationId: '1' }],
    };
    useRouteMock.mockReturnValue(mockRoute);

    setActivePinia(createPinia());
  });

  // need to handle q-card-section, cannot make v-if work, don't know why, leaving it for refacotr in the future
  it.skip('should check if correct number of cards and pluses is rendered(or stubbed)', async () => {
    const wrapper = mount(CFieldMappingFunction, {
      props: {
        func: 'FUNC:CONCAT::MAP:GenderMap$D{TestField125}SOMECUSTOM_Field123$D{GENDER}$D{AnotherField}$D{TestField125}SOMECUSTOM_Field123',
      },
    });

    expect(wrapper.findAll('.plus-sign').length).toBe(6);
    expect(wrapper.findAll('q-card').length).toBe(7);
  });
});
