variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'euw'
  stackType: 'qa1a'

  # Deploy dependencies
  dependsOn: 'euwDev1a'
  dependsOnSecond: 'Build_euwQa1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-qa1a-euw.productmarketingcloud.com'

  auth0Domain: 'inriverdev.eu.auth0.com'
  auth0ClientId: 'nbL2G6DGbSzuA3PHlw8gA9WjCBtCwOUz'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: ''

  allowMissingOrgMapping: true
  
  # Application insights instrumentation key
  instrumentationKey: '3c0c7f8b-1911-480f-8d0a-9d4883725eca'