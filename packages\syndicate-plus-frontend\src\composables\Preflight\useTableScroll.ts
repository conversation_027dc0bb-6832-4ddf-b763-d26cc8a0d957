import { Ref } from 'vue';

export default function useTableScroll(tableRef: Ref<any>) {
  // Functions
  const scrollToCell = (nextColumnId: number, nextRowId: number) => {
    const cell = document.getElementById(`${nextColumnId}-${nextRowId}`);
    cell && cell.scrollIntoView({ block: 'nearest' });
  };

  const scrollToRow = (nextRowId: number) => {
    tableRef.value && (tableRef.value as { scrollTo: Function }).scrollTo(nextRowId);
  };

  return { scrollToRow, scrollToCell };
}
