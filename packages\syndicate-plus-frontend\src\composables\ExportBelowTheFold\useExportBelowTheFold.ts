import { Ref, createApp, h } from 'vue';
import J<PERSON>Z<PERSON> from 'jszip';
import { saveAs } from 'file-saver';
import { initInriFramework } from '@inriver/inri';
import { Product } from '@customTypes';
import { CExportProductsTemplate, CExportPreview } from '@components/BelowTheFold';
import { APlusTemplate } from '@customTypes/Aplus';
import productCardStyles from './productCardStyles.css?inline';
import previewStyles from './previewStyles.css?inline';
import { createPinia, setActivePinia, storeToRefs } from 'pinia';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ProductGrouping } from '@enums';
import { useProgress } from '@composables/ExportBelowTheFold';

export default function useExportBelowTheFold(selectedProducts: Ref<Product[]>) {
  // Variables
  const HTMLName = 'index';
  const CSSName = 'index';
  const imagesFolderName = 'Images';
  const previewsFolderName = 'Previews';

  // Composables
  const progress = useProgress();

  // Functions
  const exportBelowTheFold = async (
    exportName: string,
    selectedTemplate: APlusTemplate,
    subCatalogId: number,
    destinationId: number
  ) => {
    progress.startProgress();
    try {
      const zip = new JSZip();
      await generateProductCardsHtml(zip, exportName);
      await generatePreviewHtml(zip, selectedTemplate, subCatalogId, destinationId);
      const content = await generateZip(zip);
      saveFile(content, exportName);
    } finally {
      progress.resetProgress();
    }
  };

  const generateProductCardsHtml = async (zip: JSZip, exportName: string): Promise<void> => {
    progress.checkCancellation();
    const app = createApp({
      render: () =>
        h(CExportProductsTemplate, {
          selectedProducts: selectedProducts.value,
          exportName,
          groupBy: ProductGrouping.CUSTOM,
        }),
    });
    initInriFramework(app);
    const content = document.createElement('div');
    app.mount(content);

    await downloadAndZipImages(zip, content);
    addPreviewLinks(content);

    zip.file(`${HTMLName}.html`, buildHtmlPage(content.innerHTML, exportName));
    zip.file(`${CSSName}.css`, productCardStyles);

    app.unmount();
    // TODO: Unmount styles
  };

  const generatePreviewHtml = async (
    zip: JSZip,
    selectedTemplate: APlusTemplate,
    subCatalogId: number,
    destinationId: number
  ): Promise<void> => {
    const previewsFolder = zip.folder(previewsFolderName);
    const pinia = createPinia();
    setActivePinia(pinia);
    const templateBuilderStore = useTemplateBuilderStore();
    await templateBuilderStore.fetchTemplate(selectedTemplate.aplusTemplateId);
    const { contentModules } = storeToRefs(templateBuilderStore);

    let currentProgress = 1;
    for (const product of selectedProducts.value) {
      progress.checkCancellation();
      progress.updateProgress(currentProgress, selectedProducts.value?.length ?? 0);
      await templateBuilderStore.fetchAplusTemplatePreview(subCatalogId, destinationId, product.customGroup);
      const app = createApp({
        render: () => h(CExportPreview, { contentModules: contentModules.value }),
      });

      initInriFramework(app);
      const content = document.createElement('div');
      app.use(pinia);
      app.mount(content);

      const htmlPage = buildHtmlPage(content.innerHTML, product.customGroup);
      previewsFolder?.file(`${product.customGroup}.html`, htmlPage);

      app.unmount();
      // TODO: Unmount styles

      currentProgress++;
    }

    previewsFolder?.file(`${CSSName}.css`, previewStyles);
  };

  const buildHtmlPage = (content: string, title?: string): string => {
    const result = `
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8">
        <link rel="stylesheet" href="index.css">
        <title>${title}</title>
      </head>
      <body>
        ${content}
      </body>
    </html>
  `;

    return result;
  };

  const downloadAndZipImages = async (zip: JSZip, content: HTMLDivElement): Promise<void> => {
    const images = Array.from(content.querySelectorAll('img'));
    const imagesFolder = zip.folder(imagesFolderName);

    await Promise.all(
      images.map(async (image, index) => {
        progress.checkCancellation();
        const imageUrl = image.getAttribute('src');
        if (imageUrl) {
          try {
            const response = await fetch(imageUrl);
            const imageBlob = await response.blob();
            const extension = imageBlob.type.split('/')[1];
            const imageName = `image${index + 1}.${extension}`;

            imagesFolder?.file(imageName, imageBlob);

            image.setAttribute('src', `${imagesFolderName}/${imageName}`);
          } catch (error) {
            console.error(`Failed to download image from ${imageUrl}`, error);
          }
        }
      })
    );
  };

  const addPreviewLinks = (content: HTMLDivElement): void => {
    const productCards = content.querySelectorAll('.c-product-card');
    productCards.forEach((card) => {
      progress.checkCancellation();
      const customGroup = card.querySelector('.description');
      const link = document.createElement('a');
      link.href = `./${previewsFolderName}/${customGroup?.innerHTML}.html`;
      card.parentNode?.insertBefore(link, card);
      link.appendChild(card);
    });
  };

  const generateZip = async (zip: JSZip) => {
    progress.checkCancellation();
    return await zip.generateAsync({ type: 'blob' });
  };

  const saveFile = async (content: Blob, exportName: string) => {
    progress.checkCancellation();
    saveAs(content, `${exportName}.zip`);
  };

  return { exportBelowTheFold };
}
