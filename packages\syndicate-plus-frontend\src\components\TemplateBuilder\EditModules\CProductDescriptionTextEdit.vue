<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <div class="flex flex-row flex-nowrap justify-center m-10px">
      <c-text-editor
        v-model="module.data.body1"
        class="product-description-container"
        :max-length="Validation.productDescriptionText.body1.maxLength"
        @update:model-value="updateModel"
        @on-field-drop="onDrop(module.data.body1, 'body1')"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, ProductDescriptionTextData } from '@customTypes/Aplus';
import { CTextEditor } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Refs
const module = ref<ContentModule<ProductDescriptionTextData>>({} as ContentModule<ProductDescriptionTextData>);

// Functions
const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, key: string) => {
  module.value.data[key] = fieldsStore.onDrop(value);
  updateModel();
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      body1: '',
    } as ProductDescriptionTextData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
<style lang="scss" scoped>
.product-description-container {
  width: 70%;
}
</style>
