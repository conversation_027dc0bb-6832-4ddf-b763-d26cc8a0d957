variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'euw'
  stackType: 'dev1a'

  # Deploy dependencies
  dependsOn: 'Build_euwDev1a'
  dependsOnSecond: 'Build_euwDev1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-dev1a-euw.productmarketingcloud.com'

  auth0Domain: 'auth-dev.syndic8.io'
  auth0ClientId: 'twD9zFV3gROvoMmRyVDq2kU6fcjZsATW'
  auth0Connection: 'inriver-prod-openid'

  fileServerUrl: 'https://app.infrared.inriver.syndic8.io'
  requestTradingPartnerUrl: 'https://community.inriver.com/hc/en-us/requests/new?ticket_form_id=8783121346204'

  apiUrl: 'https://api.infrared.inriver.syndic8.io'

  testLocalUrl: 'https://test.productmarketingcloud.com/'
  testUserName: <EMAIL>
  testUserPassword: Password123$
  testPortalUrl: https://portal-dev4a-euw.productmarketingcloud.com/

  allowMissingOrgMapping: true

  # Application insights instrumentation key
  instrumentationKey: '4410140e-2f31-4169-ace6-51335d20f7a4'

  # Output adapter API
  environmentGuid: 'dev-projectboston-devqa'
