<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <div class="row q-gutter-lg">
      <div class="col section">
        <h3>{{ title }}</h3>
        <div class="logs-message">{{ logsMessage }}</div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useDialog } from '@inriver/inri';
import { OutboundService } from '@services/ThirdPartyMessages';

// Props
const props = defineProps({
  messageId: {
    type: String,
    default: '',
  },
});

// Refs
const logsMessage = ref<string>('');

// Composables
const { t } = useI18n();
const { showDialog, isLoading, cancel } = useDialog();

// Computed
const title = computed(() => `${t('syndicate_plus.third_party_messages.message')} ${props.messageId}`);

// Lifecycle methods
onBeforeMount(async () => {
  logsMessage.value = await OutboundService.fetchLogsById(parseInt(props.messageId));
});
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }
}

.logs-message {
  max-height: 300px;
  overflow: auto;
}
</style>
