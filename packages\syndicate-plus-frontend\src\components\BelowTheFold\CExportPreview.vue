<template>
  <div>
    <c-preview-module
      v-for="(module, index) in contentModules.filter((module: ContentModule<BaseContentModuleData>) => !!module)"
      :key="index"
      class="aplus-preview-module"
      :module-type="module.moduleType"
      :index="index"
    />
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { CPreviewModule } from '@components/TemplateBuilder/PreviewModules';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, BaseContentModuleData } from '@customTypes/Aplus';

// Refs
const { contentModules } = storeToRefs(useTemplateBuilderStore());
</script>
