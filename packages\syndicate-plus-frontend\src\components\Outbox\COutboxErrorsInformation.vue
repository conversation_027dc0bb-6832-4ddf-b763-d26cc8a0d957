<template>
  <q-table
    v-model:pagination="pagination"
    row-key="startDate"
    separator="cell"
    dense
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    flat
    :columns="infoColumns"
    class="hide-checkboxes mb-20px"
    :rows="information && Object.keys(information) ? [information] : []"
    :loading="isLoadingInformation"
    hide-bottom
  >
    <template #loading>
      <q-inner-loading showing color="primary" class="inner-loading">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </template>
  </q-table>
  <q-table
    ref="tableRef"
    :pagination="{
      page: 1,
      rowsPerPage: 0,
    }"
    dense
    hide-bottom
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    flat
    :columns="errorTableColumns"
    class="outbox-errors-table hide-checkboxes sticky-table-header"
    :rows-per-page-options="[0]"
    :rows="outboxErrors"
    row-key="name"
    separator="cell"
    :loading="loading"
    virtual-scroll
    :virtual-scroll-item-size="31"
    :virtual-scroll-sticky-size-start="31"
    @virtual-scroll="onScroll"
  >
    <template #body-cell-status="props">
      <q-td :props="props">
        <c-small-square
          :color="props.row['status'] === OutboxErrorStatus.FAILED ? SquareColor.RED : SquareColor.ORANGE"
        />
        <span class="ml-2">{{ props.row['status'] }}</span>
      </q-td>
    </template>
    <template #loading>
      <q-inner-loading showing color="primary" class="inner-loading">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </template>
  </q-table>
</template>

<script setup lang="ts">
import { nextTick, watch, onBeforeMount } from 'vue';
import { storeToRefs } from 'pinia';
import { infoColumns, errorTableColumns } from '@const';
import { useOutboxErrorsStore } from '@stores';
import { CSmallSquare } from '@components';
import { SquareColor, OutboxErrorStatus } from '@enums';
import { useRoute } from 'vue-router';

// Props
const props = defineProps({
  catalogExportId: {
    type: String,
    default: '',
  },
});

// Composables
const { fetchOutboxErrors, resetOutboxErrorsState, fetchOutboxErrorsInformation } = useOutboxErrorsStore();
const route = useRoute();

// Variables
const destinationId = parseInt(route.params.destinationId as string);
const pagination = {
  page: 1,
  rowsPerPage: 0,
};

// Refs
const { outboxErrors, loading, lastPage, isLoadingInformation, information } = storeToRefs(useOutboxErrorsStore());

// Functions
const onScroll = async (details) => {
  const { to, ref } = details;
  const lastIndex = outboxErrors.value.length - 1;
  if (!loading.value && !lastPage.value && to === lastIndex) {
    await fetchOutboxErrors(destinationId, props.catalogExportId);
    nextTick(() => {
      ref.refresh();
    });
  }
};

// Lifecycle methods
onBeforeMount(async () => {
  await resetOutboxErrorsState();
  await fetchOutboxErrors(destinationId, props.catalogExportId);
  await fetchOutboxErrorsInformation(destinationId, props.catalogExportId);
});

watch(
  () => props.catalogExportId,
  async (newValue) => {
    if (!newValue) {
      return;
    }

    await resetOutboxErrorsState();
    await fetchOutboxErrors(destinationId, props.catalogExportId);
    await fetchOutboxErrorsInformation(destinationId, props.catalogExportId);
  }
);
</script>

<style scoped lang="scss">
$outbox-errors-offset: 227px;

.outbox-errors-table {
  max-height: calc(100vh - $outbox-errors-offset);
}
</style>
