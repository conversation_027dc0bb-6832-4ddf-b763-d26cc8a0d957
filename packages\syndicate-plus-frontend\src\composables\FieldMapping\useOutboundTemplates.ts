import { ref, computed, onBeforeMount, Ref, ComputedRef } from 'vue';
import { Field, TemplateMappingEntry, InboundTemplateField, FullMappingPathTemplateField } from '@customTypes';
import { useSuggestionStore, useTemplatesStore } from '@stores';
import { SuggestionTypeEnum } from '@enums';

export default function useOutboundTemplates(
  columns: ComputedRef<any>,
  visibleColumns: Ref<string[]>,
  selectedRows: Ref<FullMappingPathTemplateField[]>,
  destinationId: number,
  inboundTemplateFields: Ref<InboundTemplateField[] | undefined>
) {
  const { fetchSuggestions } = useSuggestionStore();
  const templatesStore = useTemplatesStore();

  // Refs
  const isLoading = ref(false);
  const outboundTemplates = ref<Field[]>([]);
  const selectedOutboundTemplate = ref<Field>();
  const outboundTemplateSheets = ref<Field[]>([]);
  const selectedOutboundSheet = ref<Field>();
  const outboundTemplateMappingEntries = ref<[] | TemplateMappingEntry[]>();

  // Computed
  const outboundTemplateFields = computed<FullMappingPathTemplateField[]>(
    () =>
      outboundTemplateMappingEntries.value?.map((m: TemplateMappingEntry) =>
        m.toFullMappingPathTemplateField(inboundTemplateFields.value ?? [])
      ) ?? []
  );

  // Functions
  const loadOutboundData = async (): Promise<void> => {
    isLoading.value = true;
    try {
      if (selectedOutboundTemplate.value !== undefined) {
        await templatesStore.fetchTemplates(selectedOutboundTemplate.value.id, destinationId);
        outboundTemplateSheets.value = await templatesStore.getSheetSuggestionForTemplate(
          selectedOutboundTemplate.value.id,
          destinationId
        );

        if (outboundTemplateSheets.value.length) {
          selectedOutboundSheet.value = outboundTemplateSheets.value[0];
          outboundTemplateMappingEntries.value = templatesStore.getTemplateSheetFields(
            selectedOutboundTemplate.value.id,
            selectedOutboundSheet.value.name
          );
        } else {
          outboundTemplateMappingEntries.value = templatesStore.getTemplateById(
            selectedOutboundTemplate.value.id
          )?.fields;
        }
        hideColumnsIfNeeded();
      }
    } finally {
      isLoading.value = false;
    }
  };

  const hideColumnsIfNeeded = () => {
    resetVisibleColumns();

    const shouldShowSuggestedColumn = outboundTemplateMappingEntries.value?.some((x) => x.mapping?.suggestedMatch);
    if (!shouldShowSuggestedColumn) {
      visibleColumns.value = visibleColumns.value.filter((x) => x !== 'suggestedMatch');
    }
  };

  const resetVisibleColumns = () => {
    visibleColumns.value = columns.value.map((x) => x.name);
    selectedRows.value = [];
  };

  const filterData = () => {
    selectedRows.value = [];
    if (selectedOutboundTemplate.value && selectedOutboundSheet.value) {
      outboundTemplateMappingEntries.value = templatesStore.getTemplateSheetFields(
        selectedOutboundTemplate.value.id,
        selectedOutboundSheet.value.name
      );
    }
  };

  // Lifecycle methods
  onBeforeMount(async () => {
    outboundTemplates.value = await fetchSuggestions(destinationId, SuggestionTypeEnum.ExportMultiTemplates);
    if (outboundTemplates.value.length) {
      selectedOutboundTemplate.value = outboundTemplates.value[0];
      await loadOutboundData();
    }
  });

  return {
    isLoading,
    outboundTemplates,
    selectedOutboundTemplate,
    outboundTemplateSheets,
    selectedOutboundSheet,
    outboundTemplateFields,
    loadOutboundData,
    filterData,
  };
}
