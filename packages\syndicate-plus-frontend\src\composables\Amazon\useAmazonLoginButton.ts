import { Ref, computed } from 'vue';
import { useCurrentTradingPartner } from '@composables';
import { useAmazonCallbackStatus } from '@composables/Amazon';

export default function useAmazonLoginButton(tradingPartnerName: Ref<string>, useI18n: Function) {
  // Computed
  const showGoToAmazonLogin = computed(() => isAmazon.value);

  // Composables
  const { isAmazon } = useCurrentTradingPartner(tradingPartnerName);
  useAmazonCallbackStatus(useI18n);

  return { showGoToAmazonLogin };
}
