import { vi, describe, it, beforeEach, expect, Mock } from 'vitest';
import { ref } from 'vue';
import { useProductDetailsButton } from '@composables/Buttons';
import { SyndicationPageTabNames, ProductGrouping } from '@enums';
import { ProductFilterDto } from '@dtos';
import { useRouter } from '@composables/useRouter';
import { goToScopedPageWithSelect } from '@services/Details/ScopedProductSelectionService';
import { notify } from '@inriver/inri';

vi.mock('@composables/useRouter');
vi.mock('@inriver/inri');
vi.mock('@services/Details/ScopedProductSelectionService');

const useI18n = vi.fn().mockReturnValue({
  t: vi.fn(),
});

describe('useProductDetailsButton', async () => {
  const mockRouter = {
    goToPage: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useRouter as Mock).mockReturnValue(mockRouter);
  });

  describe('isDetailsButtonVisible', async () => {
    it('shows details button correctly', async () => {
      const testCases = [
        { activeTab: SyndicationPageTabNames.Products, numberOfProducts: 4, isVisible: true },
        { activeTab: SyndicationPageTabNames.Products, numberOfProducts: 0, isVisible: false },
        { activeTab: SyndicationPageTabNames.Collection, numberOfProducts: 3, isVisible: false },
        { activeTab: SyndicationPageTabNames.Products, numberOfProducts: 10, isVisible: true },
        { activeTab: null, numberOfProducts: 0, isVisible: false },
        { activeTab: null, numberOfProducts: 1, isVisible: true },
      ];

      testCases.forEach(({ activeTab, numberOfProducts, isVisible }) => {
        // Arrange
        const tab = ref<SyndicationPageTabNames | null>(SyndicationPageTabNames.Collection);
        const filter = ref(new ProductFilterDto());
        const numberOfSelectedProducts = ref(0);
        const { isDetailsButtonVisible } = useProductDetailsButton(useI18n, tab, filter, numberOfSelectedProducts);

        // Act
        tab.value = activeTab;
        numberOfSelectedProducts.value = numberOfProducts;

        // Assert
        expect(isDetailsButtonVisible.value).toBe(isVisible);
      });
    });
  });

  describe('isGoToDetailsAllowed', async () => {
    it('enables details button correctly', async () => {
      const testCases = [
        { numberOfProducts: -1, isEnabled: false },
        { numberOfProducts: 0, isEnabled: false },
        { numberOfProducts: 1, isEnabled: true },
        { numberOfProducts: 5, isEnabled: true },
        { numberOfProducts: 6, isEnabled: false },
      ];

      testCases.forEach(({ numberOfProducts, isEnabled }) => {
        // Arrange
        const tab = ref(SyndicationPageTabNames.Collection);
        const filter = ref(new ProductFilterDto());
        const numberOfSelectedProducts = ref(0);
        const { isGoToDetailsAllowed } = useProductDetailsButton(useI18n, tab, filter, numberOfSelectedProducts);

        // Act
        numberOfSelectedProducts.value = numberOfProducts;

        // Assert
        expect(isGoToDetailsAllowed.value).toBe(isEnabled);
      });
    });
  });

  describe('goToDetailsPageForCollection', () => {
    it('calls goToPage correctly', async () => {
      const testCases = [
        {
          selectedProductIds: ['1', '2'],
          selectedProductNames: ['a', 'b'],
          groupBy: ProductGrouping.SKU,
          expectedQuery: {
            skus: '1,2',
            productNames: 'a,b',
          },
        },
        {
          selectedProductIds: ['1', '2', '3'],
          selectedProductNames: ['a', 'b', 'c'],
          groupBy: ProductGrouping.UPC,
          expectedQuery: {
            productIds: '1,2,3',
            productNames: 'a,b,c',
          },
        },
        {
          selectedProductIds: ['1', '2', '3', '4'],
          selectedProductNames: ['a', 'b', 'c', 'd'],
          groupBy: ProductGrouping.CUSTOM,
          expectedQuery: {
            products: '1,2,3,4',
            productNames: 'a,b,c,d',
          },
        },
      ];

      testCases.forEach(({ selectedProductIds, selectedProductNames, groupBy }) => {
        // Arrange
        const tab = ref(SyndicationPageTabNames.Products);
        const filter = ref(new ProductFilterDto());
        filter.value.groupBy.id = groupBy;
        const numberOfSelectedProducts = ref(selectedProductNames.length);
        const { goToDetailsPageForCollection } = useProductDetailsButton(
          useI18n,
          tab,
          filter,
          numberOfSelectedProducts
        );

        // Act
        goToDetailsPageForCollection(selectedProductIds, selectedProductNames);

        // Assert
        expect(goToScopedPageWithSelect).toHaveBeenCalledWith(
          mockRouter.goToPage,
          'collection-details-page',
          {},
          groupBy,
          selectedProductIds,
          selectedProductNames
        );
      });
    });

    it('shows an error notification if the selected number of products exceeds the limit', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Products);
      const filter = ref(new ProductFilterDto());
      const numberOfSelectedProducts = ref(10);
      const { goToDetailsPageForCollection } = useProductDetailsButton(useI18n, tab, filter, numberOfSelectedProducts);

      // Act
      goToDetailsPageForCollection([], []);

      // Assert
      expect(notify.error).toHaveBeenCalledOnce();
      expect(goToScopedPageWithSelect).not.toHaveBeenCalled();
    });
  });

  describe('goToDetailsPage', async () => {
    it('calls goToScopedPageWithSelect correctly', async () => {
      const testCases = [
        {
          selectedProductIds: ['1', '2'],
          selectedProductNames: ['a', 'b'],
          groupBy: ProductGrouping.SKU,
          expectedQuery: {
            skus: '1,2',
            productNames: 'a,b',
          },
        },
        {
          selectedProductIds: ['1', '2', '3'],
          selectedProductNames: ['a', 'b', 'c'],
          groupBy: ProductGrouping.UPC,
          expectedQuery: {
            productIds: '1,2,3',
            productNames: 'a,b,c',
          },
        },
        {
          selectedProductIds: ['1', '2', '3', '4'],
          selectedProductNames: ['a', 'b', 'c', 'd'],
          groupBy: ProductGrouping.CUSTOM,
          expectedQuery: {
            products: '1,2,3,4',
            productNames: 'a,b,c,d',
          },
        },
      ];

      testCases.forEach(({ selectedProductIds, selectedProductNames, groupBy }) => {
        // Arrange
        const tab = ref(SyndicationPageTabNames.Products);
        const filter = ref(new ProductFilterDto());
        filter.value.groupBy.id = groupBy;
        const numberOfSelectedProducts = ref(selectedProductNames.length);
        const { goToDetailsPage } = useProductDetailsButton(useI18n, tab, filter, numberOfSelectedProducts);

        // Act
        goToDetailsPage(selectedProductIds, selectedProductNames);

        // Assert
        expect(goToScopedPageWithSelect).toHaveBeenCalledWith(
          mockRouter.goToPage,
          'details-page',
          {},
          groupBy,
          selectedProductIds,
          selectedProductNames
        );
      });
    });

    it('shows an error notification if the selected number of products exceeds the limit', () => {
      // Arrange
      const tab = ref(SyndicationPageTabNames.Products);
      const filter = ref(new ProductFilterDto());
      const numberOfSelectedProducts = ref(10);
      const { goToDetailsPage } = useProductDetailsButton(useI18n, tab, filter, numberOfSelectedProducts);

      // Act
      goToDetailsPage([], []);

      // Assert
      expect(notify.error).toHaveBeenCalledOnce();
      expect(mockRouter.goToPage).not.toHaveBeenCalled();
    });
  });
});
