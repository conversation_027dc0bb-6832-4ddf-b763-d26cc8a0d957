import { vi, describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useAddToCollectionActions } from '@composables/Collection';
import { CollectionService } from '@services';
import { ProductFilterDto } from '@dtos';

vi.mock('@services/CollectionService');

describe('useAddToCollectionActions', async () => {
  describe('createCollection', async () => {
    it('calls the service method with the correct arguments if all products are selected', async () => {
      // Arrange
      const isLoading = ref(true);
      const selectAllProducts = true;
      const filter = {} as ProductFilterDto;
      const searchValue = 'search';
      const groupBy = 'SKU';
      const keys = ['a', 'b'];
      const collectionId = 1;
      const { addToCollection } = useAddToCollectionActions(isLoading);

      // Act
      await addToCollection(collectionId, selectAllProducts, filter, searchValue, groupBy, keys);

      // Assert
      expect(CollectionService.assignAllProducts).toBeCalledWith(collectionId, filter, searchValue, groupBy);
      expect(isLoading.value).toBeFalsy();
    });

    it('calls the service method with the correct arguments if individual products are selected', async () => {
      // Arrange
      const isLoading = ref(true);
      const selectAllProducts = false;
      const filter = {} as ProductFilterDto;
      const searchValue = 'search';
      const groupBy = 'SKU';
      const keys = ['a', 'b'];
      const collectionId = 1;
      const { addToCollection } = useAddToCollectionActions(isLoading);

      // Act
      await addToCollection(collectionId, selectAllProducts, filter, searchValue, groupBy, keys);

      // Assert
      expect(CollectionService.assignIndividualProducts).toBeCalledWith(collectionId, groupBy, keys);
      expect(isLoading.value).toBeFalsy();
    });
  });
});
