<template>
  <div class="no-data-table column flex-center">
    <c-svg-image :image="src" class="no-data-image" />
    <span class="title">
      {{ title }}
      <slot />
    </span>
    <span class="text">
      {{ text }}
    </span>
    <ul>
      <li v-for="bullet in bulletPoints" :key="bullet">
        {{ bullet }}
      </li>
    </ul>
  </div>
</template>
<script setup lang="ts">
import CSvgImage from './CSvgImage.vue';

defineProps({
  src: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  text: {
    type: String,
    default: '',
  },
  imageHeight: {
    type: String,
    default: '245px',
  },
  bulletPoints: {
    type: Array<string>,
    default: [],
  },
});
</script>
<style lang="scss" scoped>
.no-data-table {
  margin: 0 auto;
  text-align: center;
  width: 385px;
  min-height: 245px;

  .no-data-image {
    height: v-bind(imageHeight);
  }

  .title {
    font-weight: 600;
    font-size: 24px;
  }

  .text {
    font-size: 14px;
  }

  ul {
    list-style: disc;
    text-align: left;
    margin-top: 10px;
  }
}
</style>
