<template>
  <div class="mx-auto min-w-1000px w-1000px images-sidebar-preview">
    <div class="flex justify-center">
      <div class="flex flex-row flex-nowrap">
        <div class="left-column m-10px" data-testid="left-image-sidebar">
          <c-caption-angle-image-block-preview
            :image-data="module.data.imageCaption"
            image-data-class="h-400px w-300px min-h-[400px] min-w-[300px]"
          />
        </div>
        <div class="middle-column m-10px pt-10px" data-testid="middle-image-sidebar">
          <h3 class="font-bold">{{ module.data.headline }}</h3>
          <p class="italic">{{ module.data.subheadline }}</p>
          <c-html-preview :html="module.data.body" />
          <c-bullet-list-preview v-if="module.data.bullets?.length" :bullet-list="module.data.bullets" />
        </div>
        <div class="right-column m-10px pl-10px" data-testid="right-image-sidebar">
          <c-caption-angle-image-block-preview
            :image-data="module.data.sidebarImageCaption"
            caption-class="sidebar-body pl-10px pt-6px"
            image-data-class="h-100px w-200px"
          />
          <c-html-preview :html="module.data.sidebarBody?.content[0]?.content[0]?.text" />
          <c-bullet-list-preview
            v-if="module.data.sidebarBullets?.length"
            class="sidebar-bullet-list ml-10px"
            :bullet-list="module.data.sidebarBullets"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, ImageSidebarData } from '@customTypes/Aplus';
import { CBulletListPreview, CCaptionAngleImageBlockPreview, CHtmlPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<ImageSidebarData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.images-sidebar-preview {
  .bullets {
    background-color: var(--color-grey-10);
    border: 1px solid var(--color-grey-light);
  }

  .left-column {
    width: 300px;

    :deep(.gray-rectangle) {
      width: 300px;
      height: 400px;
    }
  }

  .middle-column {
    width: 380px;
  }

  .right-column {
    width: 230px;
    border-left: 1px solid var(--color-grey-light);

    :deep(.gray-rectangle) {
      width: 200px;
      height: 100px;
    }
  }

  .sidebar-body {
    font-size: 13px;
  }

  .sidebar-bullet-list {
    :deep(.bullet-text) {
      font-weight: 700;
    }
  }
}
</style>
