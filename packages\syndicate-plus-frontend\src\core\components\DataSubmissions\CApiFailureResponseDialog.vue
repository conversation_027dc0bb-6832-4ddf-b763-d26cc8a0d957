<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog api-failure-dialog"
    title="failure message"
    :content-class="'api-failure-response-content'"
    hide-cancel-button
    :max-width="800"
    confirm-button-text="сlose"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    <div v-if="!parsedResponse" class="no-data-message">no api failure response available</div>
    <div v-else class="api-failure-content">
      <div class="scrollable-container">
        <q-expansion-item
          v-for="(errors, field) in parsedResponse"
          :key="field"
          :label="field"
          :default-opened="Object.keys(parsedResponse).length === 1"
          header-class="text-primary"
          expand-icon-class="text-primary"
          group="api-errors"
        >
          <q-card>
            <q-card-section>
              <div v-for="(error, index) in errors" :key="index" class="q-mb-md error-item">
                <div class="text-weight-bold">{{ error.Level }}</div>
                <div>{{ error.Message }}</div>
                <div v-if="error.MessageCode !== 'N/A'" class="text-caption text-grey-8">
                  сode: {{ error.MessageCode }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </div>
    </div>
  </c-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useDialog } from '@inriver/inri';
import { DataSubmissionEntity } from '@core/interfaces';

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  entity: {
    type: Object as () => DataSubmissionEntity | null,
    required: true,
    default: null,
  },
});

const emit = defineEmits(['update:show']);

// Composables
const { showDialog, confirmSuccess, cancel } = useDialog();

// Refs
const parsedResponse = ref<Record<string, any> | null>(null);

// Functions
const onConfirm = () => {
  emit('update:show', false);
  confirmSuccess(null);
};

const onCancel = () => {
  emit('update:show', false);
  cancel();
};

// Lifecycle methods
watch(
  () => props.entity,
  (newEntity) => {
    if (newEntity?.apiFailureResponse) {
      try {
        parsedResponse.value = JSON.parse(newEntity.apiFailureResponse);
      } catch (error) {
        parsedResponse.value = {
          'Error Message': [
            {
              Level: 'Error',
              Message: newEntity.apiFailureResponse,
              MessageCode: 'N/A',
            },
          ],
        };
      }
    } else {
      parsedResponse.value = null;
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.api-failure-dialog {
  :deep(.q-expansion-item) {
    margin-bottom: 4px;
    border-radius: 4px;
    overflow: hidden;
    background-color: var(--color-grey-lighter, #f5f5f5);
  }

  :deep(.q-item) {
    padding: 12px 16px;
  }
}

.api-failure-response-content {
  max-height: calc(100vh - 120px);
}

.no-data-message {
  padding: 16px;
  text-align: center;
  color: var(--color-grey-dark);
}

.api-failure-content {
  padding: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.scrollable-container {
  flex: 1;
  overflow-y: auto;
  max-height: calc(80vh - 140px);
  margin-bottom: 16px;
  padding-right: 8px;
}

.error-item {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;

  &:last-child {
    border-bottom: none;
  }
}
</style>
