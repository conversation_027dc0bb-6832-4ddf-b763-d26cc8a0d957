<template>
  <div class="mx-auto min-w-1000px w-1000px product-description-preview">
    <div class="flex justify-center m-10px">
      <c-html-preview :html="module.data.body1" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, ProductDescriptionTextData } from '@customTypes/Aplus';
import { CHtmlPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<ProductDescriptionTextData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped></style>
