import { computed, Ref } from 'vue';

export default function useFilterFieldsSectionName(selectedColumnNames: Ref<string[]>, useI18n: Function) {
  // Variables
  const { t } = useI18n && useI18n();

  // Computed
  const sectionLabel = computed(() => {
    const numberOfSelectedFieldsString = selectedColumnNames.value?.length
      ? ` (${selectedColumnNames.value.length})`
      : '';

    return `${t('syndicate_plus.details.filter.show_fields')}${numberOfSelectedFieldsString}`;
  });

  return { sectionLabel };
}
