<template>
  <div :class="customClass" />
  <slot />
</template>
<script setup lang="ts">
import { SquareColor } from '@enums/SquareColor';
import { computed, PropType } from 'vue';

const props = defineProps({
  color: {
    type: String as PropType<SquareColor>,
    default: '',
  },
});

const customClass = computed(() => (props.color ? `square square-${props.color}` : ''));
</script>

<style lang="scss" scoped>
.square {
  height: 10px;
  width: 10px;
  display: inline-block;
  vertical-align: middle;
}

.square-green {
  background-color: var(--color-green-light);
}

.square-red {
  background-color: var(--color-red-light);
}

.square-purple {
  background-color: var(--color-purple-light);
}

.square-orange {
  background-color: var(--color-orange-light);
}
</style>
