import { describe, expect, it } from 'vitest';

import { sortInboundTemplates } from '@composables/FieldMapping/inboundSortMethod';

describe('inboundSortMethod', () => {
  it('sorts alphabetically', () => {
    const names = ['c', 'b', 'a'].map(createTemplate);
    const result = sortInboundTemplates(names);

    expect(result).toEqual(['a', 'b', 'c'].map(createTemplate));
  });

  it('prefers master ahead of all other things', () => {
    const names = ['c', 'b', 'master'].map(createTemplate);
    const result = sortInboundTemplates(names);

    expect(result).toEqual(['master', 'b', 'c'].map(createTemplate));
  });

  it('prefers inbound ahead of other things except master', () => {
    const names = ['c', 'inbound', 'master'].map(createTemplate);
    const result = sortInboundTemplates(names);

    expect(result).toEqual(['master', 'inbound', 'c'].map(createTemplate));
  });

  it('handles undefined', () => {
    const names = ['c', 'inbound', undefined].map(createTemplate);
    const result = sortInboundTemplates(names);

    expect(result).toEqual(['inbound', 'c', undefined].map(createTemplate));
  });
});

function createTemplate(templateName: string) {
  return {
    templateName,
  };
}
