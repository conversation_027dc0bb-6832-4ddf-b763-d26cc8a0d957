[
  {
    Id: 'Activity',
    DisplayName: 'Activity',
    Name: {
      stringMap: {
        en: 'Activity',
        'fr-CA': 'Activité',
      },
    },
    IsLinkEntityType: false,
    Parents: [],
    Children: [
      {
        Id: 'Bundle',
        DisplayName: 'Bundle',
        LinkEntityTypeId: 'ActivityBundle',
      },
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'ActivityItem',
      },
      {
        Id: 'Milestone',
        DisplayName: 'Milestone',
        LinkEntityTypeId: 'ActivityMilestone',
      },
    ],
  },
  {
    Id: 'Brand',
    DisplayName: 'Brand',
    Name: {
      stringMap: {
        en: 'Brand',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'ChannelNode',
        DisplayName: 'Nodes',
        LinkEntityTypeId: 'ChannelNodeBrand',
      },
    ],
    Children: [
      {
        Id: 'Product',
        DisplayName: 'Products',
        LinkEntityTypeId: 'BrandProduct',
      },
    ],
  },
  {
    Id: 'Bundle',
    DisplayName: 'Bundle',
    Name: {
      stringMap: {
        en: 'Bundle',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Activity',
        DisplayName: 'Activities',
        LinkEntityTypeId: 'ActivityBundle',
      },
      {
        Id: 'ChannelNode',
        DisplayName: 'Nodes',
        LinkEntityTypeId: 'ChannelNodeBundle',
      },
      {
        Id: 'Section',
        DisplayName: 'Section',
        LinkEntityTypeId: 'SectionBundle',
      },
    ],
    Children: [
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'BundleItem',
      },
      {
        Id: 'Product',
        DisplayName: 'Products',
        LinkEntityTypeId: 'BundleProduct',
      },
    ],
  },
  {
    Id: 'BundleOption',
    DisplayName: 'Bundle Option Information',
    Name: {
      stringMap: {
        en: 'Bundle Option Information',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: true,
    Parents: [],
    Children: [],
  },
  {
    Id: 'Channel',
    DisplayName: 'Channel',
    Name: {
      stringMap: {
        en: 'Channel',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [],
    Children: [
      {
        Id: 'ChannelNode',
        DisplayName: 'Nodes',
        LinkEntityTypeId: 'ChannelChannelNode',
      },
      {
        Id: 'Resource',
        DisplayName: 'Resource',
        LinkEntityTypeId: 'ChannelResource',
      },
      {
        Id: 'TargetExporter',
        DisplayName: 'TargetExporter',
        LinkEntityTypeId: 'ChannelTargetExporter',
      },
    ],
  },
  {
    Id: 'ChannelNode',
    DisplayName: 'Node',
    Name: {
      stringMap: {
        en: 'Node',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Channel',
        DisplayName: 'Channel',
        LinkEntityTypeId: 'ChannelChannelNode',
      },
    ],
    Children: [
      {
        Id: 'Brand',
        DisplayName: 'Brands',
        LinkEntityTypeId: 'ChannelNodeBrand',
      },
      {
        Id: 'Bundle',
        DisplayName: 'Bundles',
        LinkEntityTypeId: 'ChannelNodeBundle',
      },
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'ChannelNodeItem',
      },
      {
        Id: 'Product',
        DisplayName: 'Products',
        LinkEntityTypeId: 'ChannelNodeProduct',
      },
      {
        Id: 'PS',
        DisplayName: 'Primary Product Sets',
        LinkEntityTypeId: 'ChannelNodeProductSetPrimary',
      },
      {
        Id: 'Package',
        DisplayName: 'Packages',
        LinkEntityTypeId: 'NodePackage',
      },
      {
        Id: 'PS',
        DisplayName: 'Product Set',
        LinkEntityTypeId: 'NodeProductSet',
      },
      {
        Id: 'Resource',
        DisplayName: 'Resource',
        LinkEntityTypeId: 'nodeResource',
      },
    ],
  },
  {
    Id: 'Item',
    DisplayName: 'Item',
    Name: {
      stringMap: {
        en: 'Item',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Activity',
        DisplayName: 'Activity',
        LinkEntityTypeId: 'ActivityItem',
      },
      {
        Id: 'Bundle',
        DisplayName: 'Bundles',
        LinkEntityTypeId: 'BundleItem',
      },
      {
        Id: 'ChannelNode',
        DisplayName: 'Nodes',
        LinkEntityTypeId: 'ChannelNodeItem',
      },
      {
        Id: 'Package',
        DisplayName: 'Packages',
        LinkEntityTypeId: 'PackageItem',
      },
      {
        Id: 'Product',
        DisplayName: 'Product',
        LinkEntityTypeId: 'ProductItem',
      },
      {
        Id: 'PS',
        DisplayName: 'Product Set',
        LinkEntityTypeId: 'ProductSetItem',
      },
      {
        Id: 'Task',
        DisplayName: 'Tasks',
        LinkEntityTypeId: 'TaskItem',
      },
    ],
    Children: [
      {
        Id: 'Resource',
        DisplayName: 'Resources',
        LinkEntityTypeId: 'ItemResource',
      },
      {
        Id: 'SubItem',
        DisplayName: 'Sub Items',
        LinkEntityTypeId: 'ItemSubItem',
      },
    ],
  },
  {
    Id: 'Milestone',
    DisplayName: 'Milestone',
    Name: {
      stringMap: {
        en: 'Milestone',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Activity',
        DisplayName: 'Activity',
        LinkEntityTypeId: 'ActivityMilestone',
      },
    ],
    Children: [
      {
        Id: 'Task',
        DisplayName: 'Task',
        LinkEntityTypeId: 'MilestonTask',
      },
    ],
  },
  {
    Id: 'Package',
    DisplayName: 'Package',
    Name: {
      stringMap: {
        en: 'Package',
        'fr-CA': 'Package',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'ChannelNode',
        DisplayName: 'Nodes',
        LinkEntityTypeId: 'NodePackage',
      },
    ],
    Children: [
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'PackageItem',
      },
      {
        Id: 'Resource',
        DisplayName: 'Resources',
        LinkEntityTypeId: 'PackageResource',
      },
    ],
  },
  {
    Id: 'Product',
    DisplayName: 'Product',
    Name: {
      stringMap: {
        en: 'Product',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Brand',
        DisplayName: 'Brand',
        LinkEntityTypeId: 'BrandProduct',
      },
      {
        Id: 'Bundle',
        DisplayName: 'Bundles',
        LinkEntityTypeId: 'BundleProduct',
      },
      {
        Id: 'ChannelNode',
        DisplayName: 'Nodes',
        LinkEntityTypeId: 'ChannelNodeProduct',
      },
      {
        Id: 'Section',
        DisplayName: 'Section',
        LinkEntityTypeId: 'SectionProducts',
      },
      {
        Id: 'Task',
        DisplayName: 'Tasks',
        LinkEntityTypeId: 'TaskProduct',
      },
    ],
    Children: [
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'ProductItem',
      },
      {
        Id: 'Resource',
        DisplayName: 'Resources',
        LinkEntityTypeId: 'ProductResource',
      },
      {
        Id: 'Specification',
        DisplayName: 'Specification',
        LinkEntityTypeId: 'ProductSpecification',
      },
    ],
  },
  {
    Id: 'PS',
    DisplayName: 'ProductSet',
    Name: {
      stringMap: {
        en: 'ProductSet',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'ChannelNode',
        DisplayName: 'Pimary Category',
        LinkEntityTypeId: 'ChannelNodeProductSetPrimary',
      },
      {
        Id: 'ChannelNode',
        DisplayName: 'Node',
        LinkEntityTypeId: 'NodeProductSet',
      },
    ],
    Children: [
      {
        Id: 'Item',
        DisplayName: 'Item',
        LinkEntityTypeId: 'ProductSetItem',
      },
    ],
  },
  {
    Id: 'Publication',
    DisplayName: 'Publication',
    Name: {
      stringMap: {
        en: 'Publication',
        'fr-CA': 'Publication',
      },
    },
    IsLinkEntityType: false,
    Parents: [],
    Children: [
      {
        Id: 'Section',
        DisplayName: 'Sections',
        LinkEntityTypeId: 'PublicationSections',
      },
    ],
  },
  {
    Id: 'Resource',
    DisplayName: 'Resource',
    Name: {
      stringMap: {
        en: 'Resource',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Channel',
        DisplayName: 'Channel ',
        LinkEntityTypeId: 'ChannelResource',
      },
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'ItemResource',
      },
      {
        Id: 'ChannelNode',
        DisplayName: 'Node',
        LinkEntityTypeId: 'nodeResource',
      },
      {
        Id: 'Package',
        DisplayName: 'Packages',
        LinkEntityTypeId: 'PackageResource',
      },
      {
        Id: 'Product',
        DisplayName: 'Products',
        LinkEntityTypeId: 'ProductResource',
      },
      {
        Id: 'Runtime',
        DisplayName: 'Runtime',
        LinkEntityTypeId: 'RuntimeResource',
      },
      {
        Id: 'Task',
        DisplayName: 'Tasks',
        LinkEntityTypeId: 'TaskResource',
      },
    ],
    Children: [
      {
        Id: 'TargetExporter',
        DisplayName: 'TargetExporter',
        LinkEntityTypeId: 'ResourceTargetExporter',
      },
    ],
  },
  {
    Id: 'Runtime',
    DisplayName: 'Runtime',
    Name: {
      stringMap: {
        en: 'Runtime',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [],
    Children: [
      {
        Id: 'Resource',
        DisplayName: 'Resource',
        LinkEntityTypeId: 'RuntimeResource',
      },
      {
        Id: 'TargetExporter',
        DisplayName: 'TargetExporter',
        LinkEntityTypeId: 'RuntimeTargetExporter',
      },
    ],
  },
  {
    Id: 'Section',
    DisplayName: 'Section',
    Name: {
      stringMap: {
        en: 'Section',
        'fr-CA': 'Section',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Publication',
        DisplayName: 'Publication',
        LinkEntityTypeId: 'PublicationSections',
      },
    ],
    Children: [
      {
        Id: 'Bundle',
        DisplayName: 'Bundle',
        LinkEntityTypeId: 'SectionBundle',
      },
      {
        Id: 'Product',
        DisplayName: 'Products',
        LinkEntityTypeId: 'SectionProducts',
      },
    ],
  },
  {
    Id: 'Specification',
    DisplayName: 'Specification',
    Name: {
      stringMap: {
        en: 'Specification',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Product',
        DisplayName: 'Product',
        LinkEntityTypeId: 'ProductSpecification',
      },
    ],
    Children: [],
  },
  {
    Id: 'SubItem',
    DisplayName: 'Sub Item',
    Name: {
      stringMap: {
        en: 'Sub Item',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Item',
        DisplayName: 'Item',
        LinkEntityTypeId: 'ItemSubItem',
      },
    ],
    Children: [],
  },
  {
    Id: 'TargetExporter',
    DisplayName: 'TargetExporter',
    Name: {
      stringMap: {
        en: 'TargetExporter',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Channel',
        DisplayName: 'Channel',
        LinkEntityTypeId: 'ChannelTargetExporter',
      },
      {
        Id: 'Resource',
        DisplayName: 'Resource',
        LinkEntityTypeId: 'ResourceTargetExporter',
      },
      {
        Id: 'Runtime',
        DisplayName: 'Runtime',
        LinkEntityTypeId: 'RuntimeTargetExporter',
      },
    ],
    Children: [],
  },
  {
    Id: 'Task',
    DisplayName: 'Task',
    Name: {
      stringMap: {
        en: 'Task',
        'fr-CA': '',
      },
    },
    IsLinkEntityType: false,
    Parents: [
      {
        Id: 'Milestone',
        DisplayName: 'Milestones',
        LinkEntityTypeId: 'MilestonTask',
      },
    ],
    Children: [
      {
        Id: 'Item',
        DisplayName: 'Items',
        LinkEntityTypeId: 'TaskItem',
      },
      {
        Id: 'Product',
        DisplayName: 'Products',
        LinkEntityTypeId: 'TaskProduct',
      },
      {
        Id: 'Resource',
        DisplayName: 'Resources',
        LinkEntityTypeId: 'TaskResource',
      },
    ],
  },
];
