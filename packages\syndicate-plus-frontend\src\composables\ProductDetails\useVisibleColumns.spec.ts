import { it, describe, expect } from 'vitest';
import { ref } from 'vue';
import { useVisibleColumns } from '@composables/ProductDetails';
import { ProductDetailsFilter, FilterField } from '@customTypes/Products';

describe('useVisibleColumns', () => {
  it('should return columns marked as required when filter is empty', async () => {
    // Arrange
    const columns = ref([
      { name: 'a', isRequired: true },
      { name: 'b', isRequired: true },
      { name: 'c', isRequired: false },
    ] as FilterField[]);
    const filter = ref({} as ProductDetailsFilter);

    // Act
    const { visibleColumns } = useVisibleColumns(columns, filter);

    // Assert
    expect(visibleColumns.value).to.deep.equal(['a', 'b']);
  });

  it('should return filtered columns when filter is set', async () => {
    // Arrange
    const columns = ref([
      { name: 'a', isRequired: true },
      { name: 'b', isRequired: true },
      { name: 'c', isRequired: false },
    ] as FilterField[]);
    const filter = ref({ visibleColumns: ['b'] } as ProductDetailsFilter);

    // Act
    const { visibleColumns } = useVisibleColumns(columns, filter);

    // Assert
    expect(visibleColumns.value).to.deep.equal(['b']);
  });
});
