import { ref, onBeforeMount, Ref } from 'vue';
import { storeToRefs } from 'pinia';
import { HttpAuth0Client } from '@httpservices/HttpAuth0Client';
import {
  InboundTemplateField,
  TemplateDefinition,
  TemplateMappingEntry,
  FullMappingPathTemplateField,
} from '@customTypes';
import { useAdapterSettingsStore, useTemplatesStore } from '@stores';
import { CoreFieldMatcher } from '@utils';
import { fetchInboundTemplateIds } from '@services/DataFlow/InboundTemplateService';
import { sortInboundTemplates } from '@composables/FieldMapping/inboundSortMethod';

export default function useInboundTemplates(selectedRows?: Ref<FullMappingPathTemplateField[]>) {
  const adapterSettingsStore = useAdapterSettingsStore();

  // Variables
  let coreFieldMatcher: CoreFieldMatcher | undefined;

  // Refs
  const inboundTemplateFields = ref<InboundTemplateField[]>();
  const selectedInboundTemplate = ref<TemplateDefinition>();
  const inboundTemplates = ref<TemplateDefinition[]>([]);
  const isLoading = ref<boolean>(false);
  const { coreFieldMappingSetting } = storeToRefs(adapterSettingsStore);

  // Composables
  const store = useTemplatesStore();
  const { fetchTemplates, fetchTemplatesDefinitions, getTemplateById, getTemplateDefinitionsByTemplateIds } = store;

  // Functions
  const loadInboundData = async (): Promise<void> => {
    isLoading.value = true;
    try {
      if (selectedInboundTemplate.value !== undefined) {
        const orgId = await HttpAuth0Client.getInstance().getUserOrganizationId();
        await fetchTemplates(selectedInboundTemplate.value.id.toString(), orgId);
        const template = getTemplateById(selectedInboundTemplate.value.id.toString());
        inboundTemplateFields.value = toInboundTemplateFields(template.fields);
        if (selectedRows) {
          selectedRows.value = [];
        }
      }
    } finally {
      isLoading.value = false;
    }
  };

  const toInboundTemplateFields = (fields?: TemplateMappingEntry[]): InboundTemplateField[] | undefined => {
    return fields
      ?.filter((f) => f.outputField?.name && !f.hasNoColumnValue())
      ?.map((m) => m.toInboundTemplateField(coreFieldMatcher))
      ?.filter((templateField, index, self) => {
        return (
          self.findIndex(
            (v) =>
              v.syndicationFieldName === templateField.syndicationFieldName && v.sheetName === templateField.sheetName
          ) === index
        );
      });
  };

  // Lifecycle methods
  onBeforeMount(async () => {
    await adapterSettingsStore.fetchCoreFieldMapping();
    coreFieldMatcher = new CoreFieldMatcher(coreFieldMappingSetting.value?.CoreFieldMapping);

    await fetchTemplatesDefinitions();
    const dataFlowTemplateIds = await fetchInboundTemplateIds();
    if (!dataFlowTemplateIds.length) {
      isLoading.value = false;
      return;
    }

    try {
      inboundTemplates.value = getTemplateDefinitionsByTemplateIds(dataFlowTemplateIds);
      inboundTemplates.value = sortInboundTemplates(inboundTemplates.value);

      if (!inboundTemplates.value.length) {
        return;
      }

      selectedInboundTemplate.value = inboundTemplates.value[0];
      await loadInboundData();
    } finally {
      isLoading.value = false;
    }
  });

  return {
    isLoading,
    inboundTemplateFields,
    selectedInboundTemplate,
    inboundTemplates,
    loadInboundData,
  };
}
