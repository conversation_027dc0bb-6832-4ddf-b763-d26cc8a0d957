import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';
import { ManageMappingsPage } from '@pages/welcomePage/welcomeSettings/formatsTab/manageMappings/manageMappings.page';

test.describe('Manage mappings', () => {
  const formatSpeakers = 'best-buy - Speakers';
  const newMappingName = 'UI-Test New Mapping';
  const entityTypeActivity = 'Activity';
  const entityTypeProduct = 'Product';
  const sourceName01 = 'ProductSyndicatePlusCollection';
  const sourceName02 = 'ProductBrandCode';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatTabPage: FormatsTabPage;
  let mappingsPage: ManageMappingsPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatTabPage = new FormatsTabPage(localPage);
    mappingsPage = new ManageMappingsPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.manageMappingsButton.click();
    // Delete mapping if exists
    await mappingsPage.deleteMappingIfExists(newMappingName);
    await expect(formatTabPage.formatsTable).toBeVisible();
    await expect(mappingsPage.mappingName(newMappingName)).toBeHidden();
  });

  test('Add, edit and remove new mapping', async () => {
    // Create new mapping
    await mappingsPage.addMapping.click();
    await mappingsPage.mappingNameInput.fill(newMappingName);
    await mappingsPage.entityTypeDropDown(entityTypeActivity).click();
    await mappingsPage.entityTypeDropDown(entityTypeProduct).click();
    await mappingsPage.saveNewMapping.click();
    await expect(mappingsPage.fieldsDropdown).toBeVisible(); // Wait for page to load
    // Edit mapping
    await mappingsPage.mapAutoButton.click();
    await expect(mappingsPage.source(sourceName01), 'Field was not mapped').toBeVisible();
    await mappingsPage.getRow(sourceName01).click();
    await mappingsPage.unmapButton.click();
    await expect(mappingsPage.source(sourceName01), 'Field remain mapped').toBeHidden();
    await expect(mappingsPage.source(sourceName02), 'Field was unmapped').toBeVisible();
    await mappingsPage.saveButton.click();
    await expect(mappingsPage.saveChangesDialog, 'Changes saved dialog is not visible').toBeVisible();
    await mappingsPage.goBackButton.click();
    await expect(mappingsPage.mappingName(newMappingName), 'New mapping is not visible').toBeVisible();
    // Remove mapping
    await mappingsPage.deleteMappingIfExists(newMappingName);
    await expect(mappingsPage.mappingName(newMappingName)).toBeHidden();
  });
});
