name: $(Build.SourceBranchName).$(Date:MM.dd)$(Rev:.r)

trigger:
  branches:
    include:
      - main

parameters:
  - name: stack
    type: string
    default: euwDev1a
    displayName: Stack

resources:
  repositories:
    - repository: iPMC.Autotest.DevOps.Templates
      type: git
      name: 821ff125-367d-4cd3-82db-3c576ae40e0e/iPMC.Autotest.DevOps

stages:
  - template: templates/integration-tests.yml
    parameters:
      stack: ${{ parameters.stack }}