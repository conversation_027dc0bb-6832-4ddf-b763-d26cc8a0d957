import { computed, Ref } from 'vue';
import { ProductOutboxRow } from '@customTypes';
import { useRouter } from '@composables/useRouter';
import { OutboxRowType } from '@customTypes/OutboxRowType';
import { SyndicationPageTabNames } from '@enums';

export default function useGoToErrors(
  tab: Ref<SyndicationPageTabNames>,
  selectedRow: Ref<ProductOutboxRow | undefined>
) {
  const { goToPage } = useRouter();
  const showGoToErrors = computed(() => {
    if (
      selectedRow?.value === undefined ||
      selectedRow.value === null ||
      tab.value !== SyndicationPageTabNames.Outbox
    ) {
      return false;
    }
    return (
      (selectedRow.value.type === OutboxRowType.PluginExportProduct || selectedRow.value.type === OutboxRowType.API) &&
      selectedRow.value.failedRecords! > 0
    );
  });

  const goToErrors = () => {
    if (selectedRow.value === undefined) {
      throw Error('No outbox row selected!');
    }
    goToPage('outbox-error-page', {
      outboxId: selectedRow.value?.id,
    });
  };

  return { showGoToErrors, goToErrors };
}
