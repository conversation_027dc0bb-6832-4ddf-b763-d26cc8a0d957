import { computed, ref, Ref } from 'vue';
import { SyndicationPageTabNames, ProductGrouping } from '@enums';
import { Product } from '@customTypes';
import { notify } from '@inriver/inri';
import { ProductFilterDto } from '@dtos';
import isFeatureEnabled from '@utils/isFeatureEnabled';

export default function useExportBelowTheFoldButton(
  useI18n: Function,
  activeTab: Ref<SyndicationPageTabNames>,
  selectedProducts: Ref<Product[]>,
  filter: Ref<ProductFilterDto>
) {
  const { t } = useI18n && useI18n();

  // Variables
  const MAX_PRODUCTS = 20;

  // Refs
  const showExportBelowTheFoldDialog = ref<boolean>(false);

  // Computed
  const isExportBelowTheFoldButtonVisible = computed(
    () =>
      activeTab.value === SyndicationPageTabNames.Products &&
      !!selectedProducts.value?.length &&
      filter.value.groupBy.id === ProductGrouping.CUSTOM &&
      isFeatureEnabled('below-the-fold')
  );

  // Functions
  const openExportBelowTheFoldDialog = () => {
    if (selectedProducts.value?.length > MAX_PRODUCTS) {
      notify.error(`${t('syndicate_plus.below_the_fold.export_button.disabled', { max: MAX_PRODUCTS })}`, {
        position: 'bottom-right',
      });

      return;
    } else {
      showExportBelowTheFoldDialog.value = true;
    }
  };

  return { openExportBelowTheFoldDialog, showExportBelowTheFoldDialog, isExportBelowTheFoldButtonVisible };
}
