import { ref } from 'vue';
import { defineStore } from 'pinia';
import { Entity, RelationLink } from '@core/interfaces';
import { searchV2 } from '@core/services/searchv2Api';
import { SpecificationField } from '@core/interfaces/Specification';
import { fetchSpecificationFields } from '@core/services';

export const useSpecificationsStore = defineStore('specificationsStore', () => {
  // Refs
  const templatesCache = ref<{
    [sourceEntityTypeId: string]: Entity[];
  }>({});
  const templateFieldsCache = ref<{
    [templateId: number]: SpecificationField[];
  }>({});
  const isLoading = ref(false);

  // Functions
  const fetchTemplates = async (relations: RelationLink[]): Promise<void> => {
    isLoading.value = true;

    try {
      for (const relation of relations) {
        if (!!templatesCache.value[relation.sourceEntityTypeId]) {
          continue;
        }

        const parameters = [
          {
            type: 'System',
            id: 'EntityType',
            operator: 'Equals',
            value: 'Specification',
            relationdirection: '',
            relationtargetentitytypeid: '',
            relationsourceentitytypeid: '',
            datatype: '',
            datetimeInterval: false,
          },
          {
            type: 'Relation',
            id: relation.id,
            operator: 'NotEmpty',
            value: null,
            relationdirection: 'InBound',
            relationtargetentitytypeid: 'Specification',
            relationsourceentitytypeid: relation.sourceEntityTypeId,
            datatype: '',
            datetimeInterval: false,
          },
        ];
        const response = await searchV2(parameters as []);
        templatesCache.value[relation.sourceEntityTypeId] = response?.Groups[0]?.Data?.Entities ?? [];
      }
    } catch (e) {
      console.error('Error fetching specifications', e);
    } finally {
      isLoading.value = false;
    }
  };

  const fetchTemplateFields = async (specificationId: number): Promise<void> => {
    isLoading.value = true;
    try {
      if (!!templateFieldsCache.value[specificationId]) {
        return;
      }

      templateFieldsCache.value[specificationId] = await fetchSpecificationFields(specificationId);
    } catch (e) {
      console.error('Error fetching specification fields', e);
    } finally {
      isLoading.value = false;
    }
  };

  return {
    isLoading,
    templatesCache,
    templateFieldsCache,
    fetchTemplates,
    fetchTemplateFields,
  };
});
