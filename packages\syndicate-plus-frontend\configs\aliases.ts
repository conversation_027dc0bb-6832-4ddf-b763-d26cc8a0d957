import * as path from 'path';

export default function (__dirname) {
  return {
    '@configs': path.resolve(__dirname, './configs/'),
    '@utils': path.resolve(__dirname, './src/utils/'),
    '@customTypes': path.resolve(__dirname, './src/types/'),
    '@enums': path.resolve(__dirname, './src/enums/'),
    '@dtos': path.resolve(__dirname, './src/dtos/'),
    '@stores': path.resolve(__dirname, './src/stores/'),
    '@mocks': path.resolve(__dirname, './src/mocks/'),
    '@components': path.resolve(__dirname, './src/components/'),
    '@inriverI18n': path.resolve(__dirname, './src/i18n/'),
    '@pages': path.resolve(__dirname, './src/pages/'),
    '@composables': path.resolve(__dirname, './src/composables/'),
    '@approot': path.resolve(__dirname, './../../'),
    '@services': path.resolve(__dirname, './src/services/'),
    '@httpservices': path.resolve(__dirname, './src/httpservices/'),
    '@insights': path.resolve(__dirname, './src/insights/'),
    '@const': path.resolve(__dirname, './src/const/'),
    '@helpers': path.resolve(__dirname, './src/helpers/'),
    '@core': path.resolve(__dirname, './src/core/'),
  };
}
