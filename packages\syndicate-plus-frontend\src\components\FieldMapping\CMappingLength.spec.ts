import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';

import CMappingLength from '@components/FieldMapping/CMappingLength.vue';

describe('CMappingLength', () => {
  it('displays length correctly', async () => {
    // Arrange
    const minLength = 5;
    const maxLength = 25;
    const label = 'text';
    const wrapper = mount(CMappingLength, {
      props: {
        minLength,
        maxLength,
      },
      global: {
        mocks: {
          $t: () => label,
        },
      },
    });

    // Act
    const componentText = wrapper.text();

    // Assert
    const expectedText = `${label}: ${minLength} ${label}: ${maxLength}`;
    expect(componentText).toBe(expectedText);
  });
});
