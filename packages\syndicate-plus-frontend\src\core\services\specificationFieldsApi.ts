import { SpecificationField, SpecificationFieldResponse } from '@core/interfaces/Specification';
import { portalFetch, proxyPortalFetch } from '@utils';

export async function fetchSpecificationFields(specificationId: number): Promise<SpecificationField[]> {
  const fetch = import.meta.env.DEV ? proxyPortalFetch : portalFetch;
  const response = await fetch(`/api/specificationtemplate/${specificationId}/field`);
  const links = response?.ok ? ((await response.json()) as SpecificationFieldResponse[]) : [];

  return links.map((x) => {
    return {
      id: x.Id,
      CVLId: x.CVLId,
      displayName: x.DisplayName,
      specificationDataType: x.SpecificationDataType,
      unit: x.Unit,
    } as SpecificationField;
  });
}
