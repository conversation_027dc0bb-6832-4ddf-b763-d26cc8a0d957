<template>
  <div class="flex flex-nowrap">
    <div v-if="!isPreviewMode" class="overflow-x-hidden modules">
      <div v-if="!isFieldsTabEnabled" class="modules-content-old">
        <div class="mt-10px mb-15px font-bold q-item__label">
          {{ $t('syndicate_plus.aplus_define_template.standard_modules') }}
        </div>
        <draggable
          v-model="modules"
          :group="{
            name: 'modules',
            pull: 'clone',
            put: false,
          }"
          :sort="false"
          :move="moveModule"
          item-key="contentName"
          ghost-class="builder-ghost"
        >
          <template #item="{ element }">
            <div class="content-map-item cursor-pointer">
              {{ getDisplayName(element) }}
              <div class="triangle"></div>
            </div>
          </template>
        </draggable>
      </div>
      <div v-else class="modules-content">
        <q-tabs
          v-model="tab"
          class="bg-white text-grey-7 tab-panel-tabs"
          active-color="primary"
          active-class="active-tab"
          indicator-color="transparent"
          align="justify"
          no-caps
        >
          <q-tab
            :name="TemplateBuilderPanelTabNames.MODULES_TAB"
            :label="$t('syndicate_plus.aplus_define_template.modules')"
          />
          <q-tab
            v-if="isFieldsTabEnabled"
            :name="TemplateBuilderPanelTabNames.FIELDS_TAB"
            :label="$t('syndicate_plus.aplus_define_template.fields')"
          />
        </q-tabs>
        <q-tab-panels v-model="tab">
          <q-tab-panel :name="TemplateBuilderPanelTabNames.MODULES_TAB">
            <draggable
              v-model="modules"
              :group="{
                name: 'modules',
                pull: 'clone',
                put: false,
              }"
              :sort="false"
              :move="moveModule"
              item-key="contentName"
              ghost-class="builder-ghost"
            >
              <template #item="{ element }">
                <div class="content-map-item cursor-pointer">
                  {{ getDisplayName(element) }}
                  <div class="triangle"></div>
                </div>
              </template>
            </draggable>
          </q-tab-panel>
          <q-tab-panel class="fields-tab-panel" :name="TemplateBuilderPanelTabNames.FIELDS_TAB">
            <c-fields-tab-panel
              :is-preview="isPreview"
              :is-fields-tab-enabled="isFieldsTabEnabled"
              :trading-partner-name="tradingPartnerName"
            />
          </q-tab-panel>
        </q-tab-panels>
      </div>
      <q-inner-loading v-if="isLoading" showing color="primary">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </div>
    <c-aplus-products-filter-panel
      v-if="isPreviewMode"
      :trading-partner-name="tradingPartnerName"
      @handle-product-selection="handleProductSelection"
    />
    <div
      ref="builderContentRef"
      :class="{ dragging: isRightModuleDragging }"
      class="overflow-auto builder-content"
      @dragleave="onBuilderContentLeave"
    >
      <draggable
        v-model="contentModules"
        group="modules"
        handle=".handle"
        ghost-class="builder-ghost"
        :item-key="(module) => (module ? module.id : uuidv4())"
        @change="changeBuilderModule"
        @start="isRightModuleDragging = true"
        @end="isRightModuleDragging = false"
      >
        <template #item="{ element, index }">
          <div :data-index="index" :class="{ focus: index === targetIndex && !isRightModuleDragging }">
            <div v-if="element">
              <c-preview-module
                v-if="isPreviewMode"
                class="aplus-preview-module"
                :module-type="element.moduleType"
                :index="index"
              />
              <c-edit-module
                v-else
                class="aplus-edit-module"
                :module-type="element.moduleType"
                :index="index"
                @delete-builder-module="deleteBuilderModule(index)"
              />
            </div>
            <div v-else-if="!isPreview" class="drop-area">
              <span>{{ $t('syndicate_plus.aplus_define_template.drag_module_here') }}</span>
            </div>
          </div>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRef, onUnmounted, inject, watch, computed } from 'vue';
import { notify } from '@inriver/inri';
import { v4 as uuidv4 } from 'uuid';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import draggable from 'vuedraggable';
import { TemplateBuilderPanelTabNames } from '@enums';
import { ModuleTypes } from '@enums/Aplus';
import { CPreviewModule } from '@components/TemplateBuilder/PreviewModules';
import { CEditModule } from '@components/TemplateBuilder/EditModules';
import { CAplusProductsFilterPanel, CFieldsTabPanel } from '@components/TemplateBuilder';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { BaseContentModuleData, ContentModule } from '@customTypes/Aplus';
import { Product } from '@customTypes';
import { useEditStatus } from '@composables/Common';
import { templateBuilderStatusMessages } from '@const';
import { getDisplayName } from '@helpers';
import isFeatureEnabled from '@utils/isFeatureEnabled';

const props = defineProps({
  allModules: {
    type: Array<ModuleTypes>,
    default: [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  isPreview: {
    type: Boolean,
    default: false,
  },
  tradingPartnerName: {
    type: String,
    default: '',
  },
});

const store = useTemplateBuilderStore();

// Variables
const destinationId = inject('destinationId');
const subCatalogId = inject('subCatalogId');

// Refs
const tab = ref(TemplateBuilderPanelTabNames.MODULES_TAB);
const modules = toRef(props, 'allModules');
const isPreviewMode = toRef(props, 'isPreview');
const targetIndex = ref<number | undefined>();
const isRightModuleDragging = ref<boolean>(false);
const { contentModules, saveStatus } = storeToRefs(store);
const builderContentRef = ref<HTMLElement | null>(null);

// Computed
const isFieldsTabEnabled = computed(() => isFeatureEnabled('aplus-fields'));

// Composables
const { t } = useI18n();
useEditStatus(saveStatus, templateBuilderStatusMessages);

// Functions
const moveModule = (e) => {
  e.willInsertAfter = false;
  targetIndex.value = parseInt(e.related.dataset.index);
  if (!canReplaceModule(e.related.dataset.index)) {
    return false;
  }
};

const canReplaceModule = (index: number): boolean => contentModules.value[index] === undefined;

const changeBuilderModule = (e) => {
  if (e.added) {
    contentModules.value = removeTempContentModules();
    targetIndex.value !== undefined &&
      contentModules.value[targetIndex.value] === undefined &&
      store.commitChanges(targetIndex.value, {
        moduleType: e.added.element,
        data: {},
        id: uuidv4(),
      } as ContentModule<BaseContentModuleData>);
    targetIndex.value = undefined;
  }
};

const removeTempContentModules = (): ContentModule<BaseContentModuleData>[] =>
  contentModules.value.filter((x) => typeof x !== 'string');

const deleteBuilderModule = (index: number): void => {
  contentModules.value[index] = undefined;
  store.commitChanges(index, undefined);
};

const onBuilderContentLeave = (e) => {
  if ([...e.fromElement?.classList].includes('modules')) {
    targetIndex.value = undefined;
  }
};

const handleProductSelection = async (product: Product) => {
  try {
    await store.fetchAplusTemplatePreview(subCatalogId, destinationId, product.customGroup);
  } catch (error) {
    notify.error(t('syndicate_plus.aplus_define_template.notifications.preview_error'), {
      position: 'bottom-right',
    });
  }
};

onUnmounted(() => {
  store.$reset();
});

watch(
  () => isPreviewMode.value,
  () => {
    builderContentRef.value?.scrollTo({ top: 0, behavior: 'smooth' });
  }
);
</script>

<style lang="scss" scoped>
$large-block-min-height: 200px;

.focus {
  .drop-area {
    border: 1px dashed var(--color-green) !important;
  }
}

.builder-content {
  height: 84vh;
  overflow-y: scroll;
  width: 85%;
}

.modules {
  border-right: 1px solid var(--color-border);
  height: 84vh;
  width: 15%;
  min-width: 250px;

  .modules-content {
    padding: 0px;
  }

  .modules-content-old {
    padding: 10px 20px;
    width: 225px;
  }
}

.builder-content {
  padding: 20px 40px;

  .drop-area {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: $large-block-min-height;
    border: 1px dashed var(--color-grey);
    margin-bottom: 10px;
    min-width: 200px;

    span {
      border: 1px solid var(--color-border);
      border-radius: 25px;
      padding: 10px;
    }

    &.focus {
      border: 1px dashed var(--color-green);
    }
  }

  .builder-ghost {
    display: none;
  }
}

.dragging {
  .builder-ghost {
    display: block;
    margin-bottom: 10px;

    .builder-module {
      border: 1px dashed var(--color-green);
    }
  }
}

.content-map-item {
  position: relative;
  padding: 7px;
  border: 1px dashed var(--color-grey);
  border-radius: 8px;
  background-color: var(--color-grey-10);
  margin-bottom: 10px;
}

.triangle {
  position: absolute;
  top: 50%;
  right: -10px;
  height: 25px;
  width: 25px;
  background-color: var(--color-grey-10);
  border: 1px dashed var(--color-grey);
  transform: translateY(-50%) rotate(45deg);
  border-radius: 0px 5px 0px 0px;
  border-left: 0px;
  border-bottom: 0px;
}

.fields-tab-panel {
  min-height: 140px;
}
</style>
