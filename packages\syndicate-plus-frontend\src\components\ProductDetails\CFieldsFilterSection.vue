<template>
  <c-separated-expansion-item :is-expanded="isExpanded" :label="sectionLabel">
    <div v-if="isColumnsLoading">
      <c-spinner size="40" class="spinner" />
    </div>
    <div v-else class="flex flex-col">
      <c-search v-model="searchValue" label="search" dense class="search" />
      <div class="flex flex-col filter-buttons">
        <a class="clear-btn" aria-label="clear filter" @click="clearFilter">
          {{ $t('syndicate_plus.common.filter.reset_filters') }}
        </a>
      </div>
      <q-checkbox
        v-for="(column, index) in selectedColumns"
        v-bind="$inri.checkbox"
        :key="index"
        v-model="selectedColumnNames"
        :data-testid="column.name"
        :val="column.name"
        :label="createLabel(column)"
        @click="applyFilterSelections"
      />
      <q-separator v-if="separatorIsVisible" class="separator" />
      <q-checkbox
        v-for="(column, index) in unselectedColumns"
        v-bind="$inri.checkbox"
        :key="index"
        v-model="selectedColumnNames"
        :data-testid="column.name"
        :val="column.name"
        :label="createLabel(column)"
        @click="applyFilterSelections"
      />
    </div>
  </c-separated-expansion-item>
</template>

<script setup lang="ts">
import { watch, computed, inject, onMounted, toRef } from 'vue';
import { CSeparatedExpansionItem } from '@components/Shared';
import { useI18n } from 'vue-i18n';
import { FilterField } from '@customTypes/Products';
import {
  useFilterFieldsSection,
  useFilterFieldsSearch,
  useFilterFieldsActionButtons,
  useFilterFieldsSectionName,
} from '@composables/ProductDetails';

const props = defineProps({
  isExpanded: {
    type: Boolean,
    default: true,
  },
  isColumnsLoading: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array<FilterField>,
    default: [] as FilterField[],
  },
  showInternal: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['apply-fields-filter']);

// Variables
const destinationId = inject<number>('destinationId');

// Refs
const showInternalNames = toRef(props, 'showInternal');

// Computed
const allColumns = computed(() => props.columns);

// Composables
const { searchValue, filteredColumns } = useFilterFieldsSearch(allColumns, showInternalNames);
const { selectedColumnNames, selectedColumns, unselectedColumns, separatorIsVisible, createLabel } =
  useFilterFieldsSection(filteredColumns, showInternalNames, destinationId);
const { isFilterInitialized, clearFilter, getDefaultColumnNames } = useFilterFieldsActionButtons(
  allColumns,
  selectedColumnNames,
  applyFilterSelections,
  destinationId
);
const { sectionLabel } = useFilterFieldsSectionName(selectedColumnNames, useI18n);

// Functions
function applyFilterSelections(): void {
  emit('apply-fields-filter', selectedColumnNames.value);
}

// Lifecycle methods
onMounted(() => {
  applyFilterSelections();
});

watch(
  () => props.columns,
  (oldValue, newValue) => {
    if (oldValue != newValue && !isFilterInitialized.value) {
      selectedColumnNames.value = getDefaultColumnNames();
      applyFilterSelections();
    }
  }
);
</script>

<style scoped lang="scss">
.filter-buttons {
  flex-direction: row;
  min-height: 21px;
  align-items: center;

  .clear-btn {
    cursor: pointer;
    margin-left: auto;
  }
}

.separator,
.search {
  margin: 10px 0;
}

.spinner {
  margin: auto;
}
</style>
