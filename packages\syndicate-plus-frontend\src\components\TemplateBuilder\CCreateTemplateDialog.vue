<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <div class="row q-gutter-lg w-1/2">
      <div class="col section">
        <h3>
          {{ $t('syndicate_plus.aplus_define_template.create_template') }}
        </h3>
        <q-input
          v-model="templateName"
          v-bind="$inri.input"
          :label="$t('syndicate_plus.aplus_define_template.template_name')"
          autofocus
          @keyup.enter="logEventAndClick(ActionName.A_PLUS_SAVE_TEMPLATE, onSave)"
        />

        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.common.save')"
            :disabled="saveButtonIsDisabled"
            @click="logEventAndClick(ActionName.A_PLUS_SAVE_TEMPLATE, onSave)"
          >
          </c-btn>
        </div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useDialog } from '@inriver/inri';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';

const emit = defineEmits(['on-save']);
defineExpose({ closeDialog });

// Refs
const templateName = ref('');

// Computed
const saveButtonIsDisabled = computed(() => isLoading.value || !templateName.value?.trim());

// Composables
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel, confirmSuccess } = useDialog();

// Functions
const onSave = async () => {
  emit('on-save', templateName.value);
};

function closeDialog() {
  confirmSuccess(null);
}
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }

  .action-buttons {
    padding-top: 10px;
  }
}
</style>
