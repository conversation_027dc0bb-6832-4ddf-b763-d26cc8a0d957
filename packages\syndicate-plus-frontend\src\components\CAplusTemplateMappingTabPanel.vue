<template>
  <c-section>
    <div class="c-aplus-templates-tab-panel">
      <div v-if="pageIsLoading" class="spinner">
        <c-spinner />
      </div>
      <q-table
        v-if="tableIsVisible"
        ref="tableRef"
        v-model:selected="selected"
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        flat
        dense
        hide-bottom
        separator="cell"
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        :columns="aplusTemplatesColumns"
        :hide-header="false"
        class="aplus-templates-table"
        :rows="allTemplates"
        :row-key="(row) => row.aplusTemplateId"
        :rows-per-page-options="[0]"
        @row-click="onRowClick"
        @row-dblclick="onRowDoubleClick"
      >
        <template #body-cell-modules="scope">
          <q-td>{{ scope.row.contentModelList?.length ?? 0 }} </q-td>
        </template>
      </q-table>
      <c-no-data
        v-if="noDataIsVisible"
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.mapping.aplus_templates_tab.no_data.no_templates_title')"
        :text="$t('syndicate_plus.mapping.aplus_templates_tab.no_data.no_templates_message')"
      />
    </div>
  </c-section>
</template>

<script lang="ts" setup>
import CNoData from './CNoData.vue';
import { onBeforeMount, ref, computed, Ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppInsightsStore } from '@stores';
import { TemplateService, AplusContentMapService } from '@services';
import { PageName } from '@enums';
import { aplusTemplatesColumns } from '@components';
import { APlusTemplate, AplusContentMap } from '@customTypes/Aplus';
import { notify } from '@inriver/inri';

const emits = defineEmits(['on-row-click', 'on-double-click']);
defineExpose({ deleteAplusTemplate });

// Composables
const { t } = useI18n();
const { setScreenName } = useAppInsightsStore();

// Refs
const allTemplates = ref<any[]>([]);
const selected = ref<APlusTemplate[]>();
const pageIsLoading = ref(false);

// Computed
const tableIsVisible = computed(() => allTemplates.value?.length && !pageIsLoading.value);
const noDataIsVisible = computed(() => !allTemplates.value?.length && !pageIsLoading.value);

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.APLUS_TEMPLATES);
  pageIsLoading.value = true;
  await fetchTemplates();
  pageIsLoading.value = false;
});

// Functions
const onRowClick = (_, row: APlusTemplate): void => {
  const newRowValue = selected.value?.includes(row) ? null : row;
  selected.value = newRowValue ? [newRowValue] : [];
  emits('on-row-click', newRowValue);
};

const onRowDoubleClick = (_, row: APlusTemplate): void => {
  selected.value = [row];
  emits('on-double-click', row);
};

async function fetchTemplates() {
  allTemplates.value = await TemplateService.getAllAplusTemplates();
}

async function deleteAplusTemplate(selectedAplusTemplate: Ref<APlusTemplate>): Promise<void> {
  const templateName = selectedAplusTemplate.value?.templateName;
  const response = await TemplateService.deleteAplusTemplateById(
    selectedAplusTemplate.value?.aplusTemplateId as number
  );

  if (response?.type === 'ok') {
    notify.success(t('syndicate_plus.mapping.aplus_templates_tab.delete_template_success', { templateName }), {
      position: 'bottom-right',
    });
  }

  await deleteContentMaps(selectedAplusTemplate.value.contentModelList);
  await fetchTemplates();
}

const deleteContentMaps = async (contentMaps?: AplusContentMap[]) => {
  if (!contentMaps?.length) {
    return;
  }

  contentMaps.forEach(async (x) => await AplusContentMapService.deleteContentMap(x.aplusContentId));
};
</script>

<style lang="scss" scoped>
.spinner {
  margin: auto;
  width: min-content;
}

.aplus-templates-table {
  max-height: calc(100vh - 203px);
  margin-bottom: 200px;
}
</style>
