<template>
  <c-section>
    <q-table
      ref="tableRef"
      :pagination="pagination"
      dense
      :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
      flat
      :columns="tradingPartnerTableColumns"
      class="cursor-pointer"
      :rows="tradingPartnerList"
      row-key="field"
      :rows-per-page-options="[0]"
      :visible-columns="visibleColumns"
      separator="cell"
      hide-bottom
      binary-state-sort
      @row-click="goToProductPage"
    >
      <template #body-cell-status="props">
        <q-td :props="props" :class="{ 'cell-disabled': !props.row.isConnected }">
          <c-small-square :color="props.row.isConnected ? SquareColor.GREEN : SquareColor.RED" />
          <span class="ml-2">{{ props.row.isConnected ? 'connected' : 'not connected' }}</span>
        </q-td>
      </template>
      <template #body-cell="props">
        <q-td :props="props" :class="{ 'cell-disabled': !props.row.isConnected }">
          {{ props.row[props.col.field] }}
        </q-td>
      </template>
    </q-table>
  </c-section>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { TradingPartner } from '@customTypes';
import { SquareColor } from '@enums/SquareColor';
import { useRouter } from '@composables/useRouter';
import { CSmallSquare } from '@components';
import { tradingPartnerTableColumns } from '@const';
import isFeatureEnabled from '@utils/isFeatureEnabled';
import { SyndicationType } from '@core/enums';
import { TradingPartnerType } from '@core/interfaces';

const router = useRouter();

defineProps({
  title: {
    type: String,
    default: '',
  },
  tradingPartnerList: {
    type: Array<TradingPartnerType>,
    default: new Array<TradingPartner>(),
  },
});

// Variables
const pagination = {
  page: 1,
  rowsPerPage: 1000,
};

// Computed
const visibleColumns = computed(() =>
  tradingPartnerTableColumns.map((x) => {
    if (x.field === 'skus' && !isFeatureEnabled('skus')) {
      return;
    }

    return x.name;
  })
);

// Functions
const goToProductPage = (_: Event, row: TradingPartner) => {
  if (row.type === SyndicationType.Core) {
    return router.goToPage('trading-partner-page', {
      tradingPartnerId: row.id,
    });
  }
  if (row.subCatalogId != null && row.destinationId != null) {
    router.goToPage('syndication-page', {
      subCatalogId: row.subCatalogId,
      destinationId: row.destinationId,
    });
  }
};
</script>

<style lang="scss" scoped>
.field-mapping-table {
  margin-top: 30px;
}

.selected {
  background-color: #e8f8f1;
}

.cell-disabled {
  cursor: not-allowed;
  pointer-events: all !important;
}
</style>
