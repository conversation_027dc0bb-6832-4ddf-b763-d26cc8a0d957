<template>
  <div class="filter-panel p-20px">
    <div>
      <c-select
        v-model="selectedCollection"
        :options="tradingPartnerCollections"
        :label="$t('syndicate_plus.collections.collection')"
        clearable
        option-label="name"
        option-value="id"
        @update:model-value="onChangeCollection"
      />
      <c-inri-search
        v-model="searchValue"
        class="c-inri-custom-search pb-10px"
        :class="customSearchClass"
        dense
        :placeholder="$t('syndicate_plus.common.filter.search')"
        @search="search"
        @keydown.enter.prevent="search"
        @update:model-value="onSearchValueChanged"
        @clear="clearSearch"
      />
      <q-separator />
    </div>

    <c-preview-products
      :selected-products="selectedProducts"
      :products="products"
      :is-fetching="isFetching"
      @select-product="handleProductSelection"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { SearchState, SortDirection } from '@enums';
import { CInriSearch } from '@components';
import { CPreviewProducts } from '@components/TemplateBuilder';
import { CollectionDetails, Product } from '@customTypes';
import { CollectionService } from '@services';
import { usePreviewProductStore } from '@stores/TemplateBuilder';
import { useRoute } from 'vue-router';

const route = useRoute();

const props = defineProps({
  tradingPartnerName: {
    type: String,
    default: '',
  },
});

// Variables
const destinationId = inject('destinationId') as string;
const subCatalogId = inject('subCatalogId') as string;
const aplusTemplateId = route.params.templateId as string;

// Composables
const store = usePreviewProductStore();

// Refs
const selectedCollection = ref<CollectionDetails>();
const selectedProducts = ref<Product[]>([]);
const searchValue = ref('');
const tradingPartnerCollections = ref<CollectionDetails[]>([]);
const { isFetching, searchState, products } = storeToRefs(store);

const emit = defineEmits(['handle-product-selection']);

// Computed
const customSearchClass = computed(() => {
  const currentSearchState = searchState.value as SearchState;
  switch (currentSearchState) {
    case SearchState.SearchNotStarted:
      return '';
    case SearchState.SearchPending:
      return 'search-pending';
    case SearchState.SearchFinished:
      return 'search-finish';
    default:
      return '';
  }
});

// Functions
const clearSearch = () => {
  searchValue.value = '';
};

const delay = async (ms: number): Promise<void> => new Promise((resolve) => setTimeout(resolve, ms));

const search = async (): Promise<void> => {
  if (isFetching.value) {
    await delay(500);
    await search();
    return;
  }

  store.fetchProducts(subCatalogId, destinationId, selectedCollection.value?.id, searchValue.value, aplusTemplateId);
};

const onSearchValueChanged = async (): Promise<void> => {
  if (!searchValue.value) {
    store.updateSearchState(SearchState.SearchNotStarted);
    search();
    return;
  }

  store.updateSearchState(SearchState.SearchPending);
};

const handleProductSelection = (product: Product): void => {
  emit('handle-product-selection', product);
};

const onChangeCollection = async (): Promise<void> => await fetchAndTrySelectFirstProduct();

const fetchAndTrySelectFirstProduct = async (): Promise<void> => {
  await store.fetchProducts(
    subCatalogId,
    destinationId,
    selectedCollection.value?.id,
    searchValue.value,
    aplusTemplateId
  );

  if (products.value?.length) {
    const newSelectedProduct = products.value[0];
    selectedProducts.value = [newSelectedProduct];
    handleProductSelection(newSelectedProduct);
  }
};

// Lifecycle methods
onBeforeMount(async () => {
  tradingPartnerCollections.value = await CollectionService.getTradingPartnerCollections(
    props.tradingPartnerName,
    1000,
    1,
    SortDirection.ASC
  );
  await fetchAndTrySelectFirstProduct();
});
</script>

<style lang="scss" scoped>
.filter-panel {
  width: 15%;
  min-width: 250px;
  height: 84vh;
  background-color: var(--on-primary-color);
  border-right: 1px solid var(--color-border);
}

:deep(.c-inri-input.c-inri-input--default:not(.q-textarea) .q-field__control) {
  min-width: 210px;
}

:deep(.c-inri-input .q-field__native) {
  padding-left: 10px !important;
  word-break: break-word;
}

:deep(.c-inri-input.q-field--with-bottom) {
  padding-bottom: 10px;
}

:deep(.c-inri-input.c-inri-custom-search) {
  &.q-field--outlined .q-field__control {
    padding: 0px;
    border-radius: 0px;
    background: var(--color-grey-lighter);

    &::after {
      border: 0 !important;
    }
  }

  &.c-inri-input--dense.q-field--outlined .q-field__control::before {
    border: 0 !important;
  }

  .q-field__inner .q-field__control {
    padding: 0px;
    background: var(--color-grey-lighter);
    border-radius: 0px;

    .q-field__append span {
      width: 40px;
      height: 40px;
      border-left: 1px solid var(--surface-color);

      svg {
        height: 50%;
        width: 50%;
      }
    }
  }

  &.search-pending {
    .q-field__append span {
      color: var(--color-grey-darkest);
    }

    .icon-close {
      color: var(--color-grey-dark);
    }
  }

  &.search-finish {
    .icon-close {
      color: var(--color-grey-darkest);
    }
  }
}
</style>
