import { FormatEnum } from './Mapping';

export interface DynamicMappingResponse {
  name: string;
  data: string;
  createdBy: string;
  id: number;
  tradingPartnerId: string;
  category: string;
  createdDate: string;
  updatedBy: string;
  updatedDate: string;
  environmentFormatId?: string;
}

// TODO: check where we use this interface and rename accordingly
export interface DynamicMappingToSave {
  name: string;
  data: string;
  createdBy: string;
}

export interface NewMapping {
  MappingId: number | null;
  MappingName: string;
  WorkareaEntityTypeId: string;
  OutputEntityTypeId: string;
  EnableSKU: boolean;
  FirstRelatedEntityTypeId: string | null;
  SecondRelatedEntityTypeId: string | null;
  FormatId: number;
  ImageUrl: string | null;
  FirstLinkEntityTypeId: string | null;
  SecondLinkEntityTypeId: string | null;
  DefaultLanguage: null;
  MappingModelList: any[];
}

export interface DynamicMappingDetailsResponse {
  MappingId: number;
  MappingName: string;
  WorkareaEntityTypeId: string;
  FirstRelatedEntityTypeId: string;
  FirstLinkEntityTypeId: string | null;
  SecondRelatedEntityTypeId: string;
  SecondLinkEntityTypeId: string | null;
  DefaultLanguage: string;
  MappingModelList: DynamicMappingFieldResponse[];
}

export interface DynamicMappingFieldResponse {
  inRiverEntityTypeId: string | null;
  inRiverFieldTypeId: string | null;
  inRiverDataType: string | null;
  FormatField: string;
  FormatFieldId: string | null;
  Category: string | null;
  Path: string;
  UnitType: string;
  UnitCvl: string;
  UnitDefaultValue: string;
  Unique: boolean;
  Recommended: boolean;
  DefaultValue: string;
  MaxLength: number | null;
  Description: string;
  Mandatory: boolean;
  ConverterArgs: string | null;
  ConverterClass: string | null;
  ConverterId: number | null;
  FormatDataType: string;
  MinLength: number | null;
  MaxValue: string;
  MinValue: string;
  MaxInstances: number;
  MinInstances: number;
  DecimalFormat: string;
  RegEx: string;
  ConditionalRule: string;
  ChildAttributes: string[];
  Format: string;
  GroupRequiredFields: string;
  Enumerations: FormatEnum[];
  listItemOf?: string;
  // TODO: EnumerationValues can be deleted after the release of the LRJ part of the #110266 work item  (https://dev.azure.com/inriver/iPMC/_git/742e671e-0928-4e07-a6f9-6ddb40d82d80/pullrequest/32723)
  EnumerationValues: string;
  CvlCompleteness: boolean;
}
