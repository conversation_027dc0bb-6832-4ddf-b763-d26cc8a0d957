import { TradingPartnerTabNames } from '@core/enums';
import sessionStorageManager from '@utils/sessionStorageManager';

// Constants
const STORAGE_KEY = 'tradingPartnerPageLastActiveTab';
const LAST_TRADING_PARTNER_KEY = 'tradingPartnerPageLastPartnerId';

/**
 * Helper function to find the dynamic prefix used in session storage
 */
function getSessionStoragePrefix(): string {
  // Look for our keys with any prefix
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key && key.endsWith(STORAGE_KEY)) {
      return key.substring(0, key.length - STORAGE_KEY.length);
    }
    if (key && key.endsWith(LAST_TRADING_PARTNER_KEY)) {
      return key.substring(0, key.length - LAST_TRADING_PARTNER_KEY.length);
    }
  }
  // Default prefix if none found
  return 'inRiverPortal-';
}

// Force both keys to be stored directly
function forceSave(partnerId: string, tabName: string = TradingPartnerTabNames.COLLECTIONS): void {
  // First with sessionStorageManager
  sessionStorageManager.setItem(STORAGE_KEY, tabName);
  sessionStorageManager.setItem(LAST_TRADING_PARTNER_KEY, partnerId);

  // Also directly in sessionStorage with dynamic prefix
  const prefix = getSessionStoragePrefix();
  sessionStorage.setItem(`${prefix}${STORAGE_KEY}`, tabName);
  sessionStorage.setItem(`${prefix}${LAST_TRADING_PARTNER_KEY}`, partnerId);
}

/**
 * Get the initial active tab for the trading partner page
 * Always start from Collections tab if this is a different trading partner
 * @param tradingPartnerId The ID of the trading partner
 * @returns The tab name to set as active
 */
function getInitialActiveTab(tradingPartnerId: string): TradingPartnerTabNames {
  // Get the standard values through the manager (for test compatibility)
  const lastValue = sessionStorageManager.getItemValue(STORAGE_KEY);
  const lastTradingPartnerId = sessionStorageManager.getItemValue(LAST_TRADING_PARTNER_KEY);

  // If we're switching to a different trading partner, always return Collections tab
  if (lastTradingPartnerId !== tradingPartnerId) {
    // Update both storage mechanisms with the new partner and Collections tab
    forceSave(tradingPartnerId, TradingPartnerTabNames.COLLECTIONS);
    return TradingPartnerTabNames.COLLECTIONS;
  }

  // Otherwise, use the saved tab if available
  if (lastValue) {
    return lastValue as TradingPartnerTabNames;
  }

  return TradingPartnerTabNames.COLLECTIONS; // Default tab
}

/**
 * Save the last active tab for trading partner pages
 * @param tradingPartnerId The ID of the trading partner
 * @param tab The tab name that was active
 */
function setLastActiveTab(tradingPartnerId: string, tab: string): void {
  // Save to both storage mechanisms
  forceSave(tradingPartnerId, tab);
}

export { getInitialActiveTab, setLastActiveTab };
