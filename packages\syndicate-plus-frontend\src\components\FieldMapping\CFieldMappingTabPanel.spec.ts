import { describe, it, vi, beforeEach, expect, Mock } from 'vitest';
import { mount, shallowMount } from '@vue/test-utils';
import CFieldMappingTabPanel from '@components/FieldMapping/CFieldMappingTabPanel.vue';
import { useRouter } from '@composables/useRouter';
import templateService from '@services/TemplateService';
import { Field } from '@customTypes/field';
import { useRoute } from 'vue-router';
import CSection from '@inriver/inri/src/components/CSection.vue';
import { createPinia, setActivePinia } from 'pinia';
import SubcatalogService from '@services/SubcatalogService';
import suggestionService from '@services/SuggestionService';
import { ImportanceType } from '@enums/ImportanceType';
import { TemplateMappingEntry } from '@customTypes';
import { DataFlowService } from '@services/DataFlow';
import { fetchInboundTemplateIds } from '@services/DataFlow/InboundTemplateService';

vi.mock('@composables/useRouter');
vi.mock('@services/SuggestionService');
vi.mock('@services/TemplateService');
vi.mock('@services/SubcatalogService');
vi.mock('@services/DataFlow/DataFlowService');
vi.mock('@services/DataFlow/InboundTemplateService');
vi.mock('vue-router');

describe('FieldMappingTabPanel', () => {
  let mockRouter;
  let mockRoute;
  const useRouterNew = useRouter as Mock;
  const useRouteMock = useRoute as Mock;
  let fields: TemplateMappingEntry[] = [];

  const product: Field = {
    name: 'Mock Name',
    disabled: false,
    id: 'mock-id-123',
    syndicationType: 'syndicationType',
  };

  fields = [
    new TemplateMappingEntry(
      'sourceField',
      'targetField',
      ImportanceType.REQUIRED,
      'Sheet1',
      {
        name: 'sourceField',
        disabled: false,
        id: '12345',
        syndicationType: '',
      },
      {
        name: 'targetField',
        disabled: false,
        id: '12345',
        syndicationType: '',
      },
      ['function1', 'function2'],
      'default',
      'string',
      'format',
      '1',
      '255',
      false,
      'COLUMNNAME:NOCOLUMN',
      true
    ),
    new TemplateMappingEntry(
      'sourceField',
      'targetField',
      ImportanceType.RECOMMENDED,
      'Sheet1',
      {
        name: 'sourceField',
        disabled: false,
        id: '12345',
        syndicationType: '',
      },
      {
        name: 'targetField',
        disabled: false,
        id: '12345',
        syndicationType: '',
      },
      ['function1', 'function2'],
      'default',
      'string',
      'format',
      '1',
      '255',
      false,
      'COLUMNNAME:NOCOLUMN',
      true
    ),
  ];
  beforeEach(() => {
    mockRouter = {
      goToPage: vi.fn(),
    };
    useRouterNew.mockReturnValue(mockRouter);
    mockRoute = {
      params: [{ subCatalogId: '1' }, { destinationId: '1' }],
    };
    useRouteMock.mockReturnValue(mockRoute);

    // @ts-expect-error
    SubcatalogService.getById.mockResolvedValue({ name: 'name' });

    // @ts-expect-error
    templateService.getTemplateFields.mockResolvedValue(fields);

    (templateService.getAllTemplatesDefinitions as Mock).mockResolvedValue([]);

    // @ts-expect-error
    suggestionService.get.mockResolvedValue([product]);

    (DataFlowService.getDataFlowTemplateIds as Mock).mockResolvedValue([1, 2, 3]);
    (fetchInboundTemplateIds as Mock).mockResolvedValue([1, 2, 3]);

    setActivePinia(createPinia());
  });

  it('checks component properties', async () => {
    // Arrange
    const CFieldMappingTabPanelProps = {
      tradingPartnerName: 'test trading partner name',
    };

    // Act
    const wrapper = shallowMount(CFieldMappingTabPanel, {
      props: CFieldMappingTabPanelProps,
      global: {
        mocks: {
          // Here the solution
          $t: (msg: string) => msg,
        },
      },
    });

    // Assert
    expect(wrapper.props().tradingPartnerName).toMatch('test trading partner name');
  });

  it('checks that c-section exists', async () => {
    const wrapper = mount(CFieldMappingTabPanel, {
      global: {
        components: { 'c-section': CSection },
        stubs: { 'c-no-data': true },
        mocks: {
          // Here the solution
          $t: (msg: string) => msg,
        },
      },
    });
    expect(wrapper.find('.c-inri-section').exists()).toBe(true);
  });
});
