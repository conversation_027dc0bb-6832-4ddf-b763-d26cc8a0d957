<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <div class="flex flex-col flex-nowrap justify-center m-10px">
      <q-input
        v-model="module.data.headline1"
        v-bind="$inri.input"
        hide-bottom-space
        :label="$t('syndicate_plus.aplus_define_template.headline')"
        :maxlength="Validation.text.headline1.maxLength"
        counter
        @keyup.enter="updateModel"
        @blur="updateModel"
        @drop="onDrop(module.data.headline1, 'headline1')"
      />
      <c-text-editor
        v-model="module.data.body1"
        :max-length="Validation.text.body1.maxLength"
        @on-field-drop="onDrop(module.data.body1, 'body1')"
        @update:model-value="updateModel"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount, onUpdated, ref } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { ContentModule, TextData } from '@customTypes/Aplus';
import { CTextEditor } from '@components/TemplateBuilder/Blocks';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Refs
const module = ref<ContentModule<TextData>>({} as ContentModule<TextData>);

// Functions
const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, moduleParameter) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data[moduleParameter] = result;
    updateModel();
  }
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  if (!Object.keys(module.value.data)?.length) {
    module.value.data = {
      headline1: '',
      body1: '',
    } as TextData;
  }
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
