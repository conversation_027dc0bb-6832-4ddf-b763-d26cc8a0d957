import { expect, it, describe } from 'vitest';
import { NameOverrideSplittingService } from './NameOverrideSplittingService';

describe('NameOverrideSplittingService', () => {
  it('should split string into several components', () => {
    // Arrange
    const stringToSplit = '${SKU}_${COLOR}${INDEX_TAG}${EXT}_';
    const expected = ['${SKU}', '_', '${COLOR}', '${INDEX_TAG}', '${EXT}', '_'];

    // Act
    const result = NameOverrideSplittingService.Split(stringToSplit);

    // Assert
    expect(result).toStrictEqual(expected);
  });

  it('should remove ${} from elements of the array', () => {
    // Arrange
    const stringsToRemoveMarking = ['${SKU}', '_', '${COLOR}', '${INDEX_TAG}', '${EXT}', '_', '${EX{TE}}'];
    const expected = ['SKU', '_', 'COLOR', 'INDEX_TAG', 'EXT', '_', 'EX{TE}'];

    // Act
    const result = NameOverrideSplittingService.RemoveMarkings(stringsToRemoveMarking);

    // Assert
    expect(result).toStrictEqual(expected);
  });
});
