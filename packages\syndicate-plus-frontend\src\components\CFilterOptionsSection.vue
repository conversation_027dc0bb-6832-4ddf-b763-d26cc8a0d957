<template>
  <div class="flex flex-col">
    <q-checkbox
      v-for="option in options"
      v-bind="$inri.checkbox"
      :key="option.id"
      v-model="localSelectedItems"
      :label="option.name"
      :val="option.id"
      @click="applyFilter"
    />
  </div>
</template>

<script setup lang="ts">
import { Field } from '@customTypes';
import { watchEffect, ref } from 'vue';

const localSelectedItems = ref<Field[]>([]);

const emit = defineEmits(['apply-filter']);
const props = defineProps({
  options: {
    type: Array as () => Array<Field>,
    required: true,
  },
  selectedItems: {
    type: Array,
    required: false,
    default: () => [],
  },
});

watchEffect(() => {
  localSelectedItems.value = [...props.selectedItems] as Field[];
});

function applyFilter() {
  emit('apply-filter', localSelectedItems.value);
}
</script>

<style scoped></style>
