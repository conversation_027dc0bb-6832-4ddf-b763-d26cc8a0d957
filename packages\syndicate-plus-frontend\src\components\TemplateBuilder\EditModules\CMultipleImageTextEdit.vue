<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <div class="flex flex-col flex-nowrap m-10px">
      <div class="module-block text-wrap-word-break m-10px flex flex-row flex-nowrap justify-center h-380px">
        <c-caption-angle-image-block
          v-model="module.data.image1"
          class="main-image-container"
          :dimensions="dimensionsMainImage"
          :caption-max-length="Validation.multipleImagesAndText.caption.maxLength"
          :max-alt-text-length="Validation.multipleImagesAndText.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url, 1)"
        />
        <div class="text-container ml-20px">
          <q-input
            v-model="module.data.headline1"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.headline')"
            :maxlength="Validation.multipleImagesAndText.headline1.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data.headline1, 'headline1')"
          />
          <c-text-editor
            v-model="module.data.body1"
            :max-length="Validation.multipleImagesAndText.body1.maxLength"
            @on-field-drop="onDrop(module.data.body1, 'body1')"
          />
        </div>
      </div>
      <div class="flex flex-row flex-nowrap justify-center">
        <div v-for="image in numberOfSmallImages" :key="image" class="image-container">
          <c-caption-angle-image-block
            v-model="module.data[`image${image}`]"
            :dimensions="dimensionsForSmallImages"
            :caption-max-length="Validation.multipleImagesAndText.caption.maxLength"
            @update-model="updateModel"
            @url-updated="(url) => handleUrlUpdate(url, image)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onUpdated } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { CTextEditor, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { useMultipleImageText } from '@composables/AplusTemplateBuilder';
import { Dimension } from '@customTypes/Aplus';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Variables
const numberOfSmallImages = 4;
const dimensionsMainImage = { width: 300, height: 300 } as Dimension;
const dimensionsForSmallImages = { width: 200, height: 200 } as Dimension;

// Composables
const { module, initData } = useMultipleImageText();

// Functions
const handleUrlUpdate = (url: string, orderIndex: number) => {
  module.value.data[`image${orderIndex}`].angle = url;
  updateModel();
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const onDrop = (value: string, key: string) => {
  module.value.data[key] = fieldsStore.onDrop(value);
  updateModel();
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  initData();
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>
<style lang="scss" scoped>
.module-block {
  overflow: auto;
}

.main-image-container {
  :deep(.q-uploader__list) {
    overflow: hidden;
    padding: 0px;
  }
}

.text-container {
  width: 500px;
}

.image-container {
  width: 234px;
}

.image-container {
  :deep(.q-field.q-field--outlined.q-input) {
    width: 180px;
  }

  :deep(.c-inri-input.c-inri-input--default:not(.q-textarea) .q-field__control) {
    width: 180px;
  }

  :deep(.q-field__inner .q-field__bottom) {
    margin-right: 0px;
  }
}
</style>
