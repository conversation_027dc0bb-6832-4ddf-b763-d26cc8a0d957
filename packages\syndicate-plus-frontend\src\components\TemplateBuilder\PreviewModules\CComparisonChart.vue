<template>
  <div class="mx-auto p-20px min-w-1384px w-1384px comparison-chart-preview">
    <div class="flex flex-row flex-nowrap justify-end">
      <div
        v-for="(_, orderIndex) in numberOfBlocks"
        :key="orderIndex"
        :class="getImageClass(orderIndex + 1)"
        class="p-10px m-5px"
        :data-testid="`${orderIndex}-comparison-chart-block`"
      >
        <c-caption-angle-image-block-preview
          v-if="module.data[`image${orderIndex + 1}`]"
          :image-data="module.data[`image${orderIndex + 1}`]"
          image-data-class="h-300px w-150px"
        />
        <div class="comparison-chart-block-title text-wrap-word-break">{{ module.data[`title${orderIndex + 1}`] }}</div>
        <div>{{ module.data[`asin${orderIndex + 1}`] }}</div>
      </div>
    </div>
    <div class="flex flex-row flex-nowrap justify-end">
      <q-table
        row-key="name"
        separator="none"
        dense
        flat
        no-header
        :columns="buildColumns()"
        :rows="module.data.metrics.metrics"
        hide-header
        hide-bottom
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, ComparisonChartData, MetricData } from '@customTypes/Aplus';
import { CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Variables
const numberOfBlocks = 6;

// Computed
const module = computed<ContentModule<ComparisonChartData>>(() => {
  return store.getModuleByIndex(props.index);
});

// Functions
const buildColumns = () => {
  if (!module.value.data?.metrics?.metrics) {
    return;
  }
  const fields: (keyof MetricData)[] = ['metric', 'text1', 'text2', 'text3', 'text4', 'text5', 'text6'];

  return fields.map((field) => ({
    name: field,
    label: field,
    field,
    align: 'left' as const,
  }));
};

const getImageClass = (orderIndex: number): string => {
  if (module.value.data[`highlighted${orderIndex}`].checked) {
    return 'highlighted';
  }

  return '';
};
</script>

<style lang="scss" scoped>
.comparison-chart-preview {
  .highlighted {
    border: 0.95px solid var(--color-black);
    transform: scale(1);
    box-shadow: 0px 4px 8px rgba(1, 1, 1, 0.75);
  }

  .headline {
    font-size: 14px;
    font-weight: bold;
  }

  :deep(td:first-child) {
    background-color: var(--color-grey-lighter);
    font-weight: bold;
  }

  :deep(td) {
    width: 180px;
    word-break: break-word;
    text-wrap: wrap;
    overflow-wrap: anywhere;
  }

  .comparison-chart-block-title {
    width: 180px;
  }
}
</style>
