<template>
  <q-uploader
    class="image-uploader gray-area"
    :factory="handleUploadFromUploader"
    url="#"
    accept=".jpg, image/*"
    auto-upload
  >
    <template #list>
      <q-file
        v-model="file"
        class="file-picker cursor-pointer"
        input-class="filepicker-input"
        accept=".jpg, image/*"
        borderless
        :loading="isLoading"
        hide-bottom-space
        @update:model-value="handleUploadFromFilepicker(file)"
      >
        <template #default>
          <slot class="q-file-slot" />
        </template>
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
      </q-file>
    </template>
  </q-uploader>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';
import { ImageHttpService } from '@httpservices';
import { useI18n } from 'vue-i18n';
import { notify } from '@inriver/inri';

// DefineEmits
const emit = defineEmits(['url-updated']);

// Composables
const { t } = useI18n();

// Refs
const file = ref<any>(null);
const isLoading = ref(false);

// Computed
const notifyErrorMessage = computed(() => {
  return `${t('syndicate_plus.aplus_define_template.notifications.image_upload_error')}`;
});

// Functions
const handleUploadFromFilepicker = (file) => {
  handleUpload(file);
};

const handleUploadFromUploader = (files) => {
  handleUpload(files[0]);
};

const handleUpload = async (file) => {
  const validatedFile = new File([file], file.name.replace(/\s/g, ''), { type: file.type });
  isLoading.value = true;
  try {
    const statusId = await ImageHttpService.getStatusId();
    await ImageHttpService.uploadFileById(validatedFile, statusId);
    const imageMetadata = await ImageHttpService.getImageMetadata(validatedFile.name);
    if (!imageMetadata) {
      isLoading.value = false;
      notify.error(notifyErrorMessage);
      return;
    }

    const uriPathPublic = await ImageHttpService.publishImage(imageMetadata.uriId);
    if (!uriPathPublic) {
      isLoading.value = false;
      notify.error(notifyErrorMessage);
      return;
    }

    emit('url-updated', uriPathPublic);
  } catch (error) {
    isLoading.value = false;
    notify.error(notifyErrorMessage);
  }
  isLoading.value = false;
};
</script>
<style lang="scss" scoped>
.image-uploader {
  &.gray-area {
    background-color: var(--color-grey-10);
  }

  box-shadow: none;

  :deep(.q-file .angle-input .q-field__input) {
    opacity: 1 !important;
  }

  :deep(.q-uploader__header) {
    display: none;
  }

  &.q-uploader--dnd {
    background-color: var(--color-green-10);
    border: 1px dashed var(--color-green);
    .image-block {
      display: none;
    }
  }

  .file-picker {
    height: 100%;
  }

  :deep(.filepicker-input) {
    display: none;
  }

  :deep(.q-uploader__dnd) {
    outline: none;
  }

  :deep(.q-file__dnd) {
    outline: none;
    background-color: var(--color-green-10);
    opacity: 0.5;
    border: 1px dashed var(--color-green);

    .image-block {
      display: none;
    }
  }

  :deep(.q-uploader__list) {
    overflow: hidden;
    padding: 0px;
  }

  :deep(.q-field__control) {
    height: 100%;
  }

  :deep(.q-field__control-container) {
    flex-direction: column;
    justify-content: center;
    display: flex;
    align-items: center;
  }

  :deep(.q-field__append) {
    padding-left: 0px;
  }

  :deep(.q-field__native) {
    min-height: 0px;
  }
}
</style>
