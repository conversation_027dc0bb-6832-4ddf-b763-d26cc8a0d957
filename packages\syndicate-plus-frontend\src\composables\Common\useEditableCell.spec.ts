import { it, describe, expect, beforeEach, vi } from 'vitest';
import { ref } from 'vue';
import { useEditableCell } from '@composables/Common';
import { ColumnDataType } from '@enums';

describe('useEditableCell', () => {
  describe('displayValue', () => {
    let languageGetter;

    beforeEach(() => {
      languageGetter = vi.spyOn(window.navigator, 'language', 'get');
    });

    it('shows the value correctly for an empty value', async () => {
      // Arrange
      const testCases = [
        { value: '', dataType: ColumnDataType.CURRENCY, isInputFocused: false, expectedValue: '-' },
        { value: '', dataType: ColumnDataType.DATE, isInputFocused: false, expectedValue: '-' },
        { value: '', dataType: ColumnDataType.STRING, isInputFocused: false, expectedValue: '-' },
        { value: null, dataType: ColumnDataType.STRING, isInputFocused: false, expectedValue: '-' },
        { value: '', dataType: ColumnDataType.CURRENCY, isInputFocused: true, expectedValue: '' },
        { value: '', dataType: ColumnDataType.DATE, isInputFocused: true, expectedValue: '' },
        { value: '', dataType: ColumnDataType.STRING, isInputFocused: true, expectedValue: '' },
      ];

      testCases.forEach(({ value, dataType, isInputFocused, expectedValue }) => {
        // Act
        const { displayValue } = useEditableCell(ref(value), ref(isInputFocused), dataType);

        // Assert
        expect(displayValue.value).toBe(expectedValue);
      });
    });

    it('shows the value correctly for different types', async () => {
      // Arrange
      const isInputFocused = ref(true);
      languageGetter.mockReturnValue('en-US');
      const testCases = [
        { value: 'ab', dataType: ColumnDataType.STRING, expectedValue: 'ab' },
        { value: '123a', dataType: ColumnDataType.STRING, expectedValue: '123a' },
        { value: '1709648102', dataType: ColumnDataType.STRING, expectedValue: '1709648102' },
        { value: '1557878400000', dataType: ColumnDataType.DATE, expectedValue: '05/15/2019' },
        { value: '1704067200000', dataType: ColumnDataType.DATE, expectedValue: '01/01/2024' },
        { value: 'a', dataType: ColumnDataType.DATE, expectedValue: 'Invalid Date' },
      ];

      testCases.forEach(({ value, dataType, expectedValue }) => {
        // Act
        const { displayValue } = useEditableCell(ref(value), isInputFocused, dataType);

        // Assert
        expect(displayValue.value).toBe(expectedValue);
      });
    });

    it('shows the value correctly for currency type', async () => {
      // Arrange
      languageGetter.mockReturnValue('en-US');
      const testCases = [
        { language: 'en-US', value: '12', isInputFocused: false, expectedValue: '12.00' },
        { language: 'en-US', value: '123.12', isInputFocused: false, expectedValue: '123.12' },
        { language: 'en-IN', value: '123456789.12', isInputFocused: false, expectedValue: '12,34,56,789.12' },
        { language: 'sw', value: '123456789.12', isInputFocused: false, expectedValue: '123,456,789.12' },
        { language: 'sw', value: '123456789.12', isInputFocused: true, expectedValue: '123456789.12' },
        { language: 'de', value: '123456789.12', isInputFocused: false, expectedValue: '123.456.789,12' },
        { language: 'de', value: '123456789.12', isInputFocused: true, expectedValue: '123456789.12' },
      ];

      testCases.forEach(({ language, value, isInputFocused, expectedValue }) => {
        languageGetter.mockReturnValue(language);

        // Act
        const { displayValue } = useEditableCell(ref(value), ref(isInputFocused), ColumnDataType.CURRENCY);

        // Assert
        expect(displayValue.value).toBe(expectedValue);
      });
    });

    it('formats the date depending on the user language', async () => {
      // Arrange
      const isInputFocused = ref(true);
      const testCases = [
        { language: 'en-US', value: '1709648102', expectedValue: '01/20/1970' },
        { language: 'en', value: '1709648102', expectedValue: '01/20/1970' },
        { language: 'ce-RU', value: '1709648102', expectedValue: '1970-01-20' },
        { language: 'sw', value: '1709648102', expectedValue: '20/01/1970' },
      ];

      testCases.forEach(({ language, value, expectedValue }) => {
        languageGetter.mockReturnValue(language);

        // Act
        const { displayValue } = useEditableCell(ref(value), isInputFocused, ColumnDataType.DATE);

        // Assert
        expect(displayValue.value).toBe(expectedValue);
      });
    });
  });

  describe('setValue', () => {
    it('sets the value for different types', async () => {
      // Arrange
      const initialValue = ref('');
      const isInputFocused = ref(true);
      const testCases = [
        { value: '123', dataType: ColumnDataType.STRING, expectedValue: '123' },
        { value: 'abc', dataType: ColumnDataType.STRING, expectedValue: 'abc' },
        { value: '123', dataType: ColumnDataType.CURRENCY, expectedValue: '123' },
        { value: 'abc', dataType: ColumnDataType.CURRENCY, expectedValue: 'abc' },
        { value: '2019/05/15', dataType: ColumnDataType.DATE, expectedValue: '1557878400000' },
        { value: '2024/01/01', dataType: ColumnDataType.DATE, expectedValue: '1704067200000' },
        { value: 'abc', dataType: ColumnDataType.DATE, expectedValue: '' },
      ];

      testCases.forEach(({ value, dataType, expectedValue }) => {
        const { setValue } = useEditableCell(initialValue, isInputFocused, dataType);

        // Act
        setValue(value);

        // Assert
        expect(initialValue.value).toBe(expectedValue);
      });
    });
  });

  describe('validateValue', () => {
    it('validates the input value correctly for the currency type', async () => {
      // Arrange
      const testCases = [
        { value: '123', key: '1', selectionStart: 1, selectionEnd: 1, allowInput: true },
        { value: '123', key: 'f', selectionStart: 1, selectionEnd: 1, allowInput: false },
        { value: '123.1', key: '1', selectionStart: 1, selectionEnd: 1, allowInput: true },
        { value: '123.1', key: '.', selectionStart: 4, selectionEnd: 4, allowInput: false },
        { value: '123.1', key: '.', selectionStart: 1, selectionEnd: 1, allowInput: false },
        { value: '123.12', key: '1', selectionStart: 1, selectionEnd: 1, allowInput: true },
        { value: '123.12', key: '1', selectionStart: 4, selectionEnd: 4, allowInput: false },
        { value: '123.12', key: '1', selectionStart: 0, selectionEnd: 4, allowInput: true },
        { value: '123.12', key: 'Delete', selectionStart: 1, selectionEnd: 1, allowInput: true },
        { value: '123.12', key: 'Backspace', selectionStart: 1, selectionEnd: 1, allowInput: true },
        { value: '123.12', key: 'ArrowLeft', selectionStart: 1, selectionEnd: 1, allowInput: true },
        { value: '123.12', key: 'ArrowRight', selectionStart: 1, selectionEnd: 1, allowInput: true },
      ];

      testCases.forEach(({ value, key, selectionStart, selectionEnd, allowInput }) => {
        const { validateValue } = useEditableCell(ref(value), ref(true), ColumnDataType.CURRENCY);
        const event = {
          key,
          target: {
            value,
            selectionStart,
            selectionEnd,
          },
          preventDefault: vi.fn(),
        };

        // Act
        validateValue(event);

        // Assert
        allowInput
          ? expect(event.preventDefault).not.toHaveBeenCalledOnce()
          : expect(event.preventDefault).toHaveBeenCalledOnce();
      });
    });

    it('does not allow to paste (ctrl+v) a value for the currency type', async () => {
      // Arrange
      const { validateValue } = useEditableCell(ref('123'), ref(true), ColumnDataType.CURRENCY);
      const event = {
        key: 'v',
        ctrlKey: true,
        target: { value: '', selectionStart: 0, selectionEnd: 0 },
        preventDefault: vi.fn(),
      };

      // Act
      validateValue(event);

      // Assert
      expect(event.preventDefault).toHaveBeenCalledOnce();
    });

    it('does not allow to paste (context menu) a value for the currency type', async () => {
      // Arrange
      const { validateValue } = useEditableCell(ref('123'), ref(true), ColumnDataType.CURRENCY);
      const event = {
        type: 'paste',
        target: { value: '', selectionStart: 0, selectionEnd: 0 },
        preventDefault: vi.fn(),
      };

      // Act
      validateValue(event);

      // Assert
      expect(event.preventDefault).toHaveBeenCalledOnce();
    });
  });
});
