# Action groups
Name: syndicateplus-euw-devqa-team-email
Subscription: "1040-inriver-boston-devqa"
Resource group: "syndicateplus-euw-devqa"
Predicted cost: "1000 emails free ,then few dollars for 100k"
Navigate: Monitor -> Alerts -> Action groups

Created Action group for dev-qa, it sends email to "<EMAIL>"

# Alert processing rule, 
Couldn't be created becouse it would need to work on the scope of app insights, and created in ipmc subscription

# Action rule 
Name: syndicate-plus-frontend-euw-dev4a-missing-org-mapping
Subscription: "1040-inriver-boston-devqa"
Resource group: "syndicate-plus-frontend-euw-dev4a"
Predicted cost - 0,5$ monthly
Navigate: Monitor -> Alerts -> Alert rules

Scope of the alert is:
Subscription: "1040-inriver-pmc2-devqa"
Resource group: "pmc2-euw-dev4a-rg-mgmt"
App Insigths: "pmc2-euw-dev4a-appinsights-00"

Triggers when custom event "SyndicatePlusOrgMappingMissing" is recorded, it works in 15 min window. 

In details, advanced settings selected option [Mute actions] for 1 day, which means email will be triggered once, and then it won't be for 24h.
# Query to view distinct environment and orgId in 24h time range

customEvents
| where name == "SyndicatePlusOrgMappingMissing"
| where cloud_RoleName == "Syndicate Plus"
| extend customDimensionsParsed = parse_json(customDimensions)
| extend environment = tostring(customDimensionsParsed.environment), defaultSyndic8OrgId = tostring(customDimensionsParsed.defaultSyndic8OrgId)
| summarize maxTimestamp = max(timestamp) by environment, defaultSyndic8OrgId
| project environment, defaultSyndic8OrgId, maxTimestamp
| order by maxTimestamp desc 
