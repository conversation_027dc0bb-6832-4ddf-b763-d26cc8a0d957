<template>
  <div class="flex flex-col">
    <q-radio
      v-for="option in options"
      :key="option.id"
      v-model="localSelectedItem"
      :label="option.label"
      :val="option.id"
      size="xs"
      @click="applyFilter"
    />
  </div>
</template>

<script setup lang="ts">
import { FilterFieldOption, EditViewFilterFieldOption, MandatoryFieldsFilterOption } from '@customTypes';
import { onBeforeMount } from 'vue';

let localSelectedItem: string;

const emit = defineEmits(['apply-filter']);
const props = defineProps({
  options: {
    type: Array as () => Array<FilterFieldOption | EditViewFilterFieldOption | MandatoryFieldsFilterOption>,
    required: true,
  },
  selectedItem: {
    type: Object as () => FilterFieldOption | EditViewFilterFieldOption | MandatoryFieldsFilterOption,
    required: true,
  },
});

onBeforeMount(() => {
  localSelectedItem = props.selectedItem.id;
});

function applyFilter() {
  const selectedOption = props.options.find((o) => o.id == localSelectedItem);

  emit('apply-filter', selectedOption);
}
</script>

<style scoped></style>
