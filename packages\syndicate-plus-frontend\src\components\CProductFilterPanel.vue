<template>
  <div class="filter-panel">
    <q-tabs
      v-model="tab"
      class="bg-white text-grey-7"
      active-color="primary"
      active-class="active-tab"
      indicator-color="transparent"
      align="justify"
      no-caps
    >
      <q-tab :name="FilterTabNames.FILTERS_TAB" :label="$t('syndicate_plus.common.filter.filters')" />
      <q-tab :name="FilterTabNames.VIEW_TAB" :label="$t('syndicate_plus.common.view.views')" />
    </q-tabs>
    <q-tab-panels v-model="tab">
      <q-tab-panel :name="FilterTabNames.FILTERS_TAB" class="panel-content p-0">
        <div class="flex flex-col filter-buttons p-4">
          <a class="clear-btn" @click="clearAllFilters">
            {{ $t('syndicate_plus.common.filter.clear_all_filters') }}
          </a>
          <a class="minimize-btn" @click="toggleExpandOrMinimizeAll">
            {{
              allMinimized
                ? $t('syndicate_plus.common.filter.expand_all')
                : $t('syndicate_plus.common.filter.minimize_all')
            }}
          </a>
        </div>

        <q-separator />

        <q-inner-loading :showing="loading" color="primary" class="z-10 inner-loading">
          <c-spinner data-testid="products-filter-spinner" size="40" class="spinner" />
        </q-inner-loading>
        <c-separated-expansion-item
          :is-expanded="expanded.syndicationStatus"
          :label="$t('syndicate_plus.syndication.filter.syndication_status')"
        >
          <div class="flex flex-col">
            <q-checkbox
              v-bind="$inri.checkbox"
              v-model="showOnlyProductsUpdatedSinceLastSyndicationRef"
              :label="$t('syndicate_plus.syndication.filter.updated_since_last_syndication')"
              @click="applyFilterSelections"
            />
          </div>
        </c-separated-expansion-item>

        <c-separated-expansion-item
          :is-expanded="expanded.brands"
          :label="$t('syndicate_plus.syndication.filter.brand')"
        >
          <c-filter-options-section
            ref="brandsRef"
            :options="filterOptions?.brands"
            :selected-items="selectedBrandItems"
            @apply-filter="applyBrandsFilter"
          />
        </c-separated-expansion-item>

        <c-separated-expansion-item
          v-if="tradingPartnerCollections && tradingPartnerCollections.length > 0"
          :is-expanded="expanded.collections"
          :label="$t('syndicate_plus.syndication.filter.collection')"
        >
          <c-filter-options-section
            ref="collectionsRef"
            :options="tradingPartnerCollections"
            :selected-items="selectedCollections"
            @apply-filter="applyCollectionsFilter"
          />
        </c-separated-expansion-item>

        <c-separated-expansion-item
          v-if="showFilterOnProductTypes"
          :is-expanded="expanded.productTypes"
          :label="$t('syndicate_plus.syndication.filter.product_types')"
        >
          <c-filter-options-section
            ref="productTypesRef"
            :options="filterOptions?.productTypes"
            :selected-items="selectedProductTypes"
            @apply-filter="applyProductTypesFilter"
          />
        </c-separated-expansion-item>
      </q-tab-panel>
      <q-tab-panel :name="FilterTabNames.VIEW_TAB" class="panel-content p-0">
        <c-separated-expansion-item
          :is-expanded="expandedProductTypes"
          :label="$t('syndicate_plus.syndication.view.product_presentation')"
        >
          <c-filter-radio-options-section
            :options="filterOptions?.groupByOptions"
            :selected-item="selectedGroupByViewRef"
            @apply-filter="applyGroupByFilter"
          />
        </c-separated-expansion-item>
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, onUnmounted, computed, ComputedRef, reactive } from 'vue';
import { storeToRefs } from 'pinia';
import { Collection, ProductFilter, FilterFieldOption } from '@customTypes';
import { useProductsStore, useTradingPartnersStore } from '@stores';
import { ProductFilterDto } from '@dtos';
import { useRoute } from 'vue-router';
import { CFilterOptionsSection, CFilterRadioOptionsSection } from '@components';
import { CSeparatedExpansionItem } from '@components/Shared';
import deepEqual from '@utils/deepEqual';
import { getDefaultGroupByOption } from '@const/groupByOptions';
import { FilterTabNames } from '@enums';
import { useEmitter } from '@composables';

const productsStore = useProductsStore();
const {
  fetchFilterOptions,
  applyFilter,
  getFilter,
  getFilterOptions,
  getAreFiltersInitialized,
  markFiltersAsInitialized,
} = productsStore;

// We currently don't show this because the list of product types is very long.
// We want an API endpoint so that we can get only the relevant product types, but we don't have that yet.
const showFilterOnProductTypes = false;
const route = useRoute();

// Refs
const tab = ref(FilterTabNames.FILTERS_TAB);
const brandsRef = ref(null);
const collectionsRef = ref(null);
const productTypesRef = ref(null);
const selectedGroupByViewRef = ref<FilterFieldOption>(
  getFilter().groupBy !== undefined ? getFilter().groupBy : getDefaultGroupByOption()
);
const { loading } = storeToRefs(productsStore);
const expanded = reactive({
  syndicationStatus: true,
  selectedGroupByViewRef: true,
  brands: true,
  collections: true,
  productTypes: true,
});

const expandedProductTypes = ref(true);

const selectedBrandItems = ref<string[]>([]);
const selectedCollections = ref<string[]>([]);
const selectedProductTypes = ref<string[]>([]);
const showOnlyProductsUpdatedSinceLastSyndicationRef = ref(false);
const { getTradingPartnerById } = useTradingPartnersStore();
const tradingPartnerName = ref('');

onBeforeMount(async () => {
  const { subCatalogId, destinationId } = route.params;
  if (!getAreFiltersInitialized()) {
    await fetchFilterOptions(subCatalogId, destinationId);
    markFiltersAsInitialized();
  } else {
    markFiltersAsAppliedFromStore();
  }
  const tradingPartner = await getTradingPartnerById(parseInt(destinationId as string));
  tradingPartnerName.value = tradingPartner?.name;
});

// Computed fields
const filterOptions: ComputedRef<ProductFilter> = computed(() => {
  const { subCatalogId } = route.params;
  return getFilterOptions(subCatalogId);
});

const tradingPartnerCollections: ComputedRef<Collection[] | undefined> = computed(() => {
  const requiredPrefix = `${tradingPartnerName.value} -`;
  const filters = filterOptions?.value?.collections
    ?.filter((c) => c.name.startsWith(requiredPrefix))
    .map((c) => ({
      id: c.id,
      disabled: c.disabled,
      name: c.name.replace(requiredPrefix, '').trim(),
    }));

  return filters;
});

const allMinimized: ComputedRef<Boolean> = computed(() => {
  return !expanded.collections && !expanded.brands;
});

// Composables
const emitter = useEmitter();

// Functions
function applyBrandsFilter(selectedItems: any[]): void {
  selectedBrandItems.value = selectedItems;
  applyFilterSelections();
}

function applyCollectionsFilter(selectedItems: any[]): void {
  selectedCollections.value = selectedItems;
  applyFilterSelections();
}

function applyProductTypesFilter(selectedItems: any[]): void {
  selectedProductTypes.value = selectedItems;
  applyFilterSelections();
}

function applyGroupByFilter(selectedItem: FilterFieldOption): void {
  selectedGroupByViewRef.value = selectedItem;
  applyFilterSelections();
}

let lastFilter: ProductFilterDto | undefined;

function applyFilterSelections() {
  const { subCatalogId, destinationId } = route.params;
  const filter = {
    groupBy: selectedGroupByViewRef.value,
    brands: [...selectedBrandItems.value],
    collections: [...selectedCollections.value],
    productTypes: [...selectedProductTypes.value],
    showOnlyUpdatedSinceLastSyndication: showOnlyProductsUpdatedSinceLastSyndicationRef.value,
  } as ProductFilterDto;
  if (!deepEqual(filter, lastFilter)) {
    applyFilter(filter, subCatalogId, destinationId);
    lastFilter = filter;
  }
}

function markFiltersAsAppliedFromStore() {
  const appliedStoreFilters = getFilter() as ProductFilterDto;
  if (appliedStoreFilters.brands) {
    selectedBrandItems.value = appliedStoreFilters.brands;
  }
  if (appliedStoreFilters.collections) {
    selectedCollections.value = appliedStoreFilters.collections;
  }
  if (appliedStoreFilters.productTypes) {
    selectedProductTypes.value = appliedStoreFilters.productTypes;
  }
}

function clearAllFilters(): void {
  const { subCatalogId, destinationId } = route.params;

  applyFilter(new ProductFilterDto(), subCatalogId, destinationId);
  selectedProductTypes.value = [];
  selectedBrandItems.value = [];
  selectedCollections.value = [];

  selectedGroupByViewRef.value = getDefaultGroupByOption();
  brandsRef.value?.reset && brandsRef.value.reset();
  collectionsRef.value?.reset && collectionsRef.value.reset();
  productTypesRef.value?.reset && productTypesRef.value.reset();
  showOnlyProductsUpdatedSinceLastSyndicationRef.value = false;
}

function toggleExpandOrMinimizeAll(): void {
  if (allMinimized.value) {
    expanded.brands = true;
    expanded.collections = true;
    expanded.productTypes = true;
    expanded.syndicationStatus = true;
  } else {
    expanded.brands = false;
    expanded.collections = false;
    expanded.productTypes = false;
    expanded.syndicationStatus = false;
  }
}

// Events
emitter.on('collection-created', async () => await fetchFilterOptions(route.params.subCatalogId, true));
emitter.on('collection-deleted', async () => await fetchFilterOptions(route.params.subCatalogId, true));
emitter.on('collection-renamed', async () => await fetchFilterOptions(route.params.subCatalogId, true));

// Lifecycle methods
onUnmounted(() => {
  emitter.all.delete('collection-created');
  emitter.all.delete('collection-deleted');
  emitter.all.delete('collection-renamed');
});
</script>

<style lang="scss" scoped>
.filter-panel {
  position: absolute;
  width: 400px;
  height: calc(100vh - 52.5px);
  z-index: 1;
  background-color: var(--on-primary-color);
  box-shadow: 2px 0 10px 0 var(--color-border);

  .panel-content {
    height: calc(100vh - 175px);
  }

  .filter-buttons {
    flex-direction: row;
    min-height: 21px;

    .clear-btn,
    .minimize-btn {
      cursor: pointer;
      text-decoration: underline;
    }

    .minimize-btn {
      margin-left: auto;
    }
  }
}
</style>
