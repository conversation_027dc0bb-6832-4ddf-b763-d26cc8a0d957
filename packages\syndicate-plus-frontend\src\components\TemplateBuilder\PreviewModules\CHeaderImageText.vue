<template>
  <div class="mx-auto min-w-1000px w-1000px header-image-text-preview" data-testid="header-image-text">
    <c-caption-angle-image-block-preview
      v-if="module.data.image1"
      :image-data="module.data.image1"
      image-data-class="image"
    />
    <h3 class="font-bold" data-testid="headline1">{{ module.data.headline1 }}</h3>
    <c-html-preview data-testid="body1" :html="module.data.body1" />
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, HeaderImageTextData } from '@customTypes/Aplus';
import { CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<HeaderImageTextData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.header-image-text-preview {
  padding: 10px 20px;

  :deep() {
    .image {
      min-width: 970px;
      width: 970px;
      height: 600px;
      object-fit: cover;
    }
  }
}
</style>
