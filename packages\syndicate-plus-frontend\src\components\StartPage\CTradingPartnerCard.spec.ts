import { describe, it, vi, beforeEach, expect, Mock } from 'vitest';

import { mount } from '@vue/test-utils';

import { TradingPartner } from '@customTypes';

import CTradingPartnerCard from '@components/StartPage/CTradingPartnerCard.vue';
import { useRouter } from '@composables/useRouter';

vi.mock('@composables/useRouter');

describe('TradingPartnerCard', () => {
  let mockRouter;
  const useRouterNew = useRouter as Mock;

  beforeEach(() => {
    mockRouter = {
      goToPage: vi.fn(),
    };
    useRouterNew.mockReturnValue(mockRouter);
  });

  it('navigates to trading partner when clicked', async () => {
    const tradingPartnerProps: TradingPartner = {
      isConnected: true,
      id: 1,
      subCatalogId: 1,
      destinationId: 1,
      name: '123',
      type: 'Marketplace',
    };

    // Arrange
    const wrapper = mount(CTradingPartnerCard, { props: tradingPartnerProps });

    // Act
    await wrapper.find('div').trigger('click');

    // Assert
    expect(mockRouter.goToPage).toHaveBeenCalledWith('syndication-page', { subCatalogId: 1, destinationId: 1 });
  });

  it('shows a green border when trading partner is connected', async () => {
    // Arrange
    const tradingPartnerProps: TradingPartner = {
      isConnected: true,
      id: 1,
      name: '123',
      type: 'Marketplace',
    };

    // Act
    const wrapper = mount(CTradingPartnerCard, { props: tradingPartnerProps });

    // Assert
    expect(await wrapper.find('.c-trading-partner-card').classes()).toContain('connected');
  });

  it('shows a grey border when trading partner is not connected', async () => {
    // Arrange
    const tradingPartnerProps: TradingPartner = {
      isConnected: false,
      id: 1,
      name: '123',
      type: 'Marketplace',
    };

    // Act
    const wrapper = mount(CTradingPartnerCard, { props: tradingPartnerProps });

    // Assert
    expect(await wrapper.find('.c-trading-partner-card').classes()).not.toContain('connected');
  });
});
