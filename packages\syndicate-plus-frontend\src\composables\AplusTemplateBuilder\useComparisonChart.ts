import { ref } from 'vue';
import { ContentModule, ComparisonChartData } from '@customTypes/Aplus';

export default function useComparisonChart() {
  // Refs
  const module = ref<ContentModule<ComparisonChartData>>({} as ContentModule<ComparisonChartData>);

  // Functions
  const initData = () => {
    if (!Object.keys(module.value.data)?.length) {
      module.value.data = {
        asin1: '',
        asin2: '',
        asin3: '',
        asin4: '',
        asin5: '',
        asin6: '',
        highlighted1: {
          checked: false,
          label: 'Highlighted',
        },
        highlighted2: {
          checked: false,
          label: 'Highlighted',
        },
        highlighted3: {
          checked: false,
          label: 'Highlighted',
        },
        highlighted4: {
          checked: false,
          label: 'Highlighted',
        },
        highlighted5: {
          checked: false,
          label: 'Highlighted',
        },
        highlighted6: {
          checked: false,
          label: 'Highlighted',
        },
        image1: {
          altText: '',
          angle: '',
        },
        image2: {
          altText: '',
          angle: '',
        },
        image3: {
          altText: '',
          angle: '',
        },
        image4: {
          altText: '',
          angle: '',
        },
        image5: {
          altText: '',
          angle: '',
        },
        image6: {
          altText: '',
          angle: '',
        },
        title1: '',
        title2: '',
        title3: '',
        title4: '',
        title5: '',
        title6: '',
        metrics: {
          metrics: [],
        },
      } as ComparisonChartData;
    }
  };

  return { module, initData };
}
