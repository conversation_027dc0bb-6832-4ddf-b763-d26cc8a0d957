<template>
  <c-section>
    <template v-if="isPageLoading">
      <div class="row justify-center q-my-md">
        <c-spinner size="40" />
      </div>
    </template>
    <div v-else-if="productOutboxRows?.length && isOutboxTab()" ref="scrollTargetRef" class="scroll scroll-container">
      <q-infinite-scroll :offset="250" :scroll-target="scrollTargetRef" @load="loadMore">
        <q-table
          ref="tableRef"
          v-model:selected="outboxStore.selectedRows"
          data-id="outbox-table"
          dense
          hide-bottom
          :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
          flat
          :columns="outboxColumns"
          class="hide-checkboxes sticky-table-header"
          :class="!productOutboxRows.length && 'empty-table'"
          :rows-per-page-options="[0]"
          :rows="productOutboxRows"
          row-key="id"
          separator="cell"
          :loading="isDataLoading"
          @row-click="onRowClick"
        >
          <template #body-cell-status="scope">
            <q-td :props="scope">
              <c-small-square :color="scope.row.status.squareColor" />
              <span class="ml-2">{{ scope.row.status.outboxStatus }}</span>
            </q-td>
          </template>
          <template #body-cell-actions>
            <c-table-actions>
              <c-icon-btn icon="mdi-arrow-right" small disabled="true" class="bg-surface grey-dark" />
            </c-table-actions>
          </template>
        </q-table>
      </q-infinite-scroll>
    </div>
    <div v-else>
      <c-no-data
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.no_data.outbox.title')"
        :text="$t('syndicate_plus.no_data.outbox.message')"
        :bullet-points="$tm('syndicate_plus.no_data.outbox.bullet_points') as string[]"
      />
    </div>
    <q-inner-loading :showing="isDataLoading && !isPageLoading" color="primary">
      <c-spinner color="primary" size="40" />
    </q-inner-loading>
  </c-section>
</template>

<script setup lang="ts">
import { ref, onMounted, watchEffect, onBeforeMount, toRef, computed } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { ProductOutboxRow } from '@customTypes';
import { CSmallSquare, CNoData } from '@components';
import { useProductOutboxStore } from '@stores';
import { SyndicationPageTabNames } from '@enums';
import { useProductOutboxPoller } from '@composables/Outbox';
import { outboxColumns } from '@const';

const route = useRoute();

// Variables
const destinationId = parseInt(route.params.destinationId as string);

// Refs
const selectedRows = computed<ProductOutboxRow[]>(() => outboxStore.selectedRows);
const emit = defineEmits(['productOutboxRowSelectedEvent']);
const tableRef = ref(null);
const scrollTargetRef = ref();
const isPageLoading = ref(false);

const outboxStore = useProductOutboxStore();
const { loading: isDataLoading, lastPage, productOutboxRows } = storeToRefs(outboxStore);
const { getNextPageOfProductOutboxRows } = outboxStore;

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  tab: {
    type: String,
    default: '',
  },
});

const activeTab = toRef(props, 'tab');
useProductOutboxPoller(activeTab, destinationId);

// Functions
async function loadMore(_, done) {
  setTimeout(async () => {
    if (isDataLoading.value || !isOutboxTab) {
      done(false);
      return;
    }

    try {
      await getNextPageOfProductOutboxRows(destinationId);
    } catch (ex) {
      console.log(ex);
    }

    done(lastPage.value);
  }, 500);
}

const isOutboxTab = (): boolean => props.tab === SyndicationPageTabNames.Outbox;

const onRowClick = (_, row: ProductOutboxRow): void => {
  if (selectedRows.value.find((x) => x.id === row.id)) {
    outboxStore.selectRow(undefined);
    emit('productOutboxRowSelectedEvent', undefined);
  } else {
    outboxStore.selectRow(row);
    emit('productOutboxRowSelectedEvent', row);
  }
};

// Lifecycle methods
onBeforeMount(() => {
  isPageLoading.value = true;
});

onMounted(async () => {
  await getNextPageOfProductOutboxRows(destinationId);
  isPageLoading.value = false;
});

watchEffect(async () => {
  if (props.tab !== SyndicationPageTabNames.Outbox) {
    outboxStore.selectRow(undefined);
  }
});
</script>

<style lang="scss" scoped>
$product-outbox-tab-panel-offset: 203px;

.scroll-container {
  max-height: calc(100vh - $product-outbox-tab-panel-offset);
}
</style>
