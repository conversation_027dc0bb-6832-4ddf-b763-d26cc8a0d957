parameters:
  - name: stack
    default: euwDev1a
    type: string

  - name: playwrightArgs
    type: string
    default: ''

stages:
  - stage: DockerTests
    dependsOn:
    variables:
      - template: '../variables/euwDev4a.yml'
      - group: inri-access-token
    jobs:
      - job: IntegrationTests
        timeoutInMinutes: 180
        pool:
          vmImage: 'ubuntu-latest'

        variables:
          - group: 'iPMC.Autotest.Secrets'

          - name: reportArtifactName
            value: playwright-report-${{ parameters.stack }}

        steps:
          - script: chmod +x entrypoint.sh
            displayName: 'Make script.sh executable'
          - script: |
              rm -f $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_IS_API_ON=true' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_FILE_SERVER_URL=${{ variables.fileServerUrl }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_INSTRUMENTATION_KEY=${{ variables.instrumentationKey }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_AUTH0_DOMAIN=${{ variables.auth0Domain }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_AUTH0_CLIENTID=${{ variables.auth0ClientId }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_API_URL=${{ variables.apiUrl }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_ALLOW_MISSING_ORG_MAPPING=${{ variables.allowMissingOrgMapping }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_AUTH0_CONNECTION=${{ variables.auth0Connection }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_STATIC_WEB_APP_URL='https://${{ variables.customDomain }}'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_IS_DATA_EDITING_ENABLED='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_APLUS-OLD='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_COLLECTION='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_SCHEDULE='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_FULL-MAPPING-PATH='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_3RD-PARTY-MESSAGING='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_SYNDICATE-CORE='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_NOT-READY='true'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_ENVIRONMENT_GID=${{ variables.environmentGuid }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_USER_NAME='autotestuser'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "VITE_FEATURE_CORE-BELOW-THE-FOLD=true" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo 'VITE_REQUEST_TRADING_PARTNER_TEMPLATE_URL=${{ variables.requestTradingPartnerUrl }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
              echo "TestLocalUrl=${{ variables.testLocalUrl }}" >> $(Build.SourcesDirectory)/test/.env
              echo 'TestUsername=${{ variables.testUserName }}' >> $(Build.SourcesDirectory)/test/.env
              echo 'TestPassword=${{ variables.testUserPassword }}' >> $(Build.SourcesDirectory)/test/.env
              echo "TestPortalUrl=${{ variables.testPortalUrl }}" >> $(Build.SourcesDirectory)/test/.env
              cat $(Build.SourcesDirectory)/test/.env
              cat $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env
            displayName: 'Set environment variables for stack'
          - task: AzureCLI@2
            inputs:
              azureSubscription: ${{ variables.serviceConnection }}
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                docker build -t  frontend-integration-tests:$(Build.BuildId) .
                docker run --name test-container --volume $(Build.ArtifactStagingDirectory)/reports:/app/test/test-results --volume $(Build.ArtifactStagingDirectory)/playwrightreports:/app/test/playwright-report -d  frontend-integration-tests:$(Build.BuildId) tail -f /dev/null
                docker exec test-container npm run test -- ${{ parameters.playwrightArgs }}
            env:
              imageName: frontend-integration-tests

          - publish: $(Build.ArtifactStagingDirectory)/reports
            condition: succeededOrFailed()
            artifact: 'test_output_$(Build.BuildId)'
            displayName: 'Publish test artifacts'

          - publish: $(Build.ArtifactStagingDirectory)/playwrightreports
            condition: succeededOrFailed()
            artifact: ${{ variables.reportArtifactName }}
            displayName: 'Publish playwright report artifacts'

          - task: PublishTestResults@2
            condition: succeededOrFailed()
            inputs:
              testRunner: JUnit
              testRunTitle: ${{ parameters.stack }}
              failTaskOnFailedTests: true
              testResultsFiles: '$(Build.ArtifactStagingDirectory)/reports/results.xml'
            displayName: 'Upload test reports'

          - template: templates/artifact/<EMAIL>
            parameters:
              buildId: $(Build.BuildId)
              artifactName: ${{ variables.reportArtifactName }}
