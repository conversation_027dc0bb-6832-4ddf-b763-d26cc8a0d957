import { describe, it, expect, vi, beforeEach } from 'vitest';
import useCVLFunction from './useCVLFunction';
import { Language } from '@customTypes/AppData';

describe('useCVLFunction', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('settings', () => {
    it('should calculate the settings correctly if no language is selected', async () => {
      // Arrange
      const sourceFieldCvlId = 'name';
      const { delimiter, language, settings } = useCVLFunction(undefined, sourceFieldCvlId);

      // Act
      delimiter.value = ';';
      language.value = undefined;

      // Assert
      expect(settings.value.function.args?.length).toBe(2);
      expect(settings.value.function.args[0]).toBe('name');
      expect(settings.value.function.args[1]).toBe(';');
      expect(settings.value.function.args[2]).toBeUndefined();
      expect(settings.value.function.values).toStrictEqual([]);
    });

    it('should calculate the settings correctly if the language is selected', async () => {
      // Arrange
      const sourceFieldCvlId = 'name';
      const { delimiter, language, settings } = useCVLFunction(undefined, sourceFieldCvlId);

      // Act
      delimiter.value = ';';
      language.value = { Name: 'en', DisplayName: 'en-name' } as Language;

      // Assert
      expect(settings.value.function.args?.length).toBe(3);
      expect(settings.value.function.args[0]).toBe('name');
      expect(settings.value.function.args[1]).toBe(';');
      expect(settings.value.function.args[2]).toBe('en');
      expect(settings.value.function.values).toStrictEqual([]);
    });
  });
});
