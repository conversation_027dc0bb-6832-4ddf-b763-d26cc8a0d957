<template>
  <q-tooltip>
    <div v-if="props.errors.length > 0">
      <p style="font-weight: bold">{{ $t('syndicate_plus.preflight.tooltip.errors') }}:</p>
      <div v-for="error in props.errors" :key="error">
        <p>
          {{ error }}
        </p>
      </div>
      <br v-if="props.warnings.length > 0" />
    </div>
    <div v-if="props.warnings.length > 0">
      <p style="font-weight: bold">{{ $t('syndicate_plus.preflight.tooltip.warnings') }}:</p>
      <div v-for="warning in props.warnings" :key="warning">
        <p>
          {{ warning }}
        </p>
      </div>
    </div>
  </q-tooltip>
</template>

<script setup lang="ts">
const props = defineProps({
  errors: {
    type: Object as () => string[],
    default: [] as any as string[],
  },
  warnings: {
    type: Object as () => string[],
    default: [] as any as string[],
  },
});
</script>
