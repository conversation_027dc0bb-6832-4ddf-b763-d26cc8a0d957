<template>
  <div>
    <div class="text-center">{{ $t('syndicate_plus.aplus_define_template.click_to_add_image') }}</div>
    <div class="text-center mb-10px font-bold">{{ dimensionsDisplayName }}</div>
    <c-select
      v-model="model.angle"
      :options="imageAngles"
      :label="$t('syndicate_plus.aplus_define_template.angle_or_image_url')"
      class="angle-input"
      option-label="name"
      option-value="id"
      emit-value
      hide-bottom-space
      use-input
      hide-selected
      fill-input
      clearable
      @update:model-value="updateModel"
      @filter="onFilter"
    />
    <q-input
      v-model="model.altText"
      class="mt-1 alt-text"
      v-bind="$inri.input"
      hide-bottom-space
      :label="$t('syndicate_plus.aplus_define_template.alt_text')"
      :maxlength="maxAltTextLength"
      counter
      @keyup.enter="updateModel"
      @blur="updateModel"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref, PropType, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { ImageData, Dimension } from '@customTypes/Aplus';
import { useImageAngleStore } from '@stores/TemplateBuilder';
import { useImageDimensions } from '@composables/AplusTemplateBuilder';

const props = defineProps({
  modelValue: {
    type: Object as PropType<ImageData>,
    default: () => ({}),
  },
  dimensions: {
    type: Object as PropType<Dimension>,
    default: () => ({}),
  },
  maxAltTextLength: {
    type: Number,
    default: 100,
  },
});

const imageAngleStore = useImageAngleStore();

// Composables
const { dimensionsDisplayName } = useImageDimensions(props.dimensions);

// Emits
const emit = defineEmits(['update-model']);

// Variables
const destinationId = parseInt(useRoute().params.destinationId as string);

// Refs
const model = ref(props.modelValue);
const { imageAngles } = storeToRefs(imageAngleStore);

// Functions
const updateModel = () => {
  emit('update-model');
};

const onFilter = (val) => {
  if (!val) {
    return;
  }

  model.value.angle = val;
  model.value.url = val;
};

onBeforeMount(() => imageAngleStore.fetchAngles(destinationId));
</script>

<style scoped lang="scss">
:deep(.q-field__control) {
  min-width: auto !important;
  margin: 0 auto;
  width: 80%;
}

:deep(.q-field__append) {
  padding-left: 0px;
}

:deep(.q-field__inner .q-field__bottom) {
  margin-right: 25px;
}
</style>
