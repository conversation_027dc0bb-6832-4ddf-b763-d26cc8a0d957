import { ref, onMounted, onBeforeUnmount } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';

export default function useConfirmDialog(hasUnsavedChanges: Function) {
  // Refs
  const showConfirmDialog = ref(false);

  // Variables
  let nextRouteResolver: Function | null = null;

  // Functions
  const handleConfirm = () => {
    showConfirmDialog.value = false;
    nextRouteResolver && nextRouteResolver(true);
  };

  const handleCancel = () => {
    showConfirmDialog.value = false;
    nextRouteResolver && nextRouteResolver(false);
  };

  const showUnsavedChangesDialog = async () => {
    return new Promise((resolve) => {
      showConfirmDialog.value = true;
      nextRouteResolver = resolve;
    });
  };

  const handleBeforeUnload = (event) => {
    if (hasUnsavedChanges()) {
      event.preventDefault();
    }
  };

  // Lifecycle methods
  onMounted(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });

  onBeforeRouteLeave((to, from, next): void => {
    if (hasUnsavedChanges()) {
      showUnsavedChangesDialog().then((confirmed) => {
        if (confirmed) {
          next();
        } else {
          next(false);
        }
      });
    } else {
      next();
    }
  });

  return { showConfirmDialog, handleConfirm, handleCancel };
}
