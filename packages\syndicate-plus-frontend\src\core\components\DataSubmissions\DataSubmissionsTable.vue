<template>
  <div>
    <q-table
      ref="submissionsTable"
      v-model:selected="selectedSubmission"
      class="submissions-table sticky-table-header"
      :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
      flat
      dense
      hide-bottom
      separator="cell"
      :pagination="{
        page: 1,
        rowsPerPage: 0,
      }"
      :rows="dataSubmissions"
      row-key="id"
      :columns="dataSubmissionsColumns"
      :loading="isLoading"
      virtual-scroll
      :virtual-scroll-item-size="48"
      :virtual-scroll-sticky-size-start="48"
      @virtual-scroll="onVirtualScroll"
      @row-click="onRowClick"
    >
      <template #loading>
        <q-inner-loading showing color="primary" class="inner-loading">
          <c-spinner color="primary" size="40" />
        </q-inner-loading>
      </template>
      <template #body-cell-createdDate="props">
        <q-td>{{ convertDateToLocalFormat(props.row.createdDate) }}</q-td>
      </template>
      <template #body-cell-updatedDate="props">
        <q-td>{{ jobId ?? convertDateToLocalFormat(props.row.updatedDate) }}</q-td>
      </template>
    </q-table>
  </div>
</template>

<script lang="ts" setup>
import { ref, toRef, watch } from 'vue';
import { DataSubmission } from '@core/interfaces';
import { dataSubmissionsColumns } from '@core/const';
import { convertDateToLocalFormat } from '@services/helper/dateHelpers';

const props = defineProps({
  dataSubmissions: {
    type: Array as () => DataSubmission[],
    required: true,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  jobId: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['update:selected', 'load-more']);

// Refs
const selectedSubmission = ref<DataSubmission[]>([]);

// Functions
const onVirtualScroll = ({ to }: { to: number }) => {
  const dataSubmissions = toRef(props, 'dataSubmissions');
  if (to >= dataSubmissions.value.length - 5 && !props.isLoading) {
    emit('load-more');
  }
};

const onRowClick = (_: Event, row: DataSubmission): void => {
  const newRowValue = selectedSubmission.value?.includes(row) ? null : row;
  selectedSubmission.value = newRowValue ? [newRowValue] : [];
  emit('update:selected', newRowValue || null);
};

// Lifecycle methods
watch(
  () => props.dataSubmissions,
  (newSubmissions) => {
    if (newSubmissions.length > 0 && !props.isLoading && selectedSubmission.value.length === 0) {
      const firstRow = newSubmissions[0];
      selectedSubmission.value = [firstRow];
      emit('update:selected', firstRow);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.submissions-table {
  max-height: 350px;
  margin-bottom: 20px;
  min-height: 120px;
}
</style>
