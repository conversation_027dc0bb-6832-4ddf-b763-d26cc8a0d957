<template>
  <div class="flex pb-10px">
    <c-btn
      :label="$t('syndicate_plus.aplus_define_template.add_metric')"
      :tooltip="
        isAddMetricButtonDisabled
          ? $t('syndicate_plus.aplus_define_template.add_metric_disabled_tooltip')
          : $t('syndicate_plus.aplus_define_template.add_metric')
      "
      :disabled="isAddMetricButtonDisabled"
      @click="addMetric"
    />
  </div>
  <div class="flex flex-row flex-nowrap justify-end">
    <q-list>
      <q-item v-for="(_, index) in model" :key="index" v-ripple clickable>
        <q-item-section side>
          <q-btn label="X" @click="removeBullet(index)" />
        </q-item-section>
        <q-item-section avatar>
          <div class="flex flex-row">
            <div class="w-166px m-10px flex justify-center">
              <q-input
                v-model="model[index].metric"
                v-bind="$inri.input"
                :label="$t('syndicate_plus.aplus_define_template.enter_metric')"
                hide-bottom-space
                :maxlength="maxMetricLength"
                counter
                @keyup.enter="updateMetric"
                @blur="updateMetric"
                @drop="onDropMetric(model[index].metric, 'metric', index)"
              />
            </div>
            <div
              v-for="(_, metricIndex) in numberOfMetricColumns"
              :key="metricIndex"
              class="w-166px m-10px flex justify-center"
            >
              <q-input
                v-model="model[index][`text${metricIndex + 1}`]"
                :label="$t('syndicate_plus.aplus_define_template.enter_text')"
                v-bind="$inri.input"
                hide-bottom-space
                :maxlength="maxMetricValueLength"
                counter
                @keyup.enter="updateMetric"
                @blur="updateMetric"
                @drop="onDropMetric(model[index][`text${metricIndex + 1}`], 'text', index, metricIndex + 1)"
              />
            </div>
          </div>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { MetricData } from '@customTypes/Aplus';
import { useFieldsTabStore } from '@stores/TemplateBuilder';

const props = defineProps({
  modelValue: {
    type: Array<MetricData>,
    default: [],
  },
  maxMetricLength: {
    type: Number,
    default: 100,
  },
  maxMetricValueLength: {
    type: Number,
    default: 100,
  },
});

// Variables
const numberOfMetricColumns = 6;
const maxLength = 10;

const fieldsStore = useFieldsTabStore();

// Emits
const emit = defineEmits(['update-model']);

// Refs
const model = ref(props.modelValue);
const newMetric = ref({} as MetricData);

// Computed fields
const isAddMetricButtonDisabled = computed(() => model.value?.length >= maxLength);

// Functions
const addMetric = (): void => {
  if (isAddMetricButtonDisabled.value) {
    return;
  }

  model.value.push(newMetric.value);
  newMetric.value = {} as MetricData;
  updateMetric();
};

const removeBullet = (index: number): void => {
  model.value.splice(index, 1);
  updateMetric();
};

const updateMetric = (): void => {
  emit('update-model');
};

const onDropMetric = (value: string, key: string, index: number, metricIndex?: number): void => {
  if (!metricIndex) {
    model.value[index][key] = fieldsStore.onDrop(value);
    updateMetric();
    return;
  }

  model.value[index][`${key}${metricIndex}`] = fieldsStore.onDrop(value);
  console.log(model.value);
  updateMetric();
};
</script>

<style scoped lang="scss">
:deep(.q-field__control) {
  min-width: auto !important;
}

:deep(.q-item) {
  padding: 10px 0px;
}

:deep(.q-item__section--side) {
  padding-right: 0px;
}

:deep(.q-item.q-item-type) {
  align-items: baseline;
  height: 80px;
}
</style>
