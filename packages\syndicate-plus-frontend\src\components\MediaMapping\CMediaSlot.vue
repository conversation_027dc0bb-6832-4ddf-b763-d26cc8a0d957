<template>
  <div class="c-media-slot">
    <div class="content flex items-center justify-center">
      <img v-if="mediaSlot.imageSrc" :src="mediaSlot.imageSrc" class="image" />
      <q-icon v-else name="mdi-image-outline" :style="`font-size: 54px`" />
    </div>
    <q-field
      class="media-mapping-field mb-5"
      outlined
      :label="$t('syndicate_plus.mapping.media_mapping_tab.syndication_media_tag')"
      stack-label
      readonly
    >
      <span>{{ mediaSlot.inriverTag }}</span>
    </q-field>
    <q-field
      class="media-mapping-field"
      outlined
      :label="`${tradingPartnerName} ${$t('syndicate_plus.mapping.media_mapping_tab.media_tag')}`"
      stack-label
      readonly
    >
      <span>{{ mediaSlot.tradingPartnerTag }}</span>
    </q-field>
  </div>
</template>
<script setup lang="ts">
import { MediaSlot } from '@customTypes/MediaMappings/mediaSlot';

defineProps({
  mediaSlot: {
    type: Object as () => MediaSlot,
    default: () => ({}),
  },
  tradingPartnerName: {
    type: String,
    default: 'trading partner',
  },
});
</script>

<style scoped lang="scss">
.c-media-slot {
  .content {
    width: 193px;
    height: 193px;
    margin-bottom: 15px;
    background-color: var(--color-grey-lighter);

    img {
      width: inherit !important;
      height: inherit !important;
      object-fit: contain !important;
    }

    .q-icon {
      color: var(--color-grey);
    }
  }
}

:deep(.q-field__inner .q-field__control) {
  border-radius: 5px;
  width: 193px;
  min-height: 40px;
  height: 40px;

  .q-field__control-container {
    padding-top: 10px;
  }

  .q-field__label {
    font-size: 13px;
  }
}
</style>
