<template>
  <div class="products">
    <div v-for="item in products" :key="item.id" class="product-card">
      <div class="card-badge-container">
        <q-badge v-if="item.isAplus" rounded :label="$t('syndicate_plus.syndication.aplus')" class="card-badge aplus" />
      </div>
      <c-small-card
        :key="item.id"
        :image-src="item.imageUrl"
        :title="item.name"
        :text="item.name"
        :selected="isSelected(item)"
        @click="toggleProduct(item)"
      />
    </div>
    <q-inner-loading v-if="isFetching" showing color="primary" class="spinner">
      <c-spinner color="primary" size="40" />
    </q-inner-loading>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useSelectable } from '@inriver/inri';
import { Product } from '@customTypes';

const props = defineProps({
  products: {
    type: Array<Product>,
    default: [],
  },
  selectedProducts: {
    type: Array<Product>,
    default: [],
  },
  isFetching: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['select-product']);

// Refs
const selectedProductList = ref<Product[]>(props.selectedProducts);

// Composables
const { isSelected } = useSelectable(props.products, selectedProductList);

// Function
const toggleProduct = (product: Product): void => {
  const existingItemIndex = selectedProductList.value.findIndex((i) => JSON.stringify(i) === JSON.stringify(product));
  if (existingItemIndex >= 0) {
    selectedProductList.value.splice(existingItemIndex, 1);
  } else {
    selectedProductList.value = [product];
    emit('select-product', product);
  }
};

// Lifecycle methods
watch(
  () => props.selectedProducts,
  (newValue) => {
    selectedProductList.value = newValue;
  }
);
</script>

<style scoped lang="scss">
.products {
  height: 70vh;
  display: flex;
  flex-direction: column;
  overflow-y: scroll;
  padding: 10px 20px;

  .product-card {
    align-self: center;
    padding-bottom: 10px;
    position: relative;

    .card-badge-container {
      position: absolute;
      top: 6px;
      left: 5px;
      z-index: 1;

      .card-badge {
        color: var(--color-black);
        height: 18px;

        &.aplus {
          background-color: var(--color-yellow);
          padding: 3px 4px;
        }
      }
    }
  }

  .spinner {
    z-index: 2;
  }
}

:deep(.c-inri-small-card) {
  min-height: 150px;
}
</style>
