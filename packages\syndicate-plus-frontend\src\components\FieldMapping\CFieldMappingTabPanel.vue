<template>
  <c-section class="field-mapping-tab pr-0 pb-0">
    <c-mapping-dialog
      v-if="showMappingDialog"
      v-model:show="showMappingDialog"
      :title="selectedRows[0]?.targetFieldName"
      :is-loading="isDataLoading"
      :outbound="outboundTemplateFieldForSelectedRow"
      :inbounds="inboundTemplateFieldsForSelectedRow"
    />
    <div class="filter-panel">
      <c-select
        v-model="selectedInboundTemplate"
        :options="inboundTemplates"
        :placeholder="$t('syndicate_plus.common.inbound_template')"
        :label="$t('syndicate_plus.common.inbound_template')"
        option-label="templateName"
        option-value="id"
        @update:model-value="loadInboundData"
      />
      <c-select
        v-model="selectedOutboundTemplate"
        :options="outboundTemplates"
        :placeholder="$t('syndicate_plus.common.outbound_template')"
        :label="$t('syndicate_plus.common.outbound_template')"
        option-label="name"
        option-value="id"
        @update:model-value="loadOutboundData"
      />
      <c-select
        v-if="outboundTemplateSheets?.length > 0"
        v-model="selectedOutboundSheet"
        :options="outboundTemplateSheets"
        :placeholder="$t('syndicate_plus.common.outbound_sheet')"
        :label="$t('syndicate_plus.common.outbound_sheet')"
        option-label="name"
        option-value="id"
        @update:model-value="filterData"
      />
    </div>
    <div v-if="isLoading" class="spinner">
      <c-spinner />
    </div>
    <div v-else-if="outboundTemplateFields?.length">
      <q-table
        v-if="selectedOutboundTemplate"
        ref="tableRef"
        v-model:selected="selectedRows"
        :rows="outboundTemplateFields"
        :columns="columns"
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        row-key="targetFieldName"
        separator="cell"
        dense
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        class="hide-checkboxes"
        :visible-columns="visibleColumns"
        hide-bottom
        @row-click="onRowClick"
      >
        <template #body-cell-status="props">
          <q-td>
            <c-small-square :color="props.row['status'] ? SquareColor.GREEN : SquareColor.RED">
              <q-tooltip>
                {{
                  props.row['status']
                    ? $t('syndicate_plus.mapping.mapped_field')
                    : $t('syndicate_plus.mapping.unmapped_field')
                }}
              </q-tooltip>
            </c-small-square>
          </q-td>
        </template>
        <template #body-cell-suggestedMatch="props">
          <q-td>
            <c-small-square :color="props.row['suggestedMatch'] ? SquareColor.ORANGE : SquareColor.UNKNOWN">
              <q-tooltip>{{ $t('syndicate_plus.mapping.suggested_field') }}</q-tooltip>
            </c-small-square>
          </q-td>
        </template>
        <template #body-cell-sourceFieldName="props">
          <q-td>
            {{ getSourceFieldDisplayName(props.row.sourceFieldName) }}
          </q-td>
        </template>
        <template #body-cell-actions>
          <c-table-actions>
            <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
          </c-table-actions>
        </template>
        <template #body-cell-function="props">
          <q-td :props="props">
            <div class="field-mapping-function-class">
              <div v-for="functionObject in props.row.functions" :key="functionObject">
                <c-field-mapping-function :func="functionObject" />
              </div>
            </div>
          </q-td>
        </template>
        <template #body-cell-dataType="props">
          <c-table-actions>
            {{ `${props.row['dataType']?.toLocaleLowerCase()}` }}
          </c-table-actions>
        </template>
        <template #body-cell-length="props">
          <q-td :props="props">
            <c-mapping-length
              :min-length="props.row['minCharacterLength']"
              :max-length="props.row['maxCharacterLength']"
            />
          </q-td>
        </template>
        <template #body-cell-importanceType="props">
          <q-td :props="props">
            <div class="flex flex-nowrap justify-start">
              {{ $t(`syndicate_plus.mapping.importace_type.${props.row['importanceType']}`) }}
            </div>
          </q-td>
        </template>
      </q-table>
    </div>
    <div v-else>
      <c-no-data
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_title')"
        :text="$t('syndicate_plus.mapping.media_mapping_tab.no_data.no_mappings_message')"
      />
    </div>
  </c-section>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, toRef, computed } from 'vue';
import { useRoute } from 'vue-router';
import { CFieldMappingFunction, CMappingDialog, CMappingLength } from '@components/FieldMapping';
import { CNoData, CSmallSquare } from '@components';
import { FullMappingPathTemplateField, InboundTemplateField, OutboundTemplateField } from '@customTypes';
import { useAppInsightsStore } from '@stores';
import { PageName, SquareColor } from '@enums';
import {
  useFieldMappingColumns,
  useInboundTemplates,
  useMappingDialog,
  useOutboundTemplates,
} from '@composables/FieldMapping';
import { getSourceFieldDisplayName } from '@helpers';

const componentProps = defineProps({
  tradingPartnerName: {
    type: String,
    default: '',
  },
});
const emit = defineEmits(['selected-row-changed']);

const route = useRoute();

// Variables
const destinationId: number = parseInt(route.params.destinationId as string);

// Refs
const visibleColumns = ref<string[]>([]);
const selectedRows = ref<FullMappingPathTemplateField[]>([]);
const tradingPartnerNameRef = toRef(componentProps, 'tradingPartnerName');
const isDataLoading = ref<boolean>(false);
const inboundTemplateFieldsForSelectedRow = ref<InboundTemplateField[]>([]);
const outboundTemplateFieldForSelectedRow = ref<OutboundTemplateField>();

// Computed
const isLoading = computed(() => isInboundLoading.value || isOutboundLoading.value);

// Composables
const { setScreenName } = useAppInsightsStore();
const { showMappingDialog } = useMappingDialog();
const { columns } = useFieldMappingColumns(tradingPartnerNameRef);
const {
  isLoading: isInboundLoading,
  inboundTemplates,
  selectedInboundTemplate,
  inboundTemplateFields,
  loadInboundData,
} = useInboundTemplates(selectedRows);
const {
  isLoading: isOutboundLoading,
  outboundTemplates,
  selectedOutboundTemplate,
  outboundTemplateSheets,
  selectedOutboundSheet,
  outboundTemplateFields,
  loadOutboundData,
  filterData,
} = useOutboundTemplates(columns, visibleColumns, selectedRows, destinationId, inboundTemplateFields);

// Functions
const onRowClick = (_, row: FullMappingPathTemplateField): void => {
  const newRowValue = selectedRows.value?.includes(row) ? null : row;
  selectedRows.value = newRowValue ? [newRowValue] : [];
};

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.FIELD_MAPPINGS);
});

watch(
  () => selectedRows.value,
  () => {
    emit('selected-row-changed', selectedRows.value);
  }
);

watch(
  () => showMappingDialog.value,
  () => {
    if (showMappingDialog.value) {
      inboundTemplateFieldsForSelectedRow.value = selectedRows.value[0].sourceField
        ? [selectedRows.value[0].sourceField]
        : [];
      outboundTemplateFieldForSelectedRow.value = selectedRows.value[0].targetField;
    }
  }
);
</script>

<style lang="scss" scoped>
$field-mapping-tab-panel-offset: 296px;

.spinner {
  margin: auto;
  width: min-content;
}

.filter-panel {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.field-mapping-function-class {
  display: flex;
  align-items: center;
}
</style>
