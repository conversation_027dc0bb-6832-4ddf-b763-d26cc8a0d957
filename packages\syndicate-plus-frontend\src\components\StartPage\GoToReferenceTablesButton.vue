<template>
  <c-tile-btn
    v-if="isButtonVisible"
    icon="mdi-table"
    :icon-size="20"
    :tooltip-left="$t('syndicate_plus.reference_tables.tooltip')"
    @click="logEventAndClick(ActionName.GO_TO_REFERENCE_TABLES, goToReferenceTables)"
  />
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useSyndicatePlusAdminRights } from '@composables';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';
import { useRouter } from '@composables/useRouter';
import isFeatureEnabled from '@utils/isFeatureEnabled';

const { logEventAndClick } = useAppInsightsStore();
const { hasSyndicatePlusAdminRights } = useSyndicatePlusAdminRights();
const { goToPage } = useRouter();

const isButtonVisible = computed(() => hasSyndicatePlusAdminRights.value && isFeatureEnabled('reference-tables'));

const goToReferenceTables = (): void => {
  goToPage('reference-tables-page');
};
</script>
