import { watch, Ref } from 'vue';
import { SaveStatus } from '@enums';
import { notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';

export default function useEditStatus(saveStatus: Ref<SaveStatus>, messages?: { [T in SaveStatus]: string }) {
  watch(saveStatus, () => showNotification());

  // Variables
  let dismissNotification: Function;
  const { t } = useI18n();

  // Functions
  const showNotification = () => {
    if (saveStatus.value === SaveStatus.SAVING) {
      dismissNotification = notify.ongoing(getMessage(saveStatus.value) ?? 'saving ...', {
        position: 'bottom-right',
      });
    }

    if (saveStatus.value === SaveStatus.SAVED) {
      dismissNotification && dismissNotification();
      notify.success(getMessage(saveStatus.value) ?? '<b>edits</b> saved successfully', {
        position: 'bottom-right',
        html: true,
      });
    }

    if (saveStatus.value === SaveStatus.ERROR) {
      dismissNotification && dismissNotification();
      notify.error(getMessage(saveStatus.value) ?? 'we could not save your <b>edits</b>. please try again', {
        position: 'bottom-right',
        html: true,
      });
    }
  };

  const getMessage = (status: SaveStatus) => {
    return messages && t(messages[status]);
  };

  return {};
}
