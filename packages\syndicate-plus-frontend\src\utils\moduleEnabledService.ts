import isFeatureEnabled from '@utils/isFeatureEnabled';
import AccessService from '@services/AccessService';

export function isSyndicatePlusPartnersEnabled() {
  return (
    window?.appData?.isSyndicatePlusPartnersDisabled !== 'true' &&
    window?.appData?.isSyndicatePlusPartnersDisabled !== true &&
    !isFeatureEnabled('disable-partners')
  );
}

export function isSyndicatePlusWithCoreEnabled() {
  return (
    window?.appData?.isSyndicatePlusWithCoreEnabled === 'true' ||
    (window?.appData?.isSyndicatePlusWithCoreEnabled as any) === true ||
    isFeatureEnabled('syndicate-core')
  );
}

export function isSyndicateAdvanceEnabled() {
  return (
    window?.appData?.isSyndicateAdvanceEnabled === 'true' ||
    (window?.appData?.isSyndicateAdvanceEnabled as any) === true ||
    isFeatureEnabled('syndicate-core')
  );
}

export function isSyndicateAdvanceConfigEnabled() {
  return AccessService.hasSyndicateAdvanceConfigAccess();
}
