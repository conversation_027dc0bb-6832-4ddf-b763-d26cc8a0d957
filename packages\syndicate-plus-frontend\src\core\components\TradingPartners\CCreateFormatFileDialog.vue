<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    :confirm-button-text="$t('syndicate_plus.common.create')"
    :disable-confirm="confirmButtonIsDisabled"
    :disabled-confirm-tooltip="$t('core.manage_trading_partners.create_format_file.validation_error')"
    @cancel="cancel"
    @confirm="onSubmit"
  >
    <q-form ref="form" greedy autofocus>
      <div class="row q-gutter-lg">
        <div class="col section">
          <h3>
            {{ $t('core.manage_trading_partners.create_format_file.tooltip') }}
          </h3>
          <q-input
            v-model="tradingPartnerName"
            v-bind="$inri.input"
            :label="$t('core.manage_trading_partners.create_format_file.company')"
            :rules="[$validate.required()]"
            autofocus
          />
          <q-input
            v-model="categoryName"
            v-bind="$inri.input"
            :label="$t('core.manage_trading_partners.create_format_file.category')"
            :rules="[$validate.required()]"
          />
          <q-input
            v-model="companyImageUrl"
            v-bind="$inri.input"
            :label="$t('core.manage_trading_partners.create_format_file.url')"
            :error="isUrlInputError"
            :error-message="urlErrorMessage"
          />
          <c-file-uploader accept=".xls,.xlsx,.csv" @file-updated="onFormatFileUpdated">
            <div>
              {{ $t('core.manage_trading_partners.upload_file.placeholder') }}
            </div>
          </c-file-uploader>
        </div>
        <div class="col section flex items-center justify-center">
          <div class="image-wrapper">
            <img v-if="companyImageUrl && showPreview" :src="companyImageUrl" class="image" @error="handleImageError" />
          </div>
        </div>
      </div>
    </q-form>
  </c-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { useDialog, notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';
import { useCreateFormatFile } from '@core/composables/TradingPartners';
import { CFileUploader } from '@core/components/Common';
import { useCoreTradingPartnersStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { LocalizedError } from '@core/interfaces';

const emit = defineEmits(['format-file-created']);
const coreTradingPartnersStore = useCoreTradingPartnersStore();

// Refs
const form = ref();
const showPreview = ref<boolean>(true);
const { coreFormats } = storeToRefs(coreTradingPartnersStore);

// Composables
const { t } = useI18n();
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel, confirmSuccess } = useDialog();
const {
  categoryName,
  tradingPartnerName,
  companyImageUrl,
  confirmButtonIsDisabled,
  save,
  onFormatFileUpdated,
  isCompanyImageUrlValid,
} = useCreateFormatFile(isLoading, coreFormats);

// Computed
const isUrlInputError = computed(() => {
  return !isCompanyImageUrlValid.value && companyImageUrl.value?.length && companyImageUrl.value.length > 0;
});

const urlErrorMessage = computed(() => {
  return isUrlInputError.value ? t('core.manage_trading_partners.create_format_file.invalid_image_url') : '';
});

// Functions
const onCreate = async () => {
  try {
    const success = await save();
    if (success) {
      notify.success(t('core.manage_trading_partners.create_format_file.create_success'));
      confirmSuccess(null);
      emit('format-file-created');
    } else {
      notify.error(t('core.manage_trading_partners.create_format_file.create_error'));
    }
  } catch (e) {
    if (e instanceof LocalizedError) {
      notify.error(t(e.message));
    } else {
      notify.error((e as Error).message);
    }
  }
};

const onSubmit = async () => {
  form.value.validate().then((success) => {
    if (success) {
      logEventAndClick(ActionName.ADD_FORMAT_FILE);
      onCreate();
    } else {
      notify.error(t('core.manage_trading_partners.create_format_file.validation_error'));
    }
  });
};

const handleImageError = () => {
  showPreview.value = false;
};

// Lifecycle methods
watch(
  () => companyImageUrl.value,
  () => (showPreview.value = true)
);
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }
}

.image-wrapper {
  .image {
    object-fit: contain;
    height: 100%;
    width: 100%;
    max-height: 400px;
    max-width: 400px;
  }
}
</style>
