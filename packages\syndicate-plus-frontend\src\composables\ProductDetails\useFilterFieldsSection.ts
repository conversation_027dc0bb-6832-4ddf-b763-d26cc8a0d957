import { computed, ComputedRef, ref, Ref } from 'vue';
import { useLocalStorage } from '@vueuse/core';
import { FilterField } from '@customTypes/Products';

export default function useFilterFieldsSection(
  filteredColumns: ComputedRef<FilterField[]>,
  showInternalNames: Ref<Boolean>,
  destinationId?: number
) {
  // Refs
  const selectedColumnNames = destinationId
    ? useLocalStorage<string[]>(`aplus.${destinationId}.details.added-columns`, [])
    : ref([]);

  // Computed
  const selectedColumns = computed(() =>
    filteredColumns.value.filter((x) => selectedColumnNames.value.indexOf(x.name) >= 0)
  );
  const unselectedColumns = computed(() =>
    filteredColumns.value.filter((x) => selectedColumnNames.value.indexOf(x.name) === -1)
  );
  const separatorIsVisible = computed(() => selectedColumnNames.value?.length && unselectedColumns.value?.length);

  // Functions
  const createLabel = (column: FilterField): string => {
    const displayName =
      showInternalNames?.value && column.internalName ? column.internalName : column.displayName.toLocaleLowerCase();

    return column.isRequired ? `${displayName}*` : displayName;
  };

  return {
    selectedColumnNames,
    selectedColumns,
    unselectedColumns,
    separatorIsVisible,
    createLabel,
  };
}
