import { defineStore } from 'pinia';
import { ref } from 'vue';
import {
  getMapping,
  updateMapping,
  getSuggestions,
  fetchDynamicMappingById,
  updateDynamicMapping,
} from '@core/services/Mappings';
import { getFieldTypes } from '@core/services/FieldTypes';
import { getAllCvl } from '@core/services/Cvl';
import { fetchFormatByCategoryIdAndTradingPartnerId } from '@core/services/Categories';
import {
  MappingDetailsResponse,
  GroupedFieldType,
  MappingSuggestionModel,
  InriverFieldType,
  Cvl,
  MappingFieldResponse,
  InriverFieldTypeResponse,
  DynamicMappingResponse,
  DynamicMappingDetailsResponse,
  DynamicMappingFieldResponse,
  FormatEnum,
} from '@core/interfaces';
import { DynamicFormat, DynamicFormatRow, ObjectSchema, ObjectSchemaProperties } from '@core/interfaces/Category';
import { Language } from '@customTypes';
import { ResourceFieldTypes, InriverDataType, RowTargetType, RowTargetTypeDisplayValue } from '@core/enums';
import { useLanguages } from '@core/composables/Common';
import { useChooseLanguageFunction } from '@core/composables/FieldMapping/Functions';

export const useEditFieldMappingStore = defineStore('editFieldMappingStore', () => {
  // Refs
  const isLoading = ref(false);
  const defaultMapping = ref<MappingDetailsResponse | DynamicMappingDetailsResponse>();
  const mapping = ref<MappingDetailsResponse | DynamicMappingDetailsResponse>();
  const dynamicMapping = ref<DynamicMappingResponse>();
  const entityTypesDictionary = ref<GroupedFieldType[]>([]);
  const category = ref<DynamicFormat>();
  const cvls = ref<Cvl[]>([]);

  const { languages } = useLanguages();
  const { getChooseLanguageFunctionSettings, getChooseLanguageFunctionId } = useChooseLanguageFunction();

  // Functions
  const hasUnsavedChanges = (): boolean => {
    if (!mapping.value || !defaultMapping.value) {
      return false;
    }

    return JSON.stringify(mapping.value) !== JSON.stringify(defaultMapping.value);
  };

  const fetchMappingDetails = async (mappingId: number, update = false): Promise<void> => {
    isLoading.value = true;
    mapping.value = await getMapping(mappingId);
    defaultMapping.value = JSON.parse(JSON.stringify(mapping.value));
    if (!update) {
      cvls.value = await getAllCvl();
      await fetchFieldTypes();
    }

    isLoading.value = false;
  };

  const fetchDynamicMappingDetails = async (mappingId: number, update = false): Promise<any> => {
    isLoading.value = true;
    try {
      dynamicMapping.value = await fetchDynamicMappingById(mappingId);
      if (!Object.keys(dynamicMapping.value).length) {
        console.error('Error fetching dynamic mapping');
        return;
      }

      mapping.value = JSON.parse(dynamicMapping.value.data);
      defaultMapping.value = JSON.parse(JSON.stringify(mapping.value));
      if (!update) {
        cvls.value = await getAllCvl();
        await fetchFieldTypes();
      }
    } catch (e) {
      console.error('Error fetching dynamic mapping', e);
    } finally {
      isLoading.value = false;
    }
  };

  const fetchCategoryForDynamicMapping = async (categoryId: string, tradingPartnerId: string): Promise<void> => {
    try {
      isLoading.value = true;
      category.value = await fetchFormatByCategoryIdAndTradingPartnerId(categoryId, tradingPartnerId);
    } catch (e) {
      console.error('Error fetching category for dynamic mapping', e);
    } finally {
      isLoading.value = false;
    }
  };

  const initDynamicMappingModelList = (): void => {
    if (!mapping.value || !category.value?.formats?.length) {
      return;
    }

    if (mapping.value.MappingModelList?.length) {
      // we will add check if the dynamic format file changed and add logic to handle if so
      convertEnumerationValuesToEnumerationsForExistingMapping();
      initializeMissingPropertiesForExistingMapping();
      return;
    }

    isLoading.value = true;
    for (const format of category.value.formats) {
      const mappingRow = createMappingRow(format);
      (mapping.value as DynamicMappingDetailsResponse).MappingModelList.push(mappingRow);
    }
    isLoading.value = false;
  };

  const getFormatFieldForSelectedRow = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse) =>
    category.value?.formats.find((x) => x.field === selectedRow.FormatField);

  const getSelectedRowIndex = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse): number =>
    mapping.value ? mapping.value.MappingModelList.findIndex((x) => x.FormatField === selectedRow.FormatField) : -1;

  const createMappingRow = (
    formatField,
    parentMappingRow: DynamicMappingFieldResponse | null = null
  ): DynamicMappingFieldResponse => {
    return {
      inRiverEntityTypeId: null, // required nullable
      inRiverFieldTypeId: null, // required nullable
      inRiverDataType: null, // required nullable
      FormatFieldId: formatField.id, // required nullable
      FormatDataType: formatField.datatype, // required string
      FormatField: formatField.field, // required string
      Category: formatField.category ? formatField.category : parentMappingRow?.Category ?? '', // required string
      Path: formatField.path ? formatField.path : parentMappingRow?.Path ?? null, // required nullable
      UnitType: formatField.unitType ? formatField.unitType : parentMappingRow?.UnitType ?? null, // required nullable
      UnitCvl: formatField.unitCvl ? formatField.unitCvl : parentMappingRow?.UnitCvl ?? null, // required nullable
      UnitDefaultValue: formatField.unitDefaultValue
        ? formatField.unitDefaultValue
        : parentMappingRow?.UnitDefaultValue ?? null, // required nullable
      Unique: formatField.unique ? formatField.unique : parentMappingRow?.Unique ?? false, // required boolean
      Recommended: formatField.recommended ? formatField.recommended : parentMappingRow?.Recommended ?? false, // required boolean
      Mandatory: formatField.mandatory ? formatField.mandatory : parentMappingRow?.Mandatory ?? false, // required boolean
      DefaultValue: formatField.defaultValue ? formatField.defaultValue : parentMappingRow?.DefaultValue ?? null, // required nullable
      MaxLength: formatField.maxLength ? formatField.maxLength : parentMappingRow?.MaxLength ?? null, // required nullable
      Description: formatField.description ? formatField.description : parentMappingRow?.Description ?? null, // required nullable
      ConverterArgs: null, // required nullable
      ConverterClass: null, // required nullable
      ConverterId: null, // required nullable
      MinLength: formatField.minLength,
      MaxValue: formatField.maxValue,
      MinValue: formatField.minValue,
      MaxInstances: formatField.maxInstances,
      MinInstances: formatField.minInstances,
      DecimalFormat: formatField.decimalFormat,
      RegEx: formatField.regEx,
      ConditionalRule: formatField.conditionalRule,
      ChildAttributes: formatField.childAttributes,
      Format: formatField.format,
      GroupRequiredFields: formatField.groupRequiredFields,
      Enumerations: convertToCoreEnumFormats(formatField.enumerationValues),
      CvlCompleteness: formatField.CvlCompleteness,
      // TODO: EnumerationValues can be deleted after the release of the LRJ part of the #110266 work item  (https://dev.azure.com/inriver/iPMC/_git/742e671e-0928-4e07-a6f9-6ddb40d82d80/pullrequest/32723)
      EnumerationValues: formatField.enumerationValues ?? '',
    } as DynamicMappingFieldResponse;
  };

  const isSelectedRowCustomType = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse): boolean =>
    ([RowTargetType.LIST_ENUM, RowTargetType.LIST_OBJECT, RowTargetType.LIST_STRING] as string[]).includes(
      selectedRow.FormatDataType
    );

  const addListItem = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse): void => {
    const selectedRowIndex = getSelectedRowIndex(selectedRow);
    if (selectedRowIndex < 0) {
      console.error('Selected row not found in mapping model list');
      return;
    }

    const formatRow = getFormatFieldForSelectedRow(selectedRow);
    if (!formatRow || !isSelectedRowCustomType(selectedRow) || !mapping.value) {
      return;
    }

    isLoading.value = true;
    const currentSchema = formatRow.objectSchema;
    const isListObjectType = currentSchema && selectedRow.FormatDataType === RowTargetType.LIST_OBJECT;
    const childrenNumber = getNumberOfChildListItems(selectedRow);
    const numberOfProperties = isListObjectType ? getNumberOfProps(currentSchema.properties) : 1;
    const nextNameIndex = childrenNumber && numberOfProperties ? childrenNumber / numberOfProperties + 1 : 1;
    const mappingRowsForProperties = isListObjectType
      ? createMappingRowsForObjectProperties(selectedRow, currentSchema, nextNameIndex)
      : createMappingRowForListRow(selectedRow, formatRow, nextNameIndex);

    const propertyIndexInMappingModelList = selectedRowIndex + childrenNumber + 1;
    (mapping.value as DynamicMappingDetailsResponse).MappingModelList.splice(
      propertyIndexInMappingModelList,
      0,
      ...mappingRowsForProperties
    );
    (defaultMapping.value as DynamicMappingDetailsResponse).MappingModelList.splice(
      propertyIndexInMappingModelList,
      0,
      ...mappingRowsForProperties
    );
    isLoading.value = false;
  };

  const createMappingRowForListRow = (
    selectedRow: MappingFieldResponse | DynamicMappingFieldResponse,
    formatField: DynamicFormatRow,
    nextNameIndex: number
  ): DynamicMappingFieldResponse[] => {
    const name = selectedRow.FormatField;
    const mappingRow = createMappingRow(formatField, selectedRow as DynamicMappingFieldResponse);
    mappingRow.FormatField = `${name}_${nextNameIndex}`;
    mappingRow.FormatFieldId = null; // TODO: change to `${name}_${nextNameIndex}`
    mappingRow.listItemOf = selectedRow.FormatField;
    const isEnum = selectedRow.FormatDataType === RowTargetType.LIST_ENUM;
    mappingRow.FormatDataType = isEnum ? RowTargetTypeDisplayValue.ENUM : RowTargetTypeDisplayValue.STRING;
    mappingRow.EnumerationValues = '';
    return [mappingRow];
  };

  const createMappingRowsForObjectProperties = (
    selectedRow: MappingFieldResponse | DynamicMappingFieldResponse,
    currentSchema: ObjectSchema,
    nextNameIndex: number
  ): DynamicMappingFieldResponse[] => {
    const mappingRowsForProperties = [] as DynamicMappingFieldResponse[];
    const propertyNamePrefix = selectedRow.FormatField;
    for (const property in currentSchema.properties) {
      const currentProperty = currentSchema.properties[property];
      if (!currentProperty) {
        isLoading.value = false;
        return [];
      }

      const mappingRow = createMappingRow(property, selectedRow as DynamicMappingFieldResponse);
      mappingRow.FormatField = `${propertyNamePrefix}_${nextNameIndex}_${property}`;
      mappingRow.FormatFieldId = null; // TODO: change to `${propertyNamePrefix}_${nextNameIndex}_${property}`;
      mappingRow.listItemOf = selectedRow.FormatField;
      if (currentProperty.type === RowTargetType.STRING) {
        if (Object.keys(currentProperty).includes(RowTargetType.ENUM)) {
          mappingRow.FormatDataType = RowTargetTypeDisplayValue.ENUM;
          const enumerations = currentProperty[RowTargetType.ENUM].map((x) => {
            return {
              EnumValue: x,
            } as FormatEnum;
          });
          mappingRow.Enumerations = enumerations ?? [];
        } else {
          mappingRow.FormatDataType = RowTargetTypeDisplayValue.STRING;
        }
      } else {
        mappingRow.FormatDataType = currentProperty.type;
      }

      mappingRowsForProperties.push(mappingRow);
    }
    return mappingRowsForProperties;
  };

  const removeListItem = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse): void => {
    const formatRow = getFormatFieldForSelectedRow(selectedRow);
    if (!formatRow || !mapping.value) {
      return;
    }

    const selectedRowIndex = getSelectedRowIndex(selectedRow);
    if (selectedRowIndex < 0) {
      return;
    }

    const childrenNumber = getNumberOfChildListItems(selectedRow);
    if (!childrenNumber) {
      return;
    }

    isLoading.value = true;
    const currentSchema = formatRow.objectSchema;
    const isListObjectType = currentSchema && selectedRow.FormatDataType === RowTargetType.LIST_OBJECT;
    const numberOfProperties = isListObjectType ? getNumberOfProps(currentSchema.properties) : 1;
    const propertyIndexInMappingModelList = selectedRowIndex + childrenNumber - numberOfProperties + 1;
    (mapping.value as DynamicMappingDetailsResponse).MappingModelList.splice(
      propertyIndexInMappingModelList,
      numberOfProperties
    );
    (defaultMapping.value as DynamicMappingDetailsResponse).MappingModelList.splice(
      propertyIndexInMappingModelList,
      numberOfProperties
    );
    isLoading.value = false;
  };

  const getNumberOfChildListItems = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse): number =>
    mapping.value ? mapping.value.MappingModelList.filter((x) => x.listItemOf === selectedRow.FormatField).length : 0;

  const getNumberOfProps = (properties: ObjectSchemaProperties) => Object.keys(properties).length;

  const getCurrentNumberOfChildren = (selectedRow: MappingFieldResponse | DynamicMappingFieldResponse): number => {
    const numberOfChilds = getNumberOfChildListItems(selectedRow);
    if (!numberOfChilds) {
      return 0;
    }

    const formatField = getFormatFieldForSelectedRow(selectedRow);
    if (!formatField) {
      return 0;
    }
    const currentSchema = formatField.objectSchema;
    const numberOfProperties = currentSchema ? getNumberOfProps(currentSchema.properties) : 1;
    return numberOfChilds / numberOfProperties;
  };

  const convertEnumerationValuesToEnumerationsForExistingMapping = () => {
    if (!mapping.value) {
      return;
    }

    mapping.value.MappingModelList.map((x) => {
      if (x['EnumerationValues'] && !x.Enumerations?.length) {
        x.Enumerations = convertToCoreEnumFormats(x['EnumerationValues']);
      }
    });
  };

  const initializeMissingPropertiesForExistingMapping = () => {
    if (!mapping.value) {
      return;
    }

    mapping.value.MappingModelList.map((x) => {
      if (x['inRiverFieldTypeId'] === undefined) {
        x['inRiverFieldTypeId'] = null;
      }
      if (x['ConverterArgs'] === undefined) {
        x['ConverterArgs'] = null;
      }
    });
  };

  const convertToCoreEnumFormats = (targetEnumValues: string): FormatEnum[] =>
    targetEnumValues?.split(';').map((x) => {
      return {
        EnumValue: x,
      } as FormatEnum;
    }) ?? [];

  const toInriverFieldTypes = (responseModels: InriverFieldTypeResponse[]): InriverFieldType[] => {
    if (!responseModels?.length) {
      [] as InriverFieldType[];
    }

    return responseModels.map((x) => {
      return {
        id: x.id,
        cvlId: x.cvlid,
        dataType: x.datatype,
        displayName: x.displayname,
        entityTypeId: x.entitytypeid,
        fullDisplayName: `${x.id} (${x.entitytypeid})`,
        cvl: x.cvlid ? getCvlById(x.cvlid) : undefined,
      } as InriverFieldType;
    });
  };

  const fetchFieldTypes = async () => {
    if (!mapping.value) {
      return;
    }

    const workAreaEntityTypeFields = await getFieldTypes(mapping.value.WorkareaEntityTypeId);
    entityTypesDictionary.value.push({
      entityType: mapping.value.WorkareaEntityTypeId,
      fieldTypes: toInriverFieldTypes(workAreaEntityTypeFields),
    } as GroupedFieldType);

    if (mapping.value.FirstRelatedEntityTypeId) {
      const firstRelationEntityTypeFields = await getFieldTypes(mapping.value.FirstRelatedEntityTypeId);
      entityTypesDictionary.value.push({
        entityType: mapping.value.FirstRelatedEntityTypeId,
        fieldTypes: toInriverFieldTypes(firstRelationEntityTypeFields),
      } as GroupedFieldType);
    }

    if (mapping.value.SecondRelatedEntityTypeId) {
      const secondRelationEntityTypeFields = await getFieldTypes(mapping.value.SecondRelatedEntityTypeId);
      entityTypesDictionary.value.push({
        entityType: mapping.value.SecondRelatedEntityTypeId,
        fieldTypes: toInriverFieldTypes(secondRelationEntityTypeFields),
      } as GroupedFieldType);
    }

    entityTypesDictionary.value.push({
      entityType: 'Resources',
      fieldTypes: getResourceFields(mapping.value as MappingDetailsResponse),
    });
  };

  const mapAutomatically = async () => {
    isLoading.value = true;
    if (!mapping.value) {
      return;
    }

    const payload = {
      inputs: mapping.value.MappingModelList.map((x) => x.FormatField),
      optionIds: entityTypesDictionary.value.flatMap((x) => x.fieldTypes.flatMap((y) => [y.id, y.id])),
      options: entityTypesDictionary.value.flatMap((x) => x.fieldTypes.flatMap((y) => [y.displayName, y.id])),
      threshold: 1.5,
    } as MappingSuggestionModel;
    const suggestions = await getSuggestions(payload);
    suggestions.forEach((x, index) => {
      if (!x.length) {
        return;
      }

      const fieldTypeId = x[0].Key;
      if (!mapping.value) {
        return;
      }

      if (!mapping.value.MappingModelList[index].inRiverFieldTypeId) {
        const [fieldType] = entityTypesDictionary.value.flatMap((x) =>
          x.fieldTypes.filter((y) => y.id === fieldTypeId)
        );
        if (!fieldType) {
          return;
        }

        mapping.value.MappingModelList[index].inRiverDataType = fieldType.dataType;
        mapping.value.MappingModelList[index].inRiverEntityTypeId = fieldType.entityTypeId;
        mapping.value.MappingModelList[index].inRiverFieldTypeId = fieldTypeId;
      }
    });
    isLoading.value = false;
  };

  const getResourceFields = (mapping: MappingDetailsResponse): InriverFieldType[] => {
    const resourceFields: InriverFieldType[] = [];
    ResourceFieldTypes.map((resourceType) => {
      resourceFields.push({
        dataType: 'Resource',
        displayName: `${mapping.WorkareaEntityTypeId}${resourceType}`,
        id: `${mapping.WorkareaEntityTypeId}${resourceType}`,
      } as InriverFieldType);
    });

    mapping.FirstRelatedEntityTypeId &&
      ResourceFieldTypes.map((resourceType) => {
        resourceFields.push({
          dataType: 'Resource',
          displayName: `${mapping.FirstRelatedEntityTypeId}${resourceType}`,
          id: `${mapping.FirstRelatedEntityTypeId}${resourceType}`,
        } as InriverFieldType);
      });

    mapping.SecondRelatedEntityTypeId &&
      ResourceFieldTypes.map((resourceType) => {
        resourceFields.push({
          dataType: 'Resource',
          displayName: `${mapping.SecondRelatedEntityTypeId}${resourceType}`,
          id: `${mapping.SecondRelatedEntityTypeId}${resourceType}`,
        } as InriverFieldType);
      });
    return resourceFields;
  };

  const getDefaultMappingLanguage = (): Language | null => {
    return mapping.value?.DefaultLanguage && languages.value
      ? languages.value.find((language) => language.Name === mapping.value?.DefaultLanguage) || null
      : null;
  };

  const setDefaultMappingLanguage = (language: Language, applyToAll: Boolean) => {
    if (!mapping.value) {
      return;
    }

    mapping.value.DefaultLanguage = language.Name;
    mapping.value.MappingModelList.forEach((mappingField) => {
      if (isLocaleStringField(mappingField) && (applyToAll || (!applyToAll && !mappingField.ConverterId))) {
        linkChooseLanguageFunctionToField(mappingField, language);
      }
    });
  };

  const isLocaleStringField = (field: MappingFieldResponse): boolean =>
    field.inRiverDataType === InriverDataType.LOCALE_STRING;

  const linkChooseLanguageFunctionToField = (mappingField: MappingFieldResponse, language: Language) => {
    const functionSettings = getChooseLanguageFunctionSettings(language);
    const functionId = getChooseLanguageFunctionId();
    if (!functionId) {
      console.error('Choose language function not found');
      return;
    }

    mappingField.ConverterArgs = JSON.stringify({
      transformations: [functionSettings],
    });
    mappingField.ConverterId = functionId;
  };

  const saveChanges = async (mappingId: number): Promise<boolean> => {
    isLoading.value = true;
    try {
      if (!mapping.value) {
        throw new Error('mapping can not be undefined');
      }

      const result = await updateMapping(mapping.value as MappingDetailsResponse);
      if (result) {
        const update = true;
        await fetchMappingDetails(mappingId, update);

        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const saveDynamicMappingChanges = async (): Promise<boolean> => {
    isLoading.value = true;
    try {
      if (!mapping.value || !dynamicMapping.value) {
        throw new Error('mapping can not be undefined');
      }

      dynamicMapping.value.data = JSON.stringify(mapping.value);
      const result = await updateDynamicMapping(dynamicMapping.value as DynamicMappingResponse);
      const mappingId = dynamicMapping.value.id;
      if (result) {
        const update = true;
        await fetchDynamicMappingDetails(mappingId, update);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  const getCvlById = (cvlId: string): Cvl | undefined => {
    return cvls.value.find((x) => x.Id === cvlId);
  };

  const clearStore = () => {
    mapping.value = undefined;
    isLoading.value = false;
    entityTypesDictionary.value = [];
    cvls.value = [];
    category.value = undefined;
    dynamicMapping.value = undefined;
    defaultMapping.value = undefined;
  };

  const checkIfRowHasChanges = (row: MappingFieldResponse, index: number): boolean => {
    const defaultRow = defaultMapping.value?.MappingModelList?.[index];
    if (!defaultRow) {
      return false;
    }

    // TODO: Try to use a strict comparison (===), here we compare null and undefined now
    const sourceFieldHasChanged =
      defaultRow.inRiverDataType != row.inRiverDataType ||
      defaultRow.inRiverEntityTypeId != row.inRiverEntityTypeId ||
      defaultRow.inRiverFieldTypeId != row.inRiverFieldTypeId;
    const functionHasChanged = defaultRow.ConverterArgs != row.ConverterArgs;

    return sourceFieldHasChanged || functionHasChanged;
  };

  return {
    isLoading,
    mapping,
    defaultMapping,
    entityTypesDictionary,
    hasUnsavedChanges,
    isSelectedRowCustomType,
    fetchMappingDetails,
    fetchDynamicMappingDetails,
    fetchCategoryForDynamicMapping,
    initDynamicMappingModelList,
    addListItem,
    removeListItem,
    getDefaultMappingLanguage,
    setDefaultMappingLanguage,
    getCurrentNumberOfChildren,
    getFormatFieldForSelectedRow,
    isLocaleStringField,
    linkChooseLanguageFunctionToField,
    clearStore,
    saveChanges,
    saveDynamicMappingChanges,
    mapAutomatically,
    getCvlById,
    checkIfRowHasChanges,
    toInriverFieldTypes,
  };
});
