import { FunctionSettings } from '@core/interfaces/FieldMapping/Functions';
import { DefaultFunctionsName } from '@core/enums';

/*
  [relatedEntityType], // selected entity type id
  [fieldTypeId], // selected field type id
  [language], // if selected fieldTypeId is a local string field otherwise it's empty string
  [linkEntityTypeIds], // the chain of link entity type ids from top to bottom (first relation linkEntityTypeId second relation linkEntityTypeId from current mapping + linkEntityTypeIds from selection)
  [directionsPath], // directionsPath where 'parent' ? 0 : 1 (first relation direction(parent/child) second relation direction(parent/child) from current mapping + linkEntityTypeIds directions(parent/child) from selection)
  relations JSON object : { mainEntityTypeId, relations: { [relation index]: { direction, dropdownValue: { entityTypeId, linkEntityTypeId }, }}, fieldTypeId, language, entityId }
  [entityId] // selected entity id
*/

export type RelatedEntityFieldArgs = [string, string, string, string, string, string, string];
export type RelatedEntityFieldValues = [];

export class RelatedEntityFieldSettings implements FunctionSettings<RelatedEntityFieldArgs, RelatedEntityFieldValues> {
  constructor(args: RelatedEntityFieldArgs, values: RelatedEntityFieldValues) {
    this.name = DefaultFunctionsName.RelatedEntityField;
    this.args = args;
    this.values = values;
  }

  name: string;
  args: RelatedEntityFieldArgs;
  values: RelatedEntityFieldValues;
}
/*
example of JSON object:
  "ChannelNode",
  "ChannelNodeName",
  "en",
  "ChannelNodeProduct,ChannelNodeItem,ItemResource,ChannelResource,ChannelChannelNode",
  "0,1,1,0,1",
  "{\"mainEntityTypeId\":\"Item\",
  \"relations\":{
  \"1\":{\"direction\":\"child\",\"dropdownValue\":\"{\\\"entityTypeId\\\":\\\"Resource\\\",\\"linkEntityTypeId\\\":\\\"ItemResource\\\"\"},
  \"2\":{\"direction\":\"parent\",\"dropdownValue\":\"{\\\"entityTypeId\\\":\\\"Channel\\\",\\"linkEntityTypeId\\\":\\\"ChannelResource\\\"}"},
  \"3\":{\"direction\":\"child\",\"dropdownValue\":\"{\\\"entityTypeId\\\":\\\"ChannelNode\\\",\\"linkEntityTypeId\\\":\\\"ChannelChannelNode\\\"}\"}},
  \"fieldTypeId\":\"ChannelNodeName\",\"language\":\"en\",\"entityId\":\"23984\"}",
  "23984"
*/
