import { outputAdapterClient } from '@core/Utils';
import { DynamicMappingResponse, DynamicMappingToSave, NewMapping } from '@core/interfaces';
import { getEnvironmentGlobalId } from '@helpers/EnvironmentHelper';
import { getUserName } from '@helpers';
import { convertDateToLocalFormat } from '@services/helper/dateHelpers';

export const fetchDynamicMappingsByFormatId = async (formatFileId: number): Promise<DynamicMappingResponse[]> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/Mappings?environmentFormatId=${formatFileId}`;
  const response = await outputAdapterClient.get<DynamicMappingResponse[]>(url, 'Error fetching dynamic mappings');
  return (
    response.data
      ?.filter((x) => x.environmentFormatId === `${formatFileId}`)
      .map((item) => toDynamicMappingResponse(item)) ?? []
  );
};

export const fetchDynamicMappingById = async (mappingId: number): Promise<DynamicMappingResponse> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/Mappings/${mappingId}`;
  const response = await outputAdapterClient.get<DynamicMappingResponse>(url, 'Error fetching dynamic mapping');
  return response.data ? toDynamicMappingResponse(response.data) : ({} as DynamicMappingResponse);
};

export const saveDynamicMapping = async (
  formatFileId: number,
  dynamicMapping: NewMapping
): Promise<DynamicMappingResponse> => {
  const environmentGid = getEnvironmentGlobalId();
  const userName = getUserName();
  const url = `/api/environments/${environmentGid}/Mappings`;
  const dynamicMappingToSave = {
    name: dynamicMapping.MappingName,
    data: JSON.stringify(dynamicMapping),
    createdBy: userName,
    environmentFormatId: formatFileId,
  } as DynamicMappingToSave;
  const response = await outputAdapterClient.post<DynamicMappingResponse>(
    url,
    dynamicMappingToSave,
    'Error saving dynamic mapping'
  );
  return response.data ?? ({} as DynamicMappingResponse);
};

export const updateDynamicMapping = async (dynamicMapping: DynamicMappingResponse): Promise<boolean> => {
  const environmentGid = getEnvironmentGlobalId();
  const id = dynamicMapping.id;
  const url = `/api/environments/${environmentGid}/Mappings/${id}`;
  const userName = getUserName();
  dynamicMapping.updatedBy = userName ?? '';
  const response = await outputAdapterClient.put(url, dynamicMapping, 'Error updating dynamic mapping');
  return response.ok;
};

export const deleteDynamicMapping = async (mappingId: number): Promise<boolean> => {
  const environmentGid = getEnvironmentGlobalId();
  const url = `/api/environments/${environmentGid}/Mappings/${mappingId}`;
  const response = await outputAdapterClient.delete(url, 'Error deleting dynamic mapping');
  return response.ok;
};

const toDynamicMappingResponse = (item: DynamicMappingResponse): DynamicMappingResponse => {
  const defaultDate = '0001-01-01T00:00:00';
  return {
    name: item.name,
    data: item.data,
    createdBy: item.createdBy,
    updatedBy: item.updatedBy,
    id: item.id,
    tradingPartnerId: item.tradingPartnerId,
    category: item.category,
    createdDate: convertDateToLocalFormat(item.createdDate) ?? '',
    updatedDate: item.updatedDate && item.updatedDate !== defaultDate ? convertDateToLocalFormat(item.updatedDate) : '',
    environmentFormatId: item.environmentFormatId,
    enableSKU: item.data,
  } as DynamicMappingResponse;
};
