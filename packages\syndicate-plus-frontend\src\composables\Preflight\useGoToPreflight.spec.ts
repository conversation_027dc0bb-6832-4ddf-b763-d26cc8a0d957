import { vi, it, describe, expect, beforeEach, Mock } from 'vitest';
import { ref } from 'vue';
import { useGoToPreflight } from '@composables/Preflight';
import { ProductGrouping, SyndicationPageTabNames } from '@enums';
import { CollectionDetails } from '@customTypes/CollectionDetails';
import { ProductFilterDto } from '@dtos/ProductFilterDto';
import { useRouter } from '@composables/useRouter';

vi.mock('@composables/useRouter');

describe('useGoToPreflight', () => {
  const mockRouter = {
    goToPage: vi.fn(),
  };
  const useRouterNew = useRouter as Mock;

  beforeEach(() => {
    useRouterNew.mockReturnValue(mockRouter);
  });

  it('calls goToPage for collection tab correctly', async () => {
    // Arrange
    const tabName = ref(SyndicationPageTabNames.Collection);
    const selectAllProductsFilterApplied = ref(false);
    const searchValue = ref('search');
    const filter = ref(new ProductFilterDto(['a', 'b']));
    filter.value.groupBy.id = ProductGrouping.CUSTOM;
    const selectedCollection = ref({ id: 1 } as CollectionDetails);
    const productIds = ['a', 'b', 'c'];
    const { goToPreflightPage } = useGoToPreflight(
      filter,
      tabName,
      selectedCollection,
      selectAllProductsFilterApplied,
      searchValue
    );

    // Act
    goToPreflightPage(productIds);

    // Assert
    expect(mockRouter.goToPage).toHaveBeenCalledWith('preflight-page', null, {
      selectAllProductsFilterApplied: true,
      groupBy: ProductGrouping.CUSTOM,
      collections: [selectedCollection.value.id],
    });
  });

  it('calls goToPage for products tab correctly if all products are selected', async () => {
    // Arrange
    const tabName = ref(SyndicationPageTabNames.Products);
    const selectAllProductsFilterApplied = ref(true);
    const searchValue = ref('search');
    const filter = ref(new ProductFilterDto(['a', 'b']));
    filter.value.groupBy.id = ProductGrouping.CUSTOM;
    filter.value.collections = ['e', 'f'];
    filter.value.productTypes = ['g', 'h'];
    const selectedCollection = ref({ id: 1 } as CollectionDetails);
    const productIds = ['a', 'b', 'c'];
    const { goToPreflightPage } = useGoToPreflight(
      filter,
      tabName,
      selectedCollection,
      selectAllProductsFilterApplied,
      searchValue
    );

    // Act
    goToPreflightPage(productIds);

    // Assert
    expect(mockRouter.goToPage).toHaveBeenCalledWith('preflight-page', null, {
      selectAllProductsFilterApplied: true,
      groupBy: ProductGrouping.CUSTOM,
      brands: ['a', 'b'],
      collections: ['e', 'f'],
      productTypes: ['g', 'h'],
      searchValue: searchValue.value,
    });
  });

  it('calls goToPage for products tab correctly if indivadual products are selected', async () => {
    // Arrange
    const tabName = ref(SyndicationPageTabNames.Products);
    const selectAllProductsFilterApplied = ref(false);
    const searchValue = ref('search');
    const filter = ref(new ProductFilterDto(['a', 'b']));
    filter.value.groupBy.id = ProductGrouping.CUSTOM;
    filter.value.collections = ['e', 'f'];
    filter.value.productTypes = ['g', 'h'];
    const selectedCollection = ref({ id: 1 } as CollectionDetails);
    const productIds = ['a', 'b', 'c'];
    const { goToPreflightPage } = useGoToPreflight(
      filter,
      tabName,
      selectedCollection,
      selectAllProductsFilterApplied,
      searchValue
    );

    // Act
    goToPreflightPage(productIds);

    // Assert
    expect(mockRouter.goToPage).toHaveBeenCalledWith('preflight-page', null, {
      ids: 'a,b,c',
      groupBy: ProductGrouping.CUSTOM,
    });
  });
});
