import { ref, Ref, computed } from 'vue';

export default function useProductRowSelect(isEditingMode: Ref<boolean>) {
  // Refs
  const selectedRow = ref<any>();
  const detailsButtonRef = ref<any>(null);
  const isProductDetailsButtonDisabled = ref<boolean>();

  // Computed
  const isProductDetailsButtonVisible = computed<boolean>(() => {
    return selectedRow.value?.length && !isEditingMode.value;
  });

  // Functions
  const onRowClick = (row: any): void => {
    if (isEditingMode.value) {
      return;
    }

    isProductDetailsButtonDisabled.value = false;
    selectedRow.value = [row];
  };

  return {
    selectedRow,
    isProductDetailsButtonVisible,
    isProductDetailsButtonDisabled,
    detailsButtonRef,
    onRowClick,
  };
}
