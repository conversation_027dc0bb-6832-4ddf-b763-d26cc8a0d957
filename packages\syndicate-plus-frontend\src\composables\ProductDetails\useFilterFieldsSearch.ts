import { ref, computed, ComputedRef, Ref } from 'vue';
import { FilterField } from '@customTypes/Products';

export default function useFilterFieldsSearch(allColumns: ComputedRef<FilterField[]>, showInternalNames: Ref<boolean>) {
  // Refs
  const searchValue = ref<string>('');

  // Computed
  const filteredColumns = computed(() =>
    showInternalNames.value
      ? allColumns.value.filter((x) =>
          x.internalName?.toLocaleLowerCase().includes(searchValue.value?.toLocaleLowerCase())
        )
      : allColumns.value.filter(
          (x) =>
            x.name?.toLocaleLowerCase().includes(searchValue.value?.toLocaleLowerCase()) ||
            x.displayName?.toLocaleLowerCase().includes(searchValue.value?.toLocaleLowerCase())
        )
  );

  return {
    searchValue,
    filteredColumns,
  };
}
