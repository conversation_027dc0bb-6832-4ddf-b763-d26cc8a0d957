<template>
  <div class="mx-auto min-w-1000px w-1000px single-image-spec-preview">
    <div class="flex justify-center flex-col">
      <div class="main-headline ml-20px">{{ module.data.headline1 }}</div>
      <div class="flex flex-row flex-nowrap">
        <div class="left-column m-10px">
          <c-caption-angle-image-block-preview
            :image-data="module?.data.image"
            image-data-class="image mb-10px ml-5px mr-20px"
            data-testid="single-image-specs-details-preview"
          />
        </div>
        <div class="middle-column m-10px pt-10px">
          <h3 class="headline">{{ module.data.headline2 }}</h3>
          <p class="subheadline">{{ module.data.subheadline1 }}</p>
          <c-html-preview :html="module.data.body1" />
          <p class="subheadline mt-10px">{{ module.data.subheadline2 }}</p>
          <c-html-preview :html="module.data.body2" />
        </div>
        <div class="right-column m-10px pl-10px">
          <h3 class="headline">{{ module.data.headline3 }}</h3>
          <p class="subheadline">{{ module.data.subheadline3 }}</p>
          <c-bullet-list-preview
            v-if="module.data.bullets.length"
            class="bullet-list"
            :bullet-list="module.data.bullets"
            :bullet-checkmark="true"
          />
          <p class="subheadline">{{ module.data.subheadline4 }}</p>
          <c-html-preview :html="module.data.body3" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, SingleImageSpecData } from '@customTypes/Aplus';
import { CBulletListPreview, CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<SingleImageSpecData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.single-image-spec-preview {
  :deep() {
    .image {
      width: 300px;
      height: 300px;
      min-width: 300px;
      min-height: 300px;
      object-fit: unset;
    }
  }

  .bullets {
    background-color: var(--color-grey-10);
    border: 1px solid var(--color-grey-light);
  }

  .left-column {
    width: 300px;
  }

  .middle-column {
    width: 380px;
  }

  .right-column {
    width: 230px;
  }

  .sidebar-body {
    font-size: 13px;
  }

  .bullet-list {
    background-color: var(--surface-color);
    border: none;
    margin-top: 0px;
    :deep(.bullet-text) {
      font-weight: 700;
    }
  }

  .main-headline {
    font-size: 18px;
    font-weight: bold;
  }

  .headline {
    font-size: 14px;
    font-weight: bold;
  }

  .subheadline {
    font-size: 13px;
    color: var(--color-grey-dark);
  }
}
</style>
