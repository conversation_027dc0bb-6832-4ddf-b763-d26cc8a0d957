<template>
  <div ref="productCard" class="c-product-card">
    <div class="card-badge-container">
      <q-badge
        v-if="product.isUpdatedSinceLastSyndication"
        rounded
        :label="$t('syndicate_plus.syndication.updated')"
        class="card-badge updated"
      />
      <q-badge
        v-if="product.isAplus"
        rounded
        :label="$t('syndicate_plus.syndication.aplus')"
        class="card-badge aplus"
      />
    </div>
    <c-small-card
      class="c-product-small-card"
      :image-src="imgSrc"
      icon="mdi-image-outline"
      icon-size="52"
      background-color="white"
      :text="product.name"
      :description="getProductDescription()"
      :selected="isSelected"
    />
    <p class="brand-name">
      {{ product.brandName }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ProductGrouping } from '@enums';
import { Product } from '@customTypes/product';
import { onBeforeMount, onMounted, ref } from 'vue';

const props = defineProps({
  product: {
    type: Object as () => Product,
    required: true,
  },
  isSelected: {
    type: Boolean,
    required: true,
    default: false,
  },
  groupBy: {
    type: String as () => ProductGrouping,
    required: true,
    default: ProductGrouping.SKU,
  },
});

const productCard = ref();
const imgSrc = ref();

onBeforeMount(() => {
  imgSrc.value = props.product.imageUrl ? props.product.imageUrl + '/thumbnail' : props.product.imageUrl;
});

onMounted(() => {
  if (!productCard.value) {
    return;
  }

  const cardImages = getCardImages();
  cardImages?.length && setOnError(cardImages);
});

function handleImageSrcError() {
  imgSrc.value = props.product.imageUrl;
  const cardImages = getCardImages();
  cardImages?.length && removeOnError(cardImages);
}

function getProductDescription() {
  if (props.groupBy == ProductGrouping.CUSTOM) {
    return props.product.customGroup;
  } else if (props.groupBy == ProductGrouping.SKU && props.product.sku?.length > 0) {
    return props.product.sku[0];
  } else if (props.groupBy == ProductGrouping.UPC) {
    return props.product.upc;
  }

  return '';
}

function getCardImages() {
  return (productCard.value as HTMLImageElement)?.getElementsByTagName('img');
}

function setOnError(cardImages: HTMLCollectionOf<HTMLImageElement>) {
  Array.from(cardImages).forEach((cardImage) => (cardImage.onerror = handleImageSrcError));
}

function removeOnError(cardImages: HTMLCollectionOf<HTMLImageElement>) {
  Array.from(cardImages).forEach((cardImage) => (cardImage.onerror = null));
}
</script>

<style lang="scss" scoped>
.c-product-card {
  $card-width: 193px;
  position: relative;

  :deep(.content) {
    width: $card-width !important;
    height: $card-width !important;

    img {
      width: inherit !important;
      height: inherit !important;
      object-fit: contain !important;
    }

    .q-icon {
      color: var(--color-grey);
    }
  }

  :deep(.description) {
    color: var(--color-grey-dark);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: $card-width;
  }

  :deep(.text) {
    width: $card-width;
    margin-top: 4px;
  }

  .card-badge-container {
    position: absolute;
    top: 11px;
    left: 10px;
    z-index: 1;

    .card-badge {
      color: var(--color-black);
      line-height: 14.4px;
      letter-spacing: 0.25px;
      height: 24px;
      font-size: 12px;

      &.updated {
        background-color: var(--color-green-light);
        padding: 3px 12px;
        margin-right: 4px;
      }

      &.aplus {
        background-color: var(--color-yellow);
        padding: 3px 8px;
      }
    }
  }

  .brand-name {
    color: var(--color-grey-dark);
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: $card-width;
    margin-bottom: 0px;
  }
}
</style>
