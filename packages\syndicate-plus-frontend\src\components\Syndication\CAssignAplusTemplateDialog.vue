<template>
  <c-dialog
    v-model="showDialog"
    :is-loading="isLoading"
    class="c-dialog"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <div class="row q-gutter-lg">
      <div class="col section">
        <h3>{{ $t('syndicate_plus.syndication.assign_aplus_dialog.title') }}</h3>
        <c-select
          v-model="selectedTemplate"
          :label="$t('syndicate_plus.common.template')"
          :options="templates"
          option-value="aplusTemplateId"
          option-label="templateName"
          dense
          clearable
        />
        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.common.assign')"
            :disabled="isAssignTemplateButtonDisabled"
            @click="logEventAndClick(ActionName.CONFIRM_ASSIGN_APLUS_TEMPLATE, assignTemplate)"
          >
            <q-tooltip v-if="isAssignTemplateButtonDisabled">
              {{ $t('syndicate_plus.syndication.assign_aplus_dialog.confirm_disabled_tooltip') }}
            </q-tooltip>
          </c-btn>
        </div>
      </div>
      <div class="col section">
        <h3>{{ $t('syndicate_plus.syndication.unassign_aplus_dialog.title') }}</h3>
        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.syndication.unassign_aplus_dialog.unassign')"
            @click="logEventAndClick(ActionName.CONFIRM_UNASSIGN_APLUS_TEMPLATE, unassignTemplate)"
          />
        </div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { APlusTemplate } from '@customTypes/Aplus';
import { computed, onBeforeMount, ref } from 'vue';
import { useDialog } from '@inriver/inri';
import { ActionName, PageName } from '@enums';
import { useAppInsightsStore } from '@stores';
import TemplateService from '@services/TemplateService';

const emit = defineEmits(['on-template-assign', 'on-template-unassign']);

// Refs
const isLoading = ref<boolean>(false);
const templates = ref<APlusTemplate[]>([]);
const selectedTemplate = ref<APlusTemplate | undefined>(undefined);

// Computed
const isAssignTemplateButtonDisabled = computed(() => !selectedTemplate.value);

// Composables
const { logEventAndClick } = useAppInsightsStore();
const { setScreenName } = useAppInsightsStore();
const { showDialog, cancel } = useDialog();

// Functions
const assignTemplate = (): void => {
  emit('on-template-assign', selectedTemplate.value);
};

const unassignTemplate = (): void => {
  emit('on-template-unassign');
};

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.A_PLUS_DIALOG);
  // TODO: check limit and offset and other params for the request body
  isLoading.value = true;
  templates.value = await TemplateService.getAPlusTemplates([], 'SKU', 100, 1, [{}]);
  isLoading.value = false;
});
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }

  .action-buttons {
    padding-top: 10px;
  }
}
</style>
