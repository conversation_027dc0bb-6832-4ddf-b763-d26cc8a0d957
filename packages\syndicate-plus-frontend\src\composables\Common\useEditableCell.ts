import { Ref, computed } from 'vue';
import { ColumnDataType } from '@enums';
import getLanguage from '@services/helper/getLanguage';
import { convertTimestampToDate } from '@services/helper/dateHelpers';

export default function useEditableCell(
  newValue: Ref<string | null>,
  isInputFocused: Ref<boolean>,
  dataType: ColumnDataType
) {
  // Computed
  const displayValue = computed(() => {
    if (dataType === ColumnDataType.DATE && newValue.value) {
      const parsedDate = convertTimestampToDate(parseInt(newValue.value));
      if (!parsedDate) {
        return isInputFocused.value ? '' : '-';
      }

      return parsedDate;
    }

    if (dataType === ColumnDataType.CURRENCY && newValue.value) {
      return isInputFocused.value
        ? newValue.value
        : Intl.NumberFormat(getLanguage(), { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(
            parseFloat(newValue.value)
          );
    }

    return (newValue.value === '' || newValue.value === null) && !isInputFocused.value ? '-' : newValue.value;
  });

  // Functions
  const setValue = (value): void => {
    if (dataType === ColumnDataType.DATE && value) {
      const date = Date.parse(value);
      newValue.value = isNaN(date) ? '' : date.toString();

      return;
    }

    newValue.value = value;
  };

  const validateValue = (e) => {
    if (dataType === ColumnDataType.CURRENCY) {
      const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
      const { value, selectionStart, selectionEnd } = e.target;
      const key = e.key;
      if (allowedKeys.includes(key)) {
        return;
      }

      if ((key === 'v' && e.ctrlKey) || e.type === 'paste') {
        return e.preventDefault();
      }

      const hasSelection = selectionStart !== selectionEnd;
      const newValue = value.slice(0, selectionStart) + key + value.slice(selectionStart);

      const isValidFormat = /^\d+(\.\d{0,2})?$/.test(newValue);
      if (!isValidFormat && !hasSelection) {
        e.preventDefault();
      }
    }
  };

  return { setValue, validateValue, displayValue };
}
