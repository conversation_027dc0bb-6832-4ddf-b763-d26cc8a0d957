import { computed, Ref, ref } from 'vue';
import {
  SpecificationFieldSettings,
  SpecificationFieldArgs,
  SpecificationFieldValues,
  ConverterTransformation,
  DropdownOption,
} from '@core/interfaces/FieldMapping/Functions';
import { useConverterArgsParser } from '@core/composables/FieldMapping/Functions';
import { Language } from '@customTypes/AppData';
import { Entity, RelationLink } from '@core/interfaces';
import { SpecificationField } from '@core/interfaces/Specification';
import { InriverDataType } from '@core/enums';

export default function useSpecificationFieldFunction(
  relations: Ref<RelationLink[]>,
  templatesCache: Ref<{ [sourceEntityTypeId: string]: Entity[] }>,
  templateFieldsCache: Ref<{ [templateId: number]: SpecificationField[] }>,
  getCvlById: Function,
  fetchTemplateFields: Function,
  languages: Ref<Language[]>,
  converterArgs?: string
) {
  // Composables
  const { transformatedFunction } = useConverterArgsParser<SpecificationFieldSettings>(converterArgs);

  // Refs
  const selectedFilterValue = ref<RelationLink>();
  const selectedTemplate = ref<DropdownOption<string, number>>();
  const selectedField = ref<SpecificationField>();
  const includeUnit = ref<boolean>(false);
  const selectedLanguage = ref<Language>();

  // Computed
  const settings = computed(() => {
    if (!selectedFilterValue.value || !selectedTemplate.value || !selectedField.value) {
      return {} as ConverterTransformation;
    }

    const args: SpecificationFieldArgs = [
      selectedFilterValue.value.id,
      selectedFilterValue.value.sourceEntityTypeId,
      selectedTemplate.value.value.toString(),
      selectedField.value.id,
      selectedLanguage.value?.Name ?? '',
      includeUnit.value ? selectedField.value?.unit ?? '' : '',
    ];
    const values: SpecificationFieldValues = [];

    return { function: new SpecificationFieldSettings(args, values) } as ConverterTransformation;
  });

  const confirmIsDisabled = computed(
    () =>
      !selectedField.value ||
      !selectedTemplate.value ||
      (selectedFieldTypeIsLocaleString.value && !selectedLanguage.value)
  );

  const hasUnit = computed(() => !!selectedField.value?.unit);

  const templates = computed(() => {
    return Object.keys(templatesCache.value)
      ?.filter((sourceEntityTypeId) => {
        if (!selectedFilterValue.value) {
          return false;
        }

        if (selectedFilterValue.value.id === 'filterAll') {
          return true;
        }

        if (selectedFilterValue.value.sourceEntityTypeId === sourceEntityTypeId) {
          return true;
        }

        return false;
      })
      ?.flatMap((sourceEntityTypeId) => {
        const entities = templatesCache.value[sourceEntityTypeId] as Entity[];

        return entities.map((entity) => {
          return {
            value: entity.Id,
            label: buildTemplateLabel(sourceEntityTypeId, entity.FullDisplayName),
          } as DropdownOption<string, number>;
        });
      });
  });

  const templateFields = computed(() => {
    if (!selectedTemplate.value) {
      return [];
    }

    return templateFieldsCache.value[selectedTemplate.value.value];
  });

  const selectedFieldTypeIsLocaleString = computed(() => {
    if (selectedField.value?.specificationDataType === InriverDataType.CVL && selectedField.value.CVLId) {
      const cvl = getCvlById && getCvlById(selectedField.value.CVLId);

      return cvl?.DataType === InriverDataType.LOCALE_STRING;
    }

    return selectedField.value?.specificationDataType === InriverDataType.LOCALE_STRING;
  });

  // Functions
  const buildTemplateLabel = (sourceEntityTypeId: string, templateDisplayName: string) =>
    `${sourceEntityTypeId}: ${templateDisplayName}`.toLocaleLowerCase();

  const initValues = async () => {
    if (!transformatedFunction.value || !relations.value?.length) {
      return;
    }

    const linkId = transformatedFunction.value.args[0];
    const linkSourceEntityTypeId = transformatedFunction.value.args[1];
    const templateId = transformatedFunction.value.args[2];
    const fieldId = transformatedFunction.value.args[3];
    const language = transformatedFunction.value.args[4];
    const unit = transformatedFunction.value.args[5];
    initSelectedFilterValue(linkId, linkSourceEntityTypeId);
    initSelectedTemplate(templateId, linkSourceEntityTypeId);
    await initSelectedField(templateId, fieldId);
    initLanguage(language);
    initUnitCheckbox(unit);
  };

  const initSelectedFilterValue = (linkId: string, linkSourceEntityTypeId: string) => {
    selectedFilterValue.value = linkId
      ? ({
          id: linkId,
          sourceEntityTypeId: linkSourceEntityTypeId,
        } as RelationLink)
      : relations.value[0];
  };

  const initSelectedTemplate = (templateId: string, linkSourceEntityTypeId: string) => {
    const tempalte =
      templatesCache.value[linkSourceEntityTypeId].find((x) => x.Id.toString() === templateId) ??
      templatesCache.value[linkSourceEntityTypeId][0];
    selectedTemplate.value = {
      label: buildTemplateLabel(linkSourceEntityTypeId, tempalte.FullDisplayName),
      value: tempalte?.Id,
    } as DropdownOption<string, number>;
  };

  const initSelectedField = async (templateId: string, fieldId: string) => {
    const cacheValue = templateFieldsCache.value[Number(templateId)];
    if (!cacheValue && fetchTemplateFields) {
      await fetchTemplateFields(selectedTemplate.value?.value); // TODO:
    }

    selectedField.value =
      templateFieldsCache.value[Number(templateId)].find((x) => x.id === fieldId) ??
      templateFieldsCache.value[templateId][0];
  };

  const initLanguage = (language: string) => {
    if (!language && !selectedFieldTypeIsLocaleString.value) {
      return;
    }

    selectedLanguage.value = languages.value.find((x) => x.Name === language);
  };

  const initUnitCheckbox = (unit: string) => (includeUnit.value = !!unit);

  return {
    initValues,
    confirmIsDisabled,
    hasUnit,
    selectedFilterValue,
    selectedFieldTypeIsLocaleString,
    templateFields,
    selectedField,
    selectedLanguage,
    templates,
    selectedTemplate,
    includeUnit,
    settings,
  };
}
