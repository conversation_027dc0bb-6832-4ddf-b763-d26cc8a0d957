variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'euw'
  stackType: 'sbsfa'

  # Deploy dependencies
  dependsOn: 'Build_euwSbsfa'
  dependsOnSecond: 'Build_euwSbsfa'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'pmc2-euw-sbsfa-stapp-syndicate-plus-frontend'

  auth0Domain: 'inriverdev.eu.auth0.com'
  auth0ClientId: 'nbL2G6DGbSzuA3PHlw8gA9WjCBtCwOUz'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: ''

  allowMissingOrgMapping: true
  
  # Application insights instrumentation key
  instrumentationKey: 'd93fb592-53ac-4620-b3b0-e15b949b433d'