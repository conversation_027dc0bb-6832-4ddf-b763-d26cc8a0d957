<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <div v-for="row in numberOfBlocks / 2" :key="row" class="flex flex-row flex-nowrap">
      <div
        v-for="column in numberOfBlocks / 2"
        :key="column"
        class="text-wrap-word-break m-10px four-image flex flex-row flex-nowrap"
      >
        <c-caption-angle-image-block
          v-model="module.data[`image${getOrderIndex(row, column)}`]"
          :dimensions="dimensions"
          :hide-caption="true"
          :max-alt-text-length="Validation.fourImageTextQuadrant.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url, getOrderIndex(row, column))"
        />
        <div class="ml-20px">
          <q-input
            v-model="module.data[`headline${getOrderIndex(row, column)}`]"
            v-bind="$inri.input"
            hide-bottom-space
            :label="$t('syndicate_plus.aplus_define_template.headline')"
            :maxlength="Validation.fourImageTextQuadrant.headline.maxLength"
            counter
            @keyup.enter="updateModel"
            @blur="updateModel"
            @drop="onDrop(module.data[`headline${getOrderIndex(row, column)}`], 'headline', getOrderIndex(row, column))"
          />
          <c-text-editor
            v-model="module.data[`body${getOrderIndex(row, column)}`]"
            :max-length="Validation.fourImageTextQuadrant.body.maxLength"
            @on-field-drop="
              onDrop(module.data[`body${getOrderIndex(row, column)}`], 'body', getOrderIndex(row, column))
            "
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onUpdated } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { CTextEditor, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { useFourImageTextQuadrant } from '@composables/AplusTemplateBuilder';
import { Dimension } from '@customTypes/Aplus';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Variables
const numberOfBlocks = 4;
const dimensions = { width: 135, height: 135 } as Dimension;

// Composables
const { module, initData } = useFourImageTextQuadrant();

// Functions
const handleUrlUpdate = (url: string, orderIndex: number) => {
  module.value.data[`image${orderIndex}`].angle = url;
  updateModel();
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};
const onDrop = (value: string, key: string, orderIndex: number) => {
  module.value.data[key + orderIndex] = fieldsStore.onDrop(value);
  updateModel();
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  initData();
};

const getOrderIndex = (row: number, column: number) => {
  return (row - 1) * 2 + column;
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>

<style lang="scss" scoped>
:deep(.gray-area) {
  width: 200px !important;
  height: 200px !important;
}
</style>
