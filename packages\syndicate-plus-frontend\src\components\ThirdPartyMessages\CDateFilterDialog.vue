<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog"
    :disable-confirm="confirmButtonIsDisabled"
    :disabled-confirm-tooltip="disabledConfirmTooltip"
    :confirm-button-text="$t('syndicate_plus.third_party_messages.filter')"
    :cancel-button-text="$t('syndicate_plus.third_party_messages.filter_date.clear_filter')"
    @cancel="clearFilter"
    @confirm="logEventAndClick(ActionName.FILTER_THIRD_PARTY_MESSAGES_BY_FILTER, performFilter)"
  >
    <div class="w-1/2 pt-2">
      <q-input
        v-model="startDate"
        filled
        :mask="inputMask"
        :label="$t('syndicate_plus.third_party_messages.filter_date.start_date')"
        class="pt-2"
      >
        <template #prepend>
          <q-icon name="mdi-calendar-outline" class="cursor-pointer">
            <q-popup-proxy transition-show="scale" transition-hide="scale">
              <div class="q-pa-md">
                <div class="q-gutter-md row items-start">
                  <q-date v-model="startDate" :mask="dateTimeMask" />
                  <q-time v-model="startDate" :mask="dateTimeMask" now-btn />
                </div>
              </div>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
      <q-input
        v-model="endDate"
        filled
        :mask="inputMask"
        :label="$t('syndicate_plus.third_party_messages.filter_date.end_date')"
      >
        <template #prepend>
          <q-icon name="mdi-calendar-outline" class="cursor-pointer">
            <q-popup-proxy transition-show="scale" transition-hide="scale">
              <div class="q-pa-md">
                <div class="q-gutter-md row items-start">
                  <q-date v-model="endDate" :mask="dateTimeMask" />
                  <q-time v-model="endDate" :mask="dateTimeMask" now-btn />
                </div>
              </div>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { ActionName } from '@enums';
import { useAppInsightsStore } from '@stores';
import { useI18n } from 'vue-i18n';
import { useDateFilter } from '@composables/ThirdPartyMessages';

const emit = defineEmits(['on-confirm', 'on-clear-filter']);

// Composables
const { showDialog } = useDialog();
const { logEventAndClick } = useAppInsightsStore();
const {
  disabledConfirmTooltip,
  dateTimeMask,
  inputMask,
  confirmButtonIsDisabled,
  startDate,
  endDate,
  createSettingsModel,
} = useDateFilter(useI18n);

// Functions
const performFilter = () => {
  const filter = createSettingsModel();
  emit('on-confirm', filter);
};

const clearFilter = () => {
  emit('on-clear-filter');
};
</script>

<style scoped lang="scss">
.frequency {
  margin-left: -9px;
}

.action-buttons {
  padding-top: 10px;
}

:deep(.q-field__bottom) {
  display: none;
}

.days {
  :deep(.c-btn) {
    padding: 4px 20px !important;
  }
}

.action-buttons {
  padding-top: 10px;
}
</style>
