param location string

param appName string

@description('Sets the full Application Insights name')
param applicationInsightsName string = '${appName}-appinsights-00'

resource applicationInsights 'Microsoft.Insights/components@2020-02-02' = {
  name: applicationInsightsName
  kind: 'web'
  location: location
  properties: {
    Application_Type: 'web'
  }
}

output InstrumentationKey string = applicationInsights.properties.InstrumentationKey
