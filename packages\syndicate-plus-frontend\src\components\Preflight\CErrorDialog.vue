<template>
  <q-dialog persistent :model-value="showDialog">
    <q-card>
      <q-card-section>
        <div class="text-h6">
          {{ $t('syndicate_plus.common.error') }}
        </div>
      </q-card-section>
      <q-card-section class="q-pt-none">
        {{ $t('syndicate_plus.preflight.error_dialog.wrong_url_parameters_error_message') }}
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          flat
          :label="$t('syndicate_plus.preflight.error_dialog.back_button')"
          color="primary"
          @click="goToProductSelection"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { useRouter } from '@composables/useRouter';

const { showDialog } = useDialog();
const { goToPage } = useRouter();

function goToProductSelection() {
  goToPage('syndication-page');
}
</script>
