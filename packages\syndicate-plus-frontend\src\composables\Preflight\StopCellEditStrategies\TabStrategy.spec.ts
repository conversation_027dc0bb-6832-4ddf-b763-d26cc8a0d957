import { vi, describe, it, expect } from 'vitest';
import { TabStrategy } from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';

describe('TabStrategy', () => {
  describe('getNextRowAndColumnId', () => {
    it('returns object with next row and column id', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 1;
      const currentColumnId = 3;
      const nextColumnId = 4;
      const strategy = new TabStrategy(rows, columns);
      vi.spyOn(strategy as any, 'getNextAvailableColumnId').mockReturnValue(nextColumnId);

      // Act
      const result = strategy.getNextRowAndColumnId(currentRowId, currentColumnId);

      // Assert
      expect(result).to.deep.equal({
        columnId: nextColumnId,
        rowId: currentRowId,
      } as TableCellCoordinates);
    });

    it('returns object with next row and column id if next available column was not found', async () => {
      // Arrange
      const rows = [];
      const columns = [];
      const currentRowId = 1;
      const currentColumnId = 3;
      const nextColumnId = -1;
      const firstAvailableColumn = 5;
      const nextAvailableRowId = 4;
      const strategy = new TabStrategy(rows, columns);
      vi.spyOn(strategy as any, 'getNextAvailableColumnId').mockReturnValue(nextColumnId);
      vi.spyOn(strategy as any, 'getFirstAvailableColumnId').mockReturnValue(firstAvailableColumn);
      vi.spyOn(strategy as any, 'getNextAvailableRowId').mockReturnValue(nextAvailableRowId);

      // Act
      const result = strategy.getNextRowAndColumnId(currentRowId, currentColumnId);

      // Assert
      expect(result).to.deep.equal({
        columnId: firstAvailableColumn,
        rowId: nextAvailableRowId,
      } as TableCellCoordinates);
    });
  });
});
