<template>
  <div>
    <c-image-uploader class="uploader mb-10px mx-auto" :style="imageDimensionsStyles" @url-updated="handleUrlUpdate">
      <c-angle-image-block
        v-model="model"
        :dimensions="dimensions"
        :max-alt-text-length="props.maxAltTextLength"
        @update-model="updateModel"
      />
    </c-image-uploader>
    <q-input
      v-if="!hideCaption"
      v-model="model.caption"
      class="mt-1 alt-text mx-auto"
      v-bind="$inri.input"
      hide-bottom-space
      :label="$t('syndicate_plus.aplus_define_template.caption')"
      :maxlength="captionMaxLength"
      counter
      @keyup.enter="updateModel"
      @blur="updateModel"
      @drop="onDrop(model.caption)"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, PropType, computed } from 'vue';
import { CAngleImageBlock, CImageUploader } from '@components/TemplateBuilder/Blocks';
import { ImageData, Dimension } from '@customTypes/Aplus';
import { useFieldsTabStore } from '@stores/TemplateBuilder';

const props = defineProps({
  modelValue: {
    type: Object as PropType<ImageData>,
    default: () => ({}),
  },
  hideCaption: {
    type: Boolean,
    default: false,
  },
  dimensions: {
    type: Object as PropType<Dimension>,
    default: () => ({}),
  },
  captionMaxLength: {
    type: Number,
    default: 100,
  },
  maxAltTextLength: {
    type: Number,
    default: 100,
  },
});

// Emits
const emit = defineEmits(['update-model', 'url-updated']);

const fieldsStore = useFieldsTabStore();
// Refs
const model = ref(props.modelValue);

// Computed
const imageDimensionsStyles = computed(() => {
  return `width: ${props.dimensions.width}px; height: ${props.dimensions.height}px;`;
});

// Functions
const updateModel = () => {
  emit('update-model');
};

const handleUrlUpdate = (url: string) => {
  emit('url-updated', url);
};

const onDrop = (value?: string) => {
  if (!value) {
    value = '';
  }
  model.value.caption = fieldsStore.onDrop(value);
  updateModel();
};
</script>

<style scoped lang="scss">
:deep(.q-field__control) {
  min-width: auto !important;
}
</style>
