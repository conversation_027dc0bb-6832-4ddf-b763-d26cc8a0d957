<template>
  <c-dialog
    v-model="showDialog"
    class="c-dialog"
    :title="$t('core.default_functions.specification_field.title')"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    :is-loading="isLoading"
    :disable-confirm="confirmIsDisabled"
    @confirm="confirm"
    @cancel="cancelDialog"
  >
    <div class="row">
      <div class="col-6">
        <c-select
          v-model="selectedFilterValue"
          :options="relations"
          :label="$t('core.default_functions.specification_field.filter_template_by')"
          :placeholder="$t('core.default_functions.specification_field.filter_template_by')"
          :option-label="(x) => x.sourceEntityTypeId?.toLocaleLowerCase()"
          hide-bottom-space
          @update:model-value="onChangeFilter"
        />
      </div>
      <div class="col-6"></div>
      <div class="col-6">
        <c-select
          v-model="selectedTemplate"
          :options="templates"
          :label="$t('core.default_functions.specification_field.template')"
          :placeholder="$t('core.default_functions.specification_field.template')"
          option-label="label"
          hide-bottom-space
          @update:model-value="onChangeTemplate"
        />
      </div>
      <div class="col-6"></div>
      <div class="col-6">
        <c-select
          v-model="selectedField"
          :options="templateFields"
          :label="$t('core.default_functions.specification_field.field')"
          :placeholder="$t('core.default_functions.specification_field.field')"
          option-label="displayName"
          hide-bottom-space
          @update:model-value="onChangeField"
        />
      </div>
      <div class="col-6 p-5px pl-20px">
        <q-checkbox
          v-if="hasUnit"
          v-bind="$inri.checkbox"
          v-model="includeUnit"
          :label="$t('core.default_functions.specification_field.include_unit')"
        />
      </div>
      <div class="col-6">
        <c-select
          v-if="selectedFieldTypeIsLocaleString"
          v-model="selectedLanguage"
          :options="languages"
          :label="$t('syndicate_plus.common.language')"
          :placeholder="$t('syndicate_plus.common.language')"
          option-label="DisplayName"
          option-value="Name"
          hide-bottom-space
        />
      </div>
      <div class="col-6"></div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { onBeforeMount, ref, watch } from 'vue';
import { useDialog } from '@inriver/inri';
import { useSpecificationFieldFunction } from '@core/composables/FieldMapping/Functions';
import { FunctionSettingsComponentEmits } from '@core/interfaces/FieldMapping/Functions';
import { fetchEntityTypeRelations } from '@core/services';
import { RelationLink } from '@core/interfaces';
import { useEditFieldMappingStore, useSpecificationsStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { useLanguages } from '@core/composables/Common';

const props = defineProps({
  converterArgs: {
    type: String,
    required: false,
    default: undefined,
  },
});
const emit = defineEmits<FunctionSettingsComponentEmits>();
const editFieldMappingStore = useEditFieldMappingStore();
const specificationsStore = useSpecificationsStore();

// Variables
const filterAllOption = { id: 'filterAll', sourceEntityTypeId: 'all entity types' } as RelationLink;

// Refs
const isLoading = ref<boolean>(false);
const relations = ref<RelationLink[]>([]);
const { mapping } = storeToRefs(editFieldMappingStore);
const { templatesCache, templateFieldsCache } = storeToRefs(specificationsStore);

// Composables
const { languages } = useLanguages();
const { showDialog, cancel, confirmSuccess } = useDialog();
const {
  initValues,
  confirmIsDisabled,
  templates,
  templateFields,
  hasUnit,
  selectedFilterValue,
  selectedFieldTypeIsLocaleString,
  selectedTemplate,
  selectedField,
  selectedLanguage,
  includeUnit,
  settings,
} = useSpecificationFieldFunction(
  relations,
  templatesCache,
  templateFieldsCache,
  editFieldMappingStore.getCvlById,
  specificationsStore.fetchTemplateFields,
  languages,
  props.converterArgs
);

// Functions
const confirm = async () => {
  emit('saveSettings', settings.value);
  confirmSuccess(null);
};

const cancelDialog = async () => {
  emit('cancel');
  cancel();
};

const initFilter = async (): Promise<void> => {
  const allSpecificationInboundRelations = await fetchEntityTypeRelations('Specification', 'inbound');
  const mappingEntityTypeIds = [
    mapping.value?.WorkareaEntityTypeId,
    mapping.value?.FirstRelatedEntityTypeId,
    mapping.value?.SecondRelatedEntityTypeId,
  ];
  relations.value = [
    filterAllOption,
    ...allSpecificationInboundRelations.filter((x) => mappingEntityTypeIds.includes(x.sourceEntityTypeId)),
  ];
  if (!selectedFilterValue.value) {
    selectedFilterValue.value = relations.value[0];
  }
};

const initTemplates = async () => {
  await specificationsStore.fetchTemplates(relations.value.filter((x) => x.id !== filterAllOption.id));
};

const onChangeFilter = () => {
  if (!templates.value) {
    return;
  }

  selectedTemplate.value = templates.value[0];
};

const onChangeTemplate = async () => {
  if (!selectedTemplate.value) {
    return;
  }

  specificationsStore.fetchTemplateFields(selectedTemplate.value.value);
  if (!templateFields.value) {
    return;
  }

  selectedField.value = templateFields.value[0];
};

const onChangeField = async () => {
  includeUnit.value = false;
  if (selectedFieldTypeIsLocaleString.value && !!languages.value?.length) {
    selectedLanguage.value = languages.value[0];
  }
};

// Lifecycle methods
onBeforeMount(async () => {
  isLoading.value = true;
  try {
    await initFilter();
    await initTemplates();
    await initValues();
  } finally {
    isLoading.value = false;
  }
});

watch([templates], async (newValue, oldValue) => {
  if (newValue !== oldValue && !!templates.value?.length && !selectedTemplate.value) {
    selectedTemplate.value = templates.value[0];
    await onChangeTemplate();
  }
});

watch([templateFields], async (newValue, oldValue) => {
  if (newValue !== oldValue && !!templateFields.value?.length && !selectedField.value) {
    selectedField.value = templateFields.value[0];
  }
});
</script>
