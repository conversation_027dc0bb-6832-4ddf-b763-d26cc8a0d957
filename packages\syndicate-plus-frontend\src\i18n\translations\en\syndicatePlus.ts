export default {
  syndicate_plus: {
    syndicate_plus_title: 'syndicate plus',
    welcome_to_syndicate_plus: 'welcome to syndicate plus',
    request_trading_partner: 'request a trading partner',
    only_show_enabled: 'show only enabled',
    show_all: 'show all',
    show_table_view: 'view as table',
    show_grid_view: 'view as grid',
    imports: 'imports',
    inbound_data: 'inbound data',
    marketplaces: {
      marketplaces: 'marketplaces',
      retailers: 'retailers',
      affiliates: 'affiliates',
      active_wholesalers: 'active wholesalers',
      software_platforms: 'software platforms',
      distributors: 'distributors',
      liquidators: 'liquidators',
    },
    trading_partners: {
      connected: 'connected trading partners',
      available: 'available templates',
      sku_count: '{count} sku | {count} skus',
      no_skus: 'no skus',
    },
    third_party_messages: {
      api_calls: 'api calls',
      inbound_tab: 'inbound',
      outbound_tab: 'outbound',
      details: 'details',
      message: 'message',
      download_file: 'download file',
      download_files: 'download files',
      downloaded: 'file downloaded successfully',
      error: 'an error occurred when downloading the file',
      filter: 'filter',
      filter_date: {
        confirm_disabled_tooltip_wrong_date: 'wrong date',
        confirm_disabled_tooltip: 'information incomplete',
        start_date: 'start date',
        end_date: 'end date',
        clear_filter: 'clear filter',
      },
      no_data: {
        no_api_calls_title: 'no api calls yet.',
        no_api_calls_message:
          'you have no api calls. They will appear here once you export data using an api syndication.',
      },
    },
    media_details: {
      title: 'media',
    },
    syndication: {
      outbox: {
        cancel_process: 'cancel process',
        cancel_process_success: 'process cancelled',
        cancel_process_failed: 'cancel process failed',
        enter_filename: 'enter filename',
        filename: 'filename',
        download_file_error: 'an unexpected error occurred when downloading the file.',
        manage_scheduled_syndications: 'manage scheduled syndications',
        delete_scheduled_syndication_confirm: 'are you sure you want to delete the {jobName} job?',
      },
      assign_aplus_dialog: {
        title: 'assign a+ template',
        select_template: 'select a template',
        confirm_disabled_tooltip: 'select template to syndicate',
        assign_success: 'template assigned successfully',
        assign_error: 'error occurred when assigning a+ template',
      },
      unassign_aplus_dialog: {
        unassign: 'unassign',
        title: 'or unassign template',
        unassign_success: 'template unassigned successfully',
        unassign_error: 'error occurred when unassigning a+ template',
      },
      aplus_assign_template: 'assign a+ template',
      aplus_syndication_success: 'syndicate via a+ request started',
      aplus_syndication_failure: 'syndicate via a+ failed!',
      api_dialog: {
        confirm_disabled_tooltip_wrong_date: 'start and end date must be today or later',
        confirm_disabled_tooltip: 'information incomplete',
      },
      api_syndication: {
        schedule: 'scheduled',
        confirm_schedule: 'schedule',
        schedule_name: 'name your syndication schedule',
        send_all_products: 'always send all products',
        start_date: 'start date',
        end_date: 'end date',
        schedule_success: 'syndication scheduled successfully',
        schedule_error: 'an error occurred when scheduling the syndication',
      },
      api_syndication_success: 'syndicate via api request started',
      api_syndication_failure: 'syndicate via api failed!',
      export_zip_media_success: 'sending to outbox! <br> check your outbox for export progress.',
      export_zip_media_failure: 'export media zip file failed',
      api_syndication_dialog_title: 'api syndication',
      api_syndication_select_template_placeholder: 'select an api field mapping',
      perform_api_syndication: 'syndicate via api',
      perform_api_syndication_via_sftp: 'syndicate via sftp',
      products_tab: 'products',
      collection_tab: 'collections',
      updated: 'updated',
      aplus: 'a+',
      outbox_tab: 'outbox',
      download: 'download',
      download_mappings: {
        filename: '{tradingPartner} mappings',
        downloaded: 'value mapping downloaded successfully',
        error: 'an error occurred when downloading the value mapping',
      },
      export_errors: 'view errors',
      export_incomplete: 'export incomplete. Try downloading again later.',
      export_failed: 'export failed, check the error log for details.',
      export_canceled: 'export canceled',
      mappings: 'mappings',
      settings: 'settings',
      preflight: 'run preflight',
      authorize_amazon_connection: 'authorize amazon api connection',
      syndication_mapping: 'syndication mapping',
      details: 'view details',
      view_media: 'view media',
      details_disabled: 'limit exceeded. you can choose up to 5 products',
      price_disabled: 'limit exceeded. you can choose up to 5 products',
      import_data: 'import data',
      price_data: 'price data',
      price: 'price',
      filter: {
        view: 'view',
        brand: 'brand',
        collection: 'collection',
        product_types: 'product types',
        syndication_status: 'syndication status',
        updated_since_last_syndication: 'updated since last syndication',
      },
      product_type: {
        SKU: 'sku',
        NAME: 'product',
        UPC: 'upc',
        CUSTOM: 'product',
      },
      view: {
        views: 'views',
        product_presentation: 'product presentation',
      },
      enhanced_content_dialog: {
        syndicate_via_enhanced_content: 'syndicate via enhanced content',
        syndicate_with_template: 'syndicate with template',
        or_edit_template: 'or edit template',
        edit_template: 'edit template',
        create_new_template: 'create new template',
      },
      a_plus_content: 'run preflight',
      aplus_tab: 'a+ content',
      go_to_outbox: 'go to outbox',
      aplus_editor: {
        initializing: 'initializing',
        initializing_description: 'your A+ content template editor is initializing. It will be ready soon.',
      },
    },
    below_the_fold: {
      export_button: {
        tooltip: 'export below the fold',
        disabled: 'limit exceeded. you can choose up to {max} products',
      },
      dialog: {
        title: 'export below the fold content',
        export_name: 'export name',
        template: 'template',
        export: 'export',
        error: 'something went wrong, please try again later',
        progress: {
          label: '{current} out of {max} {text}',
        },
      },
    },
    price_import: {
      import_price_page: 'import price',
      import_price_page_description:
        'upload product price data by downloading a template, filling out the necessary fields, and dropping it here.\n upload a single .xls or .xlsx file at a time, making sure that the name contains the word "price"!',
      download_template: 'download template for trading partner pricing',
      download_template_for_general_pricing: 'download template for general pricing',
      upload_price_file: 'upload price file',
      upload_price_file_success: 'price file uploaded successfully',
      upload_price_file_error: 'price file upload failed',
      upload_price_file_name_error: 'upload failed, make sure the file name contains the word "price"',
      upload_price_file_length_error: 'upload failed, upload one price file at a time',
      upload_price_file_extension_error: 'upload failed, you can only upload .xls or .xlsx files as price files',
    },
    preflight: {
      preflight: 'preflight',
      title: 'preflight validation',
      filter: {
        errorsAndWarningsTitle: 'filter errors and warnings',
        columnsWithErrors: 'columns with errors',
        columnsWithWarnings: 'columns with warnings',
        rowsWithErrors: 'rows with errors',
        rowsWithWarnings: 'rows with warnings',
      },
      mandatory_fields: {
        title: 'mandatory fields',
        show_all_fields: 'show all fields',
        show_only_mandatory_fields: 'show only mandatory fields',
        show_only_recommended_fields: 'show only recommended fields',
      },
      editable_fields: {
        title: 'editable fields',
        show_all_fields: 'show all fields',
        only_show_editable_fields: 'only show editable fields',
      },
      status: {
        success: 'row has no errors',
        warning: 'row has syndication warnings',
        error: 'row has syndication errors',
      },
      tooltip: {
        errors: 'errors',
        warnings: 'warnings',
      },
      warning: {
        title: 'warning',
        description: 'editing of data will only be stored in the syndication model, <b>not</b> in the PIM model.',
      },
      columns_limit_warning: {
        title: 'warning',
        description:
          'you are looking at the results from the first {columnsLimit} columns out of {totalColumns}. To see more, save the results to outbox and download them as an excel file.',
      },
      error_dialog: {
        wrong_url_parameters_error_message: 'to perform preflight you need to select products first.',
        back_button: 'go back to product selection',
      },
      perform_api_syndication: 'syndicate via api',
      details_button: 'go to product details',
      details_button_disabled: 'cannot find product in enrich due to mapping error',
      syndicate: 'syndicate',
      syndicate_disable_tooltip: 'fix all warnings to syndicate',
    },
    mapping: {
      set_up_connection: 'set up {tradingPartner} connection',
      request_template_mapping: 'request template mapping',
      field_mapping: 'field mapping',
      source_mapping: 'source mapping',
      suggested_field: 'suggested field',
      details_disabled: 'to see details you need to select row first.',
      mapped_field: 'mapped field',
      unmapped_field: 'unmapped field',
      field_mapping_tab: {
        default_value: 'default value',
        function: 'function',
        importance: 'importance',
      },
      function_mapping_tab: {
        default_functions: 'default',
        custom_functions: 'custom',
      },
      aplus_templates_tab: {
        aplus_templates: 'a+ content',
        create_template: 'create template',
        delete_template: 'delete template',
        edit_template: 'edit template',
        delete_template_dialog_text: 'are you sure you want to delete the {templateName} template?',
        delete_template_success: 'template {templateName} has been deleted',
        no_data: {
          no_templates_title: 'no templates yet.',
          no_templates_message:
            'you have no templates configurations for this syndication. When you have existing templates, they will appear here.',
        },
      },
      media_mapping_tab: {
        syndication_media_tag: 'syndication media tag',
        media_tag: 'media tag',
        image_width: 'image width',
        image_height: 'image height',
        swatch_width: 'swatch width',
        swatch_height: 'swatch height',
        background_color: 'background color',
        naming_override: 'naming override',
        no_data: {
          no_mappings_title: 'no mappings yet.',
          no_mappings_message:
            'you have no mapping configurations for this syndication. When you have existing mappings, they will appear here.',
        },
      },
      value_mapping_tab: {
        no_data: {
          no_mappings_title: 'no mappings yet.',
          no_mappings_message:
            'you have no mapping configurations for this syndication. When you have existing mappings, they will appear here.',
        },
        syndication_value: 'syndication value',
        trading_partner_value: '{tradingPartner} value',
        select_syndication_value: 'select syndication value',
        select_trading_partner_value: 'select {tradingPartner} value',
        saved: 'changes saved successfully',
        warning: 'an error occurred when saving, changes have not been saved',
      },
      media_mapping: 'media mapping',
      value_mapping: 'value mapping',
      importace_type: {
        required: 'required',
        recommended: 'recommended',
        conditional: 'conditional',
      },
      min: 'min',
      max: 'max',
      amazon: {
        login_callback: {
          error: 'could not connect to amazon api. contact support',
          success: 'connected to amazon api successfully',
        },
      },
    },
    connection_settings: {
      page_title: '{tradingPartner} - connection settings',
      saved: 'connection settings saved successfully',
      error: 'an error occurred when saving connection settings',
    },
    aplus_define_template: {
      create_template: 'create template',
      new_template: 'new template',
      template_name: 'template name',
      title: 'a+ templates',
      standard_modules: 'standard modules',
      modules: 'modules',
      fields: 'fields',
      no_mapping_fields: 'no mapping fields',
      drag_module_here: 'drag module here',
      click_to_add_image: 'click to add image',
      back: '< back',
      image: 'image',
      image_url: 'image url',
      headline: 'headline',
      subheadline: 'subheadline',
      asin: 'asin',
      title_label: 'title',
      highlighted: 'highlighted',
      body: 'body',
      enter_specification: 'enter specification',
      enter_definition: 'enter definition',
      angle_or_image_url: 'angle or image url',
      alt_text: 'alt text',
      caption: 'caption',
      enter_bullet: 'enter bullet point text',
      enter_metric: 'enter metric',
      add_metric: 'add metric',
      add_metric_disabled_tooltip: 'you can add maximum 10 metrics',
      enter_text: 'enter text',
      notifications: {
        saving: 'saving...',
        saved: 'template saved successfully',
        error: 'error occurred when saving a+ template',
        image_upload_error: 'image upload failed',
        name_exists: 'template name already exists. Choose another name.',
        preview_error: 'an error occurred when requesting a preview. Try again later.',
      },
      unsaved_changes: {
        title: 'leave the template builder?',
        text: 'any unsaved changes will be lost',
        confirm_button: 'leave',
      },
    },
    define_enhanced_content_template: {
      create_template: 'create template',
      new_template: 'new template',
      template_name: 'template name',
      template_type: 'template type',
      title: 'a+ templates',
      standard_modules: 'standard modules',
      modules: 'modules',
      fields: 'fields',
      no_mapping_fields: 'no mapping fields',
      drag_module_here: 'drag module here',
      click_to_add_image: 'click to add image',
      back: '< back',
      image: 'image',
      image_url: 'image url',
      headline: 'headline',
      subheadline: 'subheadline',
      asin: 'asin',
      title_label: 'title',
      highlighted: 'highlighted',
      body: 'body',
      enter_specification: 'enter specification',
      enter_definition: 'enter definition',
      angle_or_image_url: 'angle or image url',
      alt_text: 'alt text',
      caption: 'caption',
      enter_bullet: 'enter bullet point text',
      enter_metric: 'enter metric',
      add_metric: 'add metric',
      add_metric_disabled_tooltip: 'you can add maximum 10 metrics',
      enter_text: 'enter text',
      notifications: {
        saving: 'saving...',
        saved: 'template saved successfully',
        error: 'error occurred when saving a+ template',
        image_upload_error: 'image upload failed',
        name_exists: 'template name already exists. Choose another name.',
        preview_error: 'an error occurred when requesting a preview. Try again later.',
      },
      unsaved_changes: {
        title: 'leave the template builder?',
        text: 'any unsaved changes will be lost',
        confirm_button: 'leave',
      },
    },
    warning: {
      title: 'warning',
      failed_import_description:
        'the product data in Syndicate Plus are not the latest. contact support using reference <b>{fileName}</b> to help identify the import issue.',
    },
    login_button_text: 'login',
    logout_button_text: 'log out',
    user_profile_title: 'user profile',
    common: {
      sku: 'sku',
      file_name: 'file name',
      angle: 'angle',
      assign: 'assign',
      assigning: 'assigning...',
      confirm: 'confirm',
      enhanced_content: 'enhanced content',
      download_as_csv: 'download as csv',
      table: {
        no_data: 'no data to show',
      },
      group_by: {
        CUSTOM: 'products',
        SKU: 'skus',
        UPC: 'upcs',
      },
      mapping: 'mapping',
      close: 'close',
      cancel: 'cancel',
      cvl: 'cvl',
      template: 'template',
      outbound_template: 'outbound template',
      inbound_template: 'inbound template',
      preview: 'preview',
      outbound_sheet: 'outbound sheet',
      select_all: 'select all',
      selected_mask: 'out of {totalItems} {totalItemsText} selected',
      save_to_outbox: 'save to outbox',
      edit: 'edit',
      edit_table: 'edit table',
      save: 'save',
      save_changes: 'save changes',
      create: 'create',
      add: 'add',
      add_row: 'add row',
      delete: 'delete',
      delete_row: 'delete row',
      specifications: 'specifications',
      export: 'export',
      language: 'language',
      auto: 'auto',
      error: 'error',
      filter: {
        filter: 'filter',
        filters: 'filters',
        clear_all_filters: 'clear all filters',
        clear: 'clear',
        minimize_all: 'minimize all',
        expand_all: 'expand all',
        search: 'search',
        reset_filters: 'reset filters',
      },
      view: {
        views: 'views',
        product_presentation: 'product presentation',
      },
    },
    loading: {
      preparing: 'preparing',
      crafting: 'crafting',
      aplus_preflight_preparing:
        'your A+ Editor is warming up. While you wait, imagine the possibilities and get ready to create captivating templates!',
      preflight_preparing:
        "your content is being processed to ensure quality, accuracy, and compatibility. Hold tight while we work our magic. We'll have your preflight validation shortly!",
    },
    no_data: {
      welcome: 'welcome!',
      opening_soon: 'opening soon?',
      no_preflight_template:
        'no preflight generated yet. Preflight validation ensures your content meets the standarts before going live. Ready when you are!',
      outbox: {
        title: 'your outbox is empty.',
        message: 'you will find things in your outbox when you do one of the following:',
        bullet_points: ['save to outbox', 'perform an api syndication', 'export media resources'],
      },
      products: {
        title: 'no products yet.',
        message:
          'products on published channels in plan & release marked as "syndicate plus enabled" should appear here.',
      },
      channels: {
        title: 'no channels yet.',
        message:
          'you have no channels configured for this trading partner. When you have configured channels, they will appear here and you will be able to see their products.',
      },
      trading_partner_message:
        "you haven't enabled any trading partners yet. Request a trading partner by clicking the plus icon on the top right.",
    },
    service_unavailable: {
      title: 'fika',
      text: '/ˈfiːkə/ \n\n (Swedish custom) a break from work to drink coffee, eat cakes, and rest. \n "our servers are probably having a fika, please try again later."',
    },
    media_context_menu: {
      export_media_as_zip: 'export media as zip',
    },
    details: {
      title: 'selected - details',
      no_data: {
        no_products_title: 'no product details',
        no_products_message: 'no products',
      },
      details_button: 'go to product details',
      details_button_disabled: 'cannot find product in enrich due to mapping error',
      warning: {
        title: 'warning',
        description: 'editing of data will only be stored in the syndication model, <b>not</b> in the PIM model.',
      },
      filter: {
        show_fields: 'show fields',
        show_field_ids: 'show field ids',
        show_field_names: 'show field names',
      },
      notify_request_error: 'something went wrong. Please try again later',
    },
    pricing: {
      title: 'selected - pricing',
      view_price: 'view price',
    },
    collections: {
      collection: 'collection',
      view_collection: 'view collection',
      collections: 'collections',
      notifications: {
        name_exists: 'collection name already exists. Choose another name.',
      },
      create_collection: {
        add_title: 'add to collection',
        create_title: 'create new collection',
        collection_name: 'collection name',
        created_successfully: 'collection created successfully',
        added_successfully: 'products added successfully',
        create_disabled_tooltip: 'your collection needs a name',
        add_disabled_tooltip: 'select a collection to add',
        toast_action_prompt: 'go to collections',
      },
      rename_collection: {
        tooltip_disabled: 'rename non-automated collection',
        tooltip: 'rename collection',
        title: 'rename collection {collectionName}',
        renamed_successfully: 'collection renamed successfully',
      },
      delete_collection: 'delete collection',
      delete_collection_dialog:
        "This collection was created via the integration from Plan & Release, if you haven't made the same change in Plan & Release the collection will be re-created again. Do you want to continue with the delete?",
      delete_collection_dialog_text: 'are you sure you want to delete the {collectionName} collection?',
      delete_collection_success: 'collection {collectionName} has been deleted',
      remove_from_collection: {
        remove_from_collection: 'remove from collection',
        removed_successfully: 'removed from collection successfully',
      },
      no_data: {
        no_collections_title: 'no collections yet.',
        no_collections_message:
          'you have no collections yet. Collections will appear here when you create one or when one is created automatically.',
      },
    },
    product_import: {
      filter: {
        status: 'status',
        content_type: 'content type',
      },
    },
    reference_tables: {
      tooltip: 'reference tables',
      title: 'reference tables',
      view_details: 'view details',
      download_table: 'download table',
      upload_table: 'upload table',
      no_data_title: 'no reference tables yet.',
      no_data_message: 'you have no reference tables yet. Reference tables will appear here when you upload one.',
      upload_reference_table_file: 'upload reference table file',
      upload_reference_table_file_success: 'reference table file uploaded successfully',
      upload_reference_table_file_error: 'reference table file upload failed',
      upload_reference_table_file_length_error: 'upload failed, upload one reference table file at a time',
      upload_reference_table_file_extension_error:
        'upload failed, you can only upload .xls or .xlsx files as reference table files',
      downloaded: 'file downloaded successfully',
      error: 'an error occurred when downloading the file',
    },
  },
  core: {
    common: {
      view_details: 'view details',
    },
    manage_trading_partners: {
      buttons: {
        add_api: 'add api',
        add_flatfile: 'upload format file',
      },
      title: 'settings',
      ok: 'ok',
      no_trading_partners_title: 'no trading partners yet.',
      download: 'download',
      manage_mappings: 'manage mappings',
      assign_collections: 'assign collections',
      assign_outputs: 'assign outputs',
      delete: 'delete',
      delete_mapping_success: 'mapping {mappingName} has been deleted',
      delete_mapping_error: 'error when removing mapping',
      delete_format_file_success: 'format file {templateName} has been deleted',
      delete_format_file_error: 'error when removing format file',
      confirm_delete: {
        title: 'confirm delete',
        text: 'are you sure you want to delete {tradingPartner} format file?',
      },
      delete_not_permitted: {
        title: 'could not delete mapping',
        text: 'This mapping is used in the following syndication configurations:',
        text2: 'To delete the mapping, first remove it from these configurations.',
      },
      add_api: {
        tooltip: 'add api',
        trading_partner: 'trading partner',
        category: 'category',
        disabled_confirm_tooltip: 'select trading partner and category',
      },
      add_mapping: {
        tooltip: 'add mapping',
        title: 'create new mapping',
        name: 'name',
        entity_types: 'entity types',
        entity_type: 'entity type',
        relationship: 'relationship',
        output_entity_type: 'output entity type',
        syndicate_on_sku_level: 'syndicate on sku level',
        save_mapping: 'save mapping',
        save_success: 'mapping saved successfully',
        save_error: 'error when saving mapping',
        validation_error: 'please fill in all required fields',
      },
      create_format_file: {
        invalid_image_url: 'invalid image url',
        tooltip: 'create format file',
        company: 'company',
        category: 'category',
        url: 'company image url',
        create_success: 'format file created successfully',
        create_error: 'error when creating format file',
        not_selected_error: 'trading partner or category are not selected',
        validation_error: 'please fill in all required fields',
        duplicate_error: 'dynamic format file with these trading partner and category already exists',
      },
      upload_file: {
        placeholder: 'click to add format file',
        already_exists: 'format name already exists. Choose another name',
        mandatory_columns_error:
          'upload a format file with all required columns and values. You can read the requirements on how to create your own format file',
      },
    },
    assign_collections: {
      assign_collections_for: 'assign collections for',
      channel: 'channel',
      assign_success: 'collections assigned successfully',
      assign_error: 'failed to assign collections',
      channels: 'channels',
      workarea: 'workarea',
      workareas: 'workareas',
    },
    assign_outputs: {
      assign_outputs_for: 'assign outputs for {formatFileNames}',
      assign_success: 'outputs assigned successfully',
      assign_error: 'failed to assign outputs',
    },
    trading_partners: {
      view_mappings: 'view mappings',
      collections: {
        tab_name: 'collections',
        workarea: 'workarea',
        save_to_outbox: 'save to outbox',
        select_mapping: 'select mapping',
        select_output: 'select output',
      },
      products: {
        tab_name: 'products',
        channel: 'channel',
      },
      history: {
        show_errors: 'show errors',
        no_error_message_available: 'no error message available',
        tab_name: 'history',
        delete: 'delete',
        download: 'download',
        download_resources: 'download resources',
        syndication_started: 'syndication started',
        syndication_error: 'error when running syndication',
        syndication_unavailable:
          'syndication is already running or queued and it must finish before the new one can be started',
        confirm_delete: {
          title: 'confirm delete',
          text: 'are you sure you want to delete this row?',
        },
        no_data: 'your history is empty.',
        details: 'details',
      },
      syndicate: 'syndicate',
    },
    settings: {
      formats_tab: 'formats',
      outputs_tab: 'outputs',
      templates_tab: 'below the fold',
      field_mapping: {
        saved: 'changes saved successfully',
        warning: 'an error occurred when saving, changes have not been saved',
        category: 'category',
        field_type: 'field type',
        map_automatically: 'map automatically',
        set_default_language: 'set default language',
        export: 'export mapping',
        import: 'import mapping',
        upload_mapping_file_length_error: 'upload failed, upload one mapping file at a time',
        upload_mapping_file_extension_error: 'upload failed, you can only upload .json files as mapping files',
        unmap_function: 'unmap function',
        unmap_source_field: 'unmap source field',
        edit_function: 'edit function',
        map_enum: 'map enumeration',
        state: 'state',
        fields: 'fields',
        resource_export: 'resource export',
        drop_wrong_field_type: 'use a field from the resources section',
        functions: 'functions',
        add_list_item: 'add list item',
        remove_list_item: 'remove list item',
        map_enum_dialog: {
          title: 'map enumerations',
          source: 'source',
          target: 'target',
          target_value: 'target value',
          automap: 'automap',
        },
      },
    },
    outputs: {
      delete_output: 'delete output',
      delete_output_success: 'output {outputName} has been deleted',
      delete_output_error: 'error when removing output',
      add_output_tooltip: 'add output',
      add_output: {
        output_name: 'name',
        output_name_error: 'only characters a-z, A-Z, and 0-9 are permitted. Spaces are not allowed.',
        validation_error: 'please fill in all required fields',
        output_type: 'output type',
        output_format: 'output format',
        delimiter: 'delimiter',
        data_level: 'data level',
        enable_compression: 'enable compression',
        success: 'output created successfully',
        error: 'error when creating output',
        template_file_name: 'template file name',
        export_file_name: 'export file name',
        worksheet_name: 'worksheet name',
        worksheet_start_cell: 'worksheet start cell',
        add_worksheet: 'add worksheet',
        remove_worksheet: 'remove worksheet',
        export_to_zip_folder: 'export to zip folder',
        set_encoding_to_utf_8: 'set encoding to utf-8',
        template_file_name_error:
          'write the name including the file type extension. Allowed file types are .xlsx and .xlsm.',
        export_file_name_error: 'file type extension must match the one of template file name',
        start_cell_error: 'please use standard cell naming format, such as A2, B10, etc.',
        delivery_method: 'delivery method (optional)',
        connection_string: 'connection string',
        file_name: 'file name',
        username: 'username',
        password: 'password',
        host: 'host',
        path: 'path',
        port: 'port',
        azure_delivery: {
          settings: 'Azure blob delivery settings',
          container: 'container name',
        },
        ftp_delivery: {
          settings: 'FTP delivery settings',
        },
        ftps_delivery: {
          settings: 'FTPS delivery settings',
        },
        http_delivery: {
          settings: 'HTTP Post delivery settings',
          url: 'URL',
        },
        sftp_delivery: {
          settings: 'SFTP delivery settings',
        },
      },
      create_new_output: 'create new output',
      save_output: 'save output',
      delete_not_permitted: {
        title: 'could not delete output',
        text: 'This output is used in the following syndication configurations:',
        text2: 'To delete the output, first remove it from these configurations.',
      },
      no_data: {
        no_outputs_title: 'no outputs yet.',
        no_outputs_message:
          'you have no outputs yet. Outputs will appear here when you create one or when one is created automatically.',
      },
    },
    templates: {
      create_template: 'create template',
      save_template: 'save template',
      edit_template: 'edit template',
      delete_template: 'delete template',
      delete_template_success: 'template {templateName} has been deleted',
      delete_template_error: 'error when removing template',
      no_templates_title: 'no templates yet.',
    },
    review: {
      title: 'review',
      custom_selection: 'custom selection',
      review_error: 'error when running review',
      tooltip: {
        recommended: 'field is recommended',
        mandatory: 'field is mandatory',
        enum: 'this value is invalid — check the allowed options for this field',
        number: 'enter a valid number in this field',
      },
      filter: {
        errors_and_warnings_title: 'filter errors and warnings',
        columns_with_errors: 'columns with errors',
        columns_with_warnings: 'columns with warnings',
        rows_with_errors: 'rows with errors',
        rows_with_warnings: 'rows with warnings',
      },
      mandatory_fields: {
        title: 'mandatory fields',
        show_all_fields: 'show all fields',
        show_only_mandatory_fields: 'show only mandatory fields',
        show_only_recommended_fields: 'show only recommended fields',
      },
    },
    default_functions: {
      toUpper: 'to upper',
      concatenate: {
        title: 'concatenate',
        no_value: 'no value',
        add_field: 'add field',
        remove_field: 'remove field',
        prefix: 'prefix',
        separator: 'separator',
        suffix: 'suffix',
        field: 'field {index}',
      },
      constant: 'constant',
      chooseLanguage: 'choose language',
      set_default_language: 'set default language',
      cvl_value: 'cvl value',
      beforeAfter: 'before after',
      coalesce: {
        title: 'coalesce',
        no_value: 'no value',
        add_field: 'add field',
        remove_field: 'remove field',
        constant: 'constant',
        else: 'else',
      },
      related_entity_field: {
        title: 'related entity field',
        entity_type: 'entity type',
        add_relation: 'add relation',
        remove_relation: 'remove relation',
        relationship: 'relationship',
        related_entity_type: 'related entity type {entityTypeIndex}',
        entity: 'entity',
        field: 'field',
        language: 'language',
        no_entities_tooltip:
          'There are no entities on selected type. Please create an entity or select another entity type.',
      },
      systemId: 'system id',
      specification_field: {
        title: 'specification field',
        template: 'template',
        field: 'field',
        include_unit: 'include unit',
        filter_template_by: 'filter template by',
      },
      choose_language_title: 'select language',
      before_after_title: 'before \u2223 after',
      before: 'before',
      after: 'after',
      separator: 'separator',
      delimiter: 'delimiter',
      apply_to_all: 'apply to all',
    },
    custom_functions: {
      new_function: 'new function',
      validate_function: 'validate function',
      function_name: 'function name',
      parse_script_success: 'script parsed successfully',
      save_custom_function_success: 'custom function saved',
      function_removed: 'custom function was removed',
      function_removed_error_text: 'unexpected error occurred when removing custom function.',
      function_in_use_error_text:
        'could not remove function as it is in use. Please remove from all mappings and try again.',
      validation_error: 'please fill in all required fields',
    },
    replace_mapped_source_field_dialog: {
      title: 'Replace mapped source field',
      text: 'Some selected fields have already been mapped with other source fields. Do you want to replace these source fields with the source field you just selected?',
      confirm_button_text: 'ok',
    },
    cancel_edit_mapping_dialog: {
      title: 'you have unsaved changes',
      text: 'are you sure you want to leave?',
      confirm_button_text: 'ok',
    },
  },
};
