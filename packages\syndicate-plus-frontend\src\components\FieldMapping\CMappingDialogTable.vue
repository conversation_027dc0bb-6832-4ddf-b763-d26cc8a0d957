<template>
  <q-table
    flat
    dense
    hide-bottom
    :title="title"
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    separator="cell"
    :rows="processedRows"
    :columns="columns"
  >
    <template v-if="props.isInbound" #body-cell-sourceFieldName="sourceFieldName">
      <q-td class="grey-dark">
        {{ getSourceFieldDisplayName(sourceFieldName.row.sourceFieldName) }}
      </q-td>
    </template>
    <template #body-cell-actions>
      <c-table-actions>
        <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
      </c-table-actions>
    </template>
    <template #body-cell-function="functionProps">
      <q-td class="flex justify-start">
        <div class="mapping-dialog-function-cell">
          <div v-for="functionObject in functionProps.row.functions" :key="functionObject">
            <c-field-mapping-function :func="functionObject" />
          </div>
        </div>
      </q-td>
    </template>
    <template #body-cell-importanceType="importanceProps">
      <q-td :props="importanceProps">
        <div v-if="importanceProps.row['importanceType']" class="flex flex-nowrap justify-start">
          {{ $t(`syndicate_plus.mapping.importace_type.${importanceProps.row['importanceType']}`) }}
        </div>
      </q-td>
    </template>
    <template #body-cell-length="lengthProps">
      <q-td :props="lengthProps">
        <div
          v-if="lengthProps.row['minCharacterLength'] && lengthProps.row['maxCharacterLength']"
          class="flex flex-nowrap justify-start"
        >
          <c-mapping-length
            :min-length="lengthProps.row['minCharacterLength']"
            :max-length="lengthProps.row['maxCharacterLength']"
          />
        </div>
      </q-td>
    </template>
  </q-table>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';
import CMappingLength from '@components/FieldMapping/CMappingLength.vue';
import CFieldMappingFunction from '@components/FieldMapping/CFieldMappingFunction.vue';
import { getSourceFieldDisplayName } from '@helpers';
import { InboundTemplateField } from '@customTypes';
import { OutboundTemplateField } from '@customTypes/OutboundTemplateField';

const props = defineProps({
  isInbound: {
    type: Boolean,
    required: true,
  },
  rows: {
    type: Array<InboundTemplateField | OutboundTemplateField>,
    default: [] as Array<InboundTemplateField | OutboundTemplateField>,
  },
  tradingPartnerName: {
    type: String,
    default: '',
  },
});

const { t } = useI18n();

const processedRows = computed(() => {
  return props.rows;
});

const title = computed(() => {
  return props.isInbound
    ? t('syndicate_plus.syndication.syndication_mapping')
    : `${props.tradingPartnerName} ${t('syndicate_plus.common.mapping')}`;
});

const columns = computed(() => [
  props.isInbound
    ? {
        name: 'sourceFieldName',
        field: 'sourceFieldName',
        align: 'left' as const,
        label: 'source field',
        style: 'width: 25%; color: var(--color-grey-dark)',
      }
    : {
        name: 'syndicationFieldName',
        field: 'syndicationFieldName',
        align: 'left' as const,
        label: 'syndication field',
        style: 'width: 25%; color: var(--color-grey-dark)',
      },
  {
    name: 'actions',
    field: 'actions',
    label: '',
    align: 'center' as const,
  },
  props.isInbound
    ? {
        name: 'syndicationFieldName',
        field: 'syndicationFieldName',
        label: 'syndication field',
        align: 'left' as const,
        style: 'width: 25%; color: var(--color-grey-dark)',
      }
    : {
        name: 'targetFieldName',
        field: 'targetFieldName',
        label: `${props.tradingPartnerName} field`,
        align: 'left' as const,
        style: 'width: 25%; color: var(--color-grey-dark)',
      },
  {
    name: 'defaultValue',
    field: 'defaultValue',
    label: 'default value',
    align: 'left' as const,
    style: 'width: 23%; color: var(--color-grey-dark)',
  },
  {
    name: 'function',
    field: 'function',
    label: 'function',
    align: 'left' as const,
    style: 'width: 23%',
  },
  {
    name: 'importanceType',
    field: 'importanceType',
    label: 'importance',
    align: 'left' as const,
    style: 'width: 5%; color: var(--color-grey-dark)',
  },
  {
    name: 'dataType',
    field: 'dataType',
    label: `data type`,
    align: 'left' as const,
    style: 'width: 5%',
  },
  {
    name: 'format',
    field: 'format',
    label: `data format`,
    align: 'left' as const,
    style: 'width: 5%',
  },
  {
    name: 'length',
    field: 'length',
    label: `length`,
    align: 'left' as const,
    style: 'width: 8%',
  },
  {
    name: 'parentOverrideField',
    field: 'parentOverrideField',
    label: `override`,
    align: 'left' as const,
    style: 'width: 5%',
  },
]);
</script>
<style lang="scss" scoped>
.mapping-dialog-function-cell {
  display: flex;
  align-items: center;
}
</style>
