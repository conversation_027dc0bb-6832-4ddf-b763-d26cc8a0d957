import { computed, ref } from 'vue';
import { useEnvironmentSettings } from '@composables';

export default function useProductEdit() {
  // Refs
  const isEditingMode = ref(false);

  // Computed fields
  const { isEditEnabled } = useEnvironmentSettings();

  const isEditButtonVisible = computed<boolean>(() => {
    return isEditEnabled.value;
  });

  // Functions
  const toggleEditMode = (isEditMode: boolean): void => {
    isEditingMode.value = isEditMode;
  };

  return { isEditingMode, isEditButtonVisible, toggleEditMode };
}
