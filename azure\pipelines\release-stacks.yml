trigger:
  - main

name: 'Release.$(Build.SourceBranchName).$(Date:MM.dd)$(Rev:.r)'

parameters:
  - name: 'disableApplicationBuild'
    type: 'boolean'
    default: false
    displayName: 'Disable Application Build'

  - name: 'disableApplicationDeploy'
    type: 'boolean'
    default: false
    displayName: 'Disable Application Deploy'

  - name: 'stacks'
    type: object
    default:
      - 'euwDev4a'
      - 'euwTest1a'
      - 'useTest1a'

resources:
  repositories:
    - repository: iPMC.Autotest.DevOps.Templates
      type: git
      name: 821ff125-367d-4cd3-82db-3c576ae40e0e/iPMC.Autotest.DevOps

stages:
  - ${{ each stack in parameters.stacks }}:
      - template: templates/buildStage.yml
        parameters:
          disableApplicationBuild: ${{ parameters.disableApplicationBuild }}
          stack: ${{ stack }}
      - template: templates/deployStage.yml
        parameters:
          stack: '${{ stack }}'
          disableApplicationDeploy: ${{ parameters.disableApplicationDeploy }}
