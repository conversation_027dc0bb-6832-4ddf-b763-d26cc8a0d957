import { FunctionSettings } from '@core/interfaces/FieldMapping/Functions';
import { DefaultFunctionsName } from '@core/enums';

/*
  [
    filterLinkId, ('filterAll' if a specific filter is not selected) 
    entityTypeId, 
    specificationEntityId, 
    specificationFieldId, 
    language, 
    unit
  ]
*/
export type SpecificationFieldArgs = [string, string, string, string, string, string];
export type SpecificationFieldValues = [];

export class SpecificationFieldSettings implements FunctionSettings<SpecificationFieldArgs, SpecificationFieldValues> {
  constructor(args: SpecificationFieldArgs, values: SpecificationFieldValues) {
    this.name = DefaultFunctionsName.SpecificationField;
    this.args = args;
    this.values = values;
  }

  name: string;
  args: SpecificationFieldArgs;
  values: SpecificationFieldValues;
}
