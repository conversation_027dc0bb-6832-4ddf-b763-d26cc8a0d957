import { ref, computed, Ref, ComputedRef } from 'vue';
import { SyndicationPageTabNames, CollectionUploadType } from '@enums';
import { CollectionDetails } from '@customTypes';
import { useEmitter } from '@composables';

export default function useCollectionButtons(
  tab: Ref<SyndicationPageTabNames>,
  isProductsTabWithSelectedProducts: ComputedRef<boolean>
) {
  // Variables
  const emitter = useEmitter();

  // Refs
  const canAddToCollection = ref(false);
  const showRenameCollectionDialog = ref<boolean>(false);
  const showDeleteCollectionDialog = ref<boolean>(false);
  const showAddOrCreateCollectionDialog = ref<boolean>(false);
  const selectedCollection = ref<CollectionDetails | null>(null);

  // Computed
  const isCollectionsTabWithSelectedCollection = computed(
    () => tab.value == SyndicationPageTabNames.Collection && !!selectedCollection.value
  );
  const isCreateEmptyCollectionButtonVisible = computed(() => tab.value == SyndicationPageTabNames.Collection);
  const isCreateCollectionButtonVisible = computed(() => isProductsTabWithSelectedProducts.value);
  const isDeleteCollectionButtonVisible = computed(
    () => tab.value == SyndicationPageTabNames.Collection && !!selectedCollection.value
  );

  const isRenameCollectionButtonDisabled = computed(
    () =>
      !!(
        selectedCollection.value !== undefined &&
        selectedCollection.value?.uploadType !== CollectionUploadType.USER_CREATED
      )
  );
  const isGoToCollectionDetailsButtonVisible = computed(
    () => tab.value == SyndicationPageTabNames.Collection && !!selectedCollection.value
  );

  // Functions
  const openAddOrCreateCollectionDialog = (canAdd: boolean) => {
    canAddToCollection.value = canAdd;
    showAddOrCreateCollectionDialog.value = true;
  };

  const openRenameCollectionDialog = () => {
    showRenameCollectionDialog.value = true;
  };

  const openDeleteCollectionDialog = () => {
    if (!selectedCollection.value) {
      return;
    }

    showDeleteCollectionDialog.value = true;
  };

  const onCollectionRowClick = (collection: CollectionDetails) => {
    selectedCollection.value = collection;
  };

  const onCollectionDeleted = (deletedCollection: CollectionDetails) => {
    if (deletedCollection === selectedCollection.value) {
      selectedCollection.value = null;
    }
  };

  //Events
  emitter.on('collection-deleted', (deletedCollection) => {
    onCollectionDeleted(deletedCollection);
  });

  return {
    selectedCollection,
    isCollectionsTabWithSelectedCollection,
    showAddOrCreateCollectionDialog,
    showRenameCollectionDialog,
    showDeleteCollectionDialog,
    canAddToCollection,
    isCreateEmptyCollectionButtonVisible,
    isDeleteCollectionButtonVisible,
    isCreateCollectionButtonVisible,
    isGoToCollectionDetailsButtonVisible,
    isRenameCollectionButtonDisabled,
    openAddOrCreateCollectionDialog,
    openRenameCollectionDialog,
    openDeleteCollectionDialog,
    onCollectionRowClick,
    onCollectionDeleted,
  };
}
