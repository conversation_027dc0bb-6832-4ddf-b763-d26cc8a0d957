variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'use'
  stackType: 'prod1a'

  # Deploy dependencies
  dependsOn: 'Build_useProd1a'
  dependsOnSecond: 'Build_useProd1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-prod1a-use.productmarketingcloud.com'

  auth0Domain: 'auth-dev.syndic8.io'
  auth0ClientId: 'ZmDqG4j7MFQVr2yougZHmhPOZAoZlGAy'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: 'https://api.syndic8.io'
  fileServerUrl: 'https://app.syndic8.io'

  allowMissingOrgMapping: false
  
  # Application insights instrumentation key
  instrumentationKey: '70a86ad3-c8e4-46c0-a9bb-2a9057c84b9b'

  # Infrared override
  infraredAuth0Domain: 'auth-dev.syndic8.io'
  infraredAuth0ClientId: 'twD9zFV3gROvoMmRyVDq2kU6fcjZsATW'
  infraredAuth0Connection: 'inriver-prod-openid'
  infraredApiUrl: 'https://api.infrared.inriver.syndic8.io'
  infraredFileServerUrl: 'https://app.infrared.inriver.syndic8.io'
  infraredEnvs: 'prod-visualcomfortandco-dev'

  # Environments with the price import enabled
  priceImportEnabledEnvs: 'prod-marshallgroup-test'
