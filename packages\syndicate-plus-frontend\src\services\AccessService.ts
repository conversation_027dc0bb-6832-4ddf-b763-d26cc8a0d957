import featureService from '@services/FeatureService';
import permissionService from '@services/PermissionService';
import AccessHttpService from '@httpservices/AccessHttpService';

class AccessService {
  hasSyndicatePlusAdminAccess() {
    const hasPermission = permissionService.getPermissions().find((i) => i == 'SyndicatePlusAdmin') !== undefined;
    return featureService.isSyndicatePlusAdminEnabled() && hasPermission;
  }

  async hasSyndicateAplusAccess(orgId: number): Promise<boolean> {
    return await AccessHttpService.hasSyndicateAplusAccess(orgId);
  }

  hasSyndicateAdvanceConfigAccess() {
    return permissionService.getPermissions().find((i) => i == 'SyndicateAdvance_Config') !== undefined;
  }
}

export default new AccessService();
