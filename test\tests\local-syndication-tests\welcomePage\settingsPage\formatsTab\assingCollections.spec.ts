import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';
import { WelcomeSettingsPage } from '@pages/welcomePage/welcomeSettings/welcomeSettings.page';
import { FormatsTabPage } from '@pages/welcomePage/welcomeSettings/formatsTab/formatsTab.page';
import { AssignCollectionsPage } from '@pages/welcomePage/welcomeSettings/formatsTab/assignCollections.page';

test.describe('Assign collections', () => {
  const formatSpeakers = 'best-buy - Speakers';
  const channel = 'UI Test - Channel 03';
  const node = 'UI Test - Node 03 ';
  const UITestFormat = 'UITest format - add and remove collection';
  const workAreaCollection = 'UITest - add collection test';

  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;
  let welcomeSettingsPage: WelcomeSettingsPage;
  let formatTabPage: FormatsTabPage;
  let collectionPage: AssignCollectionsPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
    welcomeSettingsPage = new WelcomeSettingsPage(localPage);
    formatTabPage = new FormatsTabPage(localPage);
    collectionPage = new AssignCollectionsPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
    await welcomeToSyndicatePlusPage.settingsButton.click();
    await expect.soft(welcomeSettingsPage.formatsTab, 'FormatsTab is not visible').toBeVisible();
    await welcomeSettingsPage.formatsTab.click();
    await expect(formatTabPage.formatsTable, 'Formats Table is not visible').toBeVisible();
  });

  test('Add and remove a channel collection to API format', async () => {
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.assignCollectionButton.click();
    await collectionPage.waitForPageToLoad();
    await collectionPage.channelTab.click();

    // add collection
    await collectionPage.selectChannel(channel);
    await expect(collectionPage.getCollectionByText(node), 'Node is not visible').toBeVisible();
    await collectionPage.dragAndDropCollection(node);
    await collectionPage.saveButton.click();
    await collectionPage.goBackButton.click();
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.assignCollectionButton.click();
    await collectionPage.waitForPageToLoad();
    await expect(collectionPage.addedCollection(node)).toBeVisible();

    // remove collection
    await collectionPage.removeCollectionLink(node);
    await collectionPage.saveButton.click();
    await collectionPage.goBackButton.click();
    await formatTabPage.selectFormat(formatSpeakers).click();
    await formatTabPage.assignCollectionButton.click();
    await collectionPage.waitForPageToLoad();
    await expect(collectionPage.addedCollection(node)).not.toBeVisible();
  });

  test('Add and remove a workarea collection to format file', async () => {
    await formatTabPage.selectFormat(UITestFormat).click();
    await formatTabPage.assignCollectionButton.click();
    await collectionPage.waitForPageToLoad();
    await collectionPage.workareaTab.click();

    // add collection
    await collectionPage.dragAndDropCollection(workAreaCollection);
    await collectionPage.saveButton.click();
    await collectionPage.goBackButton.click();
    await formatTabPage.selectFormat(UITestFormat).click();
    await formatTabPage.assignCollectionButton.click();
    await collectionPage.waitForPageToLoad();
    await expect(collectionPage.addedCollection(workAreaCollection)).toBeVisible();

    // remove collection
    await collectionPage.removeCollectionLink(workAreaCollection);
    await collectionPage.saveButton.click();
    await collectionPage.goBackButton.click();
    await formatTabPage.selectFormat(UITestFormat).click();
    await formatTabPage.assignCollectionButton.click();
    await collectionPage.waitForPageToLoad();
    await expect(collectionPage.addedCollection(workAreaCollection)).not.toBeVisible();
  });
});
