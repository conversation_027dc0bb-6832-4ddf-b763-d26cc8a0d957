name: $(Build.SourceBranchName).$(Date:MM.dd)$(Rev:.r)

trigger: none

parameters:
  - name: 'stack'
    type: 'string'
    default: 'euwDev4a'
    displayName: 'Stack'

  - name: 'disableApplicationBuild'
    type: 'boolean'
    default: false
    displayName: 'Disable Application Build'

  - name: 'disableApplicationDeploy'
    type: 'boolean'
    default: false
    displayName: 'Disable Application Deploy'

  - name: 'disableUnitTests'
    type: 'boolean'
    default: false
    displayName: 'Disable Unit tests'

resources:
  repositories:
    - repository: iPMC.Autotest.DevOps.Templates
      type: git
      name: 821ff125-367d-4cd3-82db-3c576ae40e0e/iPMC.Autotest.DevOps

stages:
  - template: templates/buildStage.yml
    parameters:
      disableApplicationBuild: ${{ parameters.disableApplicationBuild }}
      disableUnitTests: ${{ parameters.disableUnitTests }}
      stack: ${{ parameters.stack }}
  - template: templates/deployStage.yml
    parameters:
      stack: '${{ parameters.stack }}'
      disableApplicationDeploy: ${{ parameters.disableApplicationDeploy }}
