import { describe, it, expect } from 'vitest';
import { useHistoryPanelStore } from './useHistoryPanelStore';
import { LongRunningJobStatuses } from '@core/enums';

const makeHistory = (overrides = {}) => ({
  state: LongRunningJobStatuses.FINISHED,
  fileName: 'file.txt',
  ...overrides,
});

describe('useHistoryPanelStore', () => {
  it('isDeleteButtonVisible is true when at least one row is selected', () => {
    const store = useHistoryPanelStore();
    store.selectedRows.value = [makeHistory()];
    expect(store.isDeleteButtonVisible.value).toBe(true);
    store.selectedRows.value = [];
    expect(store.isDeleteButtonVisible.value).toBe(false);
  });

  it('isDisplayErrorsVisible is true only if one row is selected and its state is ERROR', () => {
    const store = useHistoryPanelStore();
    store.selectedRows.value = [makeHistory({ state: LongRunningJobStatuses.ERROR })];
    expect(store.isDisplayErrorsVisible.value).toBe(true);
    store.selectedRows.value = [makeHistory({ state: LongRunningJobStatuses.FINISHED })];
    expect(store.isDisplayErrorsVisible.value).toBe(false);
    store.selectedRows.value = [
      makeHistory({ state: LongRunningJobStatuses.ERROR }),
      makeHistory({ state: LongRunningJobStatuses.ERROR }),
    ];
    expect(store.isDisplayErrorsVisible.value).toBe(false);
  });

  it('isDownloadButtonVisible is true if a row is selected and it has a fileName', () => {
    const store = useHistoryPanelStore();
    store.selectedRows.value = [makeHistory({ fileName: 'file.txt' })];
    expect(store.isDownloadButtonVisible.value).toBe(true);
    store.selectedRows.value = [makeHistory({ fileName: '' })];
    expect(store.isDownloadButtonVisible.value).toBe(false);
    store.selectedRows.value = [];
    expect(store.isDownloadButtonVisible.value).toBe(false);
  });
});
