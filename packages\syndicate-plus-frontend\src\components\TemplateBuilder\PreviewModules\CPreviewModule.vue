<template>
  <div class="builder-module" :class="builderModuleCustomClass">
    <c-company-logo v-if="moduleType === ModuleTypes.STANDARD_COMPANY_LOGO" :index="index" />
    <c-images-and-text
      v-else-if="moduleType === ModuleTypes.STANDARD_FOUR_IMAGE_TEXT"
      :index="index"
      :number-of-blocks="4"
    />
    <c-images-and-text
      v-else-if="moduleType === ModuleTypes.STANDARD_THREE_IMAGES_AND_TEXT"
      :index="index"
      :number-of-blocks="3"
    />
    <c-image-sidebar v-else-if="moduleType === ModuleTypes.STANDARD_IMAGE_SIDEBAR" :index="index" />
    <c-image-text-overlay v-else-if="moduleType === ModuleTypes.STANDARD_IMAGE_TEXT_OVERLAY" :index="index" />
    <c-header-image-text v-else-if="moduleType === ModuleTypes.STANDARD_HEADER_IMAGE_TEXT" :index="index" />
    <c-four-image-text-quadrant
      v-else-if="moduleType === ModuleTypes.STANDARD_FOUR_IMAGE_TEXT_QUADRANT"
      :index="index"
    />
    <c-multiple-image-text v-else-if="moduleType === ModuleTypes.STANDARD_MULTIPLE_IMAGES_AND_TEXT" :index="index" />
    <c-product-description-text
      v-else-if="moduleType === ModuleTypes.STANDARD_PRODUCT_DESCRIPTION_TEXT"
      :index="index"
    />
    <c-single-image-spec v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_IMAGE_SPECS_DETAIL" :index="index" />
    <c-single-side-image v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_SIDE_IMAGE_LEFT" :index="index" />
    <c-single-side-image
      v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_SIDE_IMAGE_RIGHT"
      :index="index"
      :align-left="false"
    />
    <c-single-image-highlights
      v-else-if="moduleType === ModuleTypes.STANDARD_SINGLE_IMAGE_AND_HIGHLIGHTS"
      :index="index"
    />
    <c-tech-spec v-else-if="moduleType === ModuleTypes.STANDARD_TECH_SPECS" :index="index" />
    <c-text v-else-if="moduleType === ModuleTypes.STANDARD_TEXT" :index="index" />
    <c-comparison-chart v-else-if="moduleType === ModuleTypes.STANDARD_COMPARISON_CHART" :index="index" />
    <div v-else class="content">{{ moduleType }}</div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { ModuleTypes } from '@enums/Aplus';
import {
  CCompanyLogo,
  CImagesAndText,
  CImageSidebar,
  CImageTextOverlay,
  CHeaderImageText,
  CFourImageTextQuadrant,
  CMultipleImageText,
  CProductDescriptionText,
  CSingleImageSpec,
  CSingleSideImage,
  CSingleImageHighlights,
  CTechSpec,
  CText,
  CComparisonChart,
} from '@components/TemplateBuilder/PreviewModules';

defineProps({
  moduleType: {
    type: String,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
});

// Computed
const builderModuleCustomClass = computed(() => {
  return ModuleTypes.STANDARD_PRODUCT_DESCRIPTION_TEXT ? 'min-height-auto' : '';
});
</script>
<style lang="scss" scoped>
$large-block-min-height: 200px;

.builder-module {
  min-height: $large-block-min-height;
  margin-bottom: 12px;
  overflow: auto;
}

.min-height-auto {
  min-height: auto;
}
</style>
