import { vi, describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useCreateCollectionActions } from '@composables/Collection';
import { CollectionService } from '@services';

vi.mock('@services/CollectionService');

describe('useCreateCollectionActions', async () => {
  describe('createCollection', async () => {
    it('calls the service method with the correct arguments', async () => {
      // Arrange
      const isLoading = ref(true);
      const tradingPartnerName = 'tp';
      const newCollectionName = 'a';
      const { createCollection } = useCreateCollectionActions(isLoading);

      // Act
      await createCollection(tradingPartnerName, newCollectionName);

      // Assert
      expect(CollectionService.createCollection).toBeCalledWith(tradingPartnerName, newCollectionName);
      expect(isLoading.value).toBeFalsy();
    });
  });
});
