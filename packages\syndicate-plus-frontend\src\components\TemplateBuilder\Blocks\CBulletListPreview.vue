<template>
  <div class="bullets mt-20px py-10px">
    <q-list>
      <q-item v-for="(bullet, index) in bulletList" :key="index" class="min-h-25px py-0px">
        <q-item-section side class="pr-7px">
          <q-icon class="text-primary" :name="bulletCheckmark ? 'mdi-check' : 'mdi-circle-small'" size="28px" />
        </q-item-section>
        <q-item-section avatar>
          <p class="bullet-text mb-0px">{{ bullet }}</p>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  bulletList: {
    type: Array<string>,
    default: () => ({}),
  },
  bulletCheckmark: {
    type: Boolean,
    default: false,
  },
});
</script>
<style lang="scss" scoped>
.bullet-text {
  font-size: 13px;
}
</style>
