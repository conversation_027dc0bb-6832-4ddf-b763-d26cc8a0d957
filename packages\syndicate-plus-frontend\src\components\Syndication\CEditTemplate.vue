<template>
  <div class="edit-template-container">
    <h3 class="pb-6">
      {{ $t('syndicate_plus.syndication.enhanced_content_dialog.or_edit_template') }}
    </h3>
    <c-select
      v-model="selectedEditTemplate"
      :options="templates"
      option-value="aplusTemplateId"
      option-label="templateName"
      :label="$t('syndicate_plus.common.template')"
    />
    <c-btn
      :label="$t('syndicate_plus.syndication.enhanced_content_dialog.edit_template')"
      color="surface"
      class="mr-2"
      @click="$emit('on-edit-template-select', selectedEditTemplate)"
    />
    <c-btn
      :label="$t('syndicate_plus.syndication.enhanced_content_dialog.create_new_template')"
      color="surface"
      @click="$emit('on-create-new-template')"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { APlusTemplate } from '@customTypes/Aplus';

defineProps({
  templates: {
    type: Array<APlusTemplate>,
    default: [] as any[],
  },
});

defineEmits(['on-edit-template-select', 'on-create-new-template']);
const selectedEditTemplate = ref<APlusTemplate>();
</script>
