variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'euw'
  stackType: 'dev4a'

  # Deploy dependencies
  dependsOn: 'Build_euwDev4a'
  dependsOnSecond: 'Build_euwDev4a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-dev4a-euw.productmarketingcloud.com'

  auth0Domain: 'auth-dev.syndic8.io'
  auth0ClientId: 'twD9zFV3gROvoMmRyVDq2kU6fcjZsATW'
  auth0Connection: 'inriver-prod-openid'

  fileServerUrl: 'https://app.infrared.inriver.syndic8.io'
  requestTradingPartnerUrl: 'https://community.inriver.com/hc/en-us/requests/new?ticket_form_id=8783121346204'

  apiUrl: 'https://api.infrared.inriver.syndic8.io'

  testLocalUrl: 'https://test.productmarketingcloud.com/'
  testUserName: <EMAIL>
  testUserPassword: Password123$
  testPortalUrl: https://portal-dev4a-euw.productmarketingcloud.com/

  allowMissingOrgMapping: true

  # Application insights instrumentation key
  instrumentationKey: '072f4b7f-a1ec-45ee-b394-931eff71025c'

  # Environments with the price import enabled
  priceImportEnabledEnvs: 'dev-projectboston-dev1,dev-projectboston-devqa'

  # Output adapter API
  environmentGuid: 'dev-projectboston-devqa'
