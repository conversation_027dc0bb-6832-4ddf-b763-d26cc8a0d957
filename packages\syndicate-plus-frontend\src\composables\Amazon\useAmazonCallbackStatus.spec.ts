import { vi, describe, beforeEach, it, expect } from 'vitest';
import { useAmazonCallbackStatus } from '@composables/Amazon';
import { notify } from '@inriver/inri';
import { AmazonCallbackStatus } from '@enums/Amazon';

const mocks = vi.hoisted(() => {
  return {
    useRoute: vi.fn(),
  };
});
vi.mock('vue-router', () => {
  return {
    useRoute: mocks.useRoute,
  };
});
const i18n = vi.fn().mockReturnValue({
  t: vi.fn(),
});
const notifyError = vi.spyOn(notify, 'error');
notifyError.mockImplementation(() => vi.fn());
const notifySuccess = vi.spyOn(notify, 'success');
notifySuccess.mockImplementation(() => vi.fn());

describe('useAmazonCallbackStatus', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('showAmazonCallbackStatusNotification', () => {
    it('shows a notification of success if query contains `Success` status', async () => {
      // Arrange
      mocks.useRoute.mockReturnValue({
        query: {
          status: 'Success',
        },
      });
      const { showAmazonCallbackStatusNotification } = useAmazonCallbackStatus(i18n);

      // Act
      showAmazonCallbackStatusNotification();

      // Assert
      expect(notifySuccess).toHaveBeenCalledOnce();
    });

    it('shows a notification of success if query contains `success` status', async () => {
      // Arrange
      mocks.useRoute.mockReturnValue({
        query: {
          status: AmazonCallbackStatus.SUCCESS,
        },
      });
      const { showAmazonCallbackStatusNotification } = useAmazonCallbackStatus(i18n);

      // Act
      showAmazonCallbackStatusNotification();

      // Assert
      expect(notifySuccess).toHaveBeenCalledOnce();
    });

    it('shows an error notification if query contains error status', async () => {
      // Arrange
      mocks.useRoute.mockReturnValue({
        query: {
          status: AmazonCallbackStatus.ERROR,
        },
      });
      const { showAmazonCallbackStatusNotification } = useAmazonCallbackStatus(i18n);

      // Act
      showAmazonCallbackStatusNotification();

      // Assert
      expect(notifyError).toHaveBeenCalledOnce();
    });

    it('does not show a notification if query does not contain status', async () => {
      // Arrange
      mocks.useRoute.mockReturnValue({
        query: {},
      });
      const { showAmazonCallbackStatusNotification } = useAmazonCallbackStatus(i18n);

      // Act
      showAmazonCallbackStatusNotification();

      // Assert
      expect(notifyError).not.toHaveBeenCalled();
      expect(notifySuccess).not.toHaveBeenCalled();
    });
  });
});
