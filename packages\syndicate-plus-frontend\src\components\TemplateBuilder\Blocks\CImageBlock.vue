<template>
  <div class="text-center">{{ $t('syndicate_plus.aplus_define_template.click_to_add_image') }}</div>
  <div class="text-center mb-20px font-bold">{{ dimensionsDisplayName }}</div>
  <q-input
    v-model="imageUrl"
    v-bind="$inri.input"
    hide-bottom-space
    :label="$t('syndicate_plus.aplus_define_template.image_url')"
    :autofocus="true"
    data-testid="cm-image-url"
    @keyup.enter="handleUrlUpdate"
    @blur="handleUrlUpdate"
  />
</template>
<script lang="ts" setup>
import { ref, watch, PropType } from 'vue';
import { Dimension } from '@customTypes/Aplus';
import { useImageDimensions } from '@composables/AplusTemplateBuilder';

const props = defineProps({
  initValue: {
    type: String,
    default: '',
  },
  dimensions: {
    type: Object as PropType<Dimension>,
    default: () => ({}),
  },
});

const emit = defineEmits(['url-updated']);

// Refs
const imageUrl = ref(props.initValue);

// Composables
const { dimensionsDisplayName } = useImageDimensions(props.dimensions);

// Functions
const handleUrlUpdate = () => {
  emit('url-updated', imageUrl.value);
};

// Lifecycle methods
watch(
  () => props.initValue,
  () => {
    imageUrl.value = props.initValue;
  }
);
</script>

<style scoped lang="scss">
:deep(.q-field__control) {
  min-width: 400px !important;
}
</style>
