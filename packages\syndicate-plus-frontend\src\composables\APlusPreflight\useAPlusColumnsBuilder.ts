import { ref } from 'vue';
import { GridService } from '@services';
import { HeaderType } from '@enums';
import { PreflightColumnHelper } from '@services/helper';
import { AplusPreflightColumn } from '@customTypes/Aplus';

export default function useAplusColumnsBuilder(destinationId: number) {
  // Refs
  const columns = ref<AplusPreflightColumn[]>([]);

  // Functions
  const buildColumns = async (templateName: string): Promise<void> => {
    columns.value = [];

    const headers = await GridService.getHeaders(HeaderType.APLUS, destinationId, templateName);
    if (!headers?.length) {
      return;
    }

    headers.forEach((header) => {
      if (!header.headerName) {
        return;
      }

      columns.value.push({
        name: header.headerName,
        field: header.field,
        label: header.field,
        isExtraColumn: PreflightColumnHelper.isExtendedColumn(header),
      } as AplusPreflightColumn);
    });
  };

  return { columns, buildColumns };
}
