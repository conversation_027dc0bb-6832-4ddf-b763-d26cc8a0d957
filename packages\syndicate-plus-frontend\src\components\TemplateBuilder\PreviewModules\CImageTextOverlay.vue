<template>
  <div class="mx-auto min-w-1000px w-1000px image-text-overlay-preview" data-testid="image-text-overlay">
    <div class="container">
      <c-caption-angle-image-block-preview
        :image-data="module.data.image1"
        class="w-full h-full"
        image-data-class="w-full h-full"
      />
      <div v-if="isOverlayVisible" class="overlay overflow-hidden">
        <h3 class="font-bold p-5px overflow-hidden text-wrap-word-break">{{ module.data.headline1 }}</h3>
        <c-html-preview class="max-h-200px" :html="module.data.body1" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, ImageTextOverlayData } from '@customTypes/Aplus';
import { CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<ImageTextOverlayData>>(() => {
  return store.getModuleByIndex(props.index);
});

const isOverlayVisible = computed(() => {
  return module.value.data.headline1 || module.value.data.body1;
});
</script>

<style lang="scss" scoped>
.image-text-overlay-preview {
  overflow: hidden;

  .container {
    position: relative;
    width: 970px;
    height: 300px;

    .overlay {
      max-width: 400px;
      overflow: hidden;
      position: absolute;
      right: 50px;
      top: 20%;
      background-color: rgba(0, 0, 0, 0.7);
      color: var(--surface-color);
      padding: 10px;
    }

    :deep() {
      img {
        object-fit: cover;
      }
    }
  }
}
</style>
