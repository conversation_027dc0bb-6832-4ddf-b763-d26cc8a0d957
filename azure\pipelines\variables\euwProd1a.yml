variables:
  # Stack
  productName: 'syndicate-plus-frontend'
  region: 'euw'
  stackType: 'prod1a'

  # Deploy dependencies
  dependsOn: 'Build_euwProd1a'
  dependsOnSecond: 'Build_euwProd1a'

  # Service connection
  serviceConnection: 'pmc2-${{ variables.region }}-${{ variables.stackType }}-rg-app-servicetier_appsvc-iPMC'

  # cName Static Web App
  customDomain: 'syndicate-plus-frontend-prod1a-euw.productmarketingcloud.com'

  auth0Domain: 'auth-dev.syndic8.io'
  auth0ClientId: 'ZmDqG4j7MFQVr2yougZHmhPOZAoZlGAy'
  auth0Connection: 'inriver-prod-openid'

  apiUrl: 'https://api.syndic8.io'
  fileServerUrl: 'https://app.syndic8.io'

  allowMissingOrgMapping: false
  
  # Application insights instrumentation key
  instrumentationKey: '32d93356-9fdf-4e13-bebb-e71ed2171131'

  # Infrared override
  infraredAuth0Domain: 'auth-dev.syndic8.io'
  infraredAuth0ClientId: 'twD9zFV3gROvoMmRyVDq2kU6fcjZsATW'
  infraredAuth0Connection: 'inriver-prod-openid'
  infraredApiUrl: 'https://api.infrared.inriver.syndic8.io'
  infraredFileServerUrl: 'https://app.infrared.inriver.syndic8.io'
  infraredInstrumentationKey: '32d93356-9fdf-4e13-bebb-e71ed2171131'
  infraredEnvs: 'prod-visualcomfortandco-dev'

  # Environments with the price import enabled
  priceImportEnabledEnvs: 'prod-marshallgroup-test,prod-marshallgroup-prod'
