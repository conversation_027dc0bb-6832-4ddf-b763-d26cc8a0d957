<template>
  <div class="mx-auto p-20px min-w-1000px w-1000px">
    <q-input
      v-model="module.data.headline"
      :class="numberOfBlocks === 3 ? 'mx-10px' : ''"
      v-bind="$inri.input"
      hide-bottom-space
      :label="$t('syndicate_plus.aplus_define_template.headline')"
      :maxlength="Validation.imagesAndText.mainHeadline.maxLength"
      counter
      @keyup.enter="updateModel"
      @blur="updateModel"
      @drop="onDrop(module.data.headline, 'headline')"
    />
    <div class="flex flex-row flex-nowrap justify-center">
      <div
        v-for="orderIndex in numberOfBlocks"
        :key="orderIndex"
        class="module-block text-wrap-word-break m-10px"
        :class="numberOfBlocks === 3 ? 'three-image' : 'four-image'"
      >
        <c-caption-angle-image-block
          v-model="module.data[`image${orderIndex}`]"
          :dimensions="numberOfBlocks === 3 ? dimensions3images : dimensions4images"
          :hide-caption="true"
          :max-alt-text-length="Validation.imagesAndText.imageAltText.maxLength"
          @update-model="updateModel"
          @url-updated="(url) => handleUrlUpdate(url, orderIndex)"
        />
        <q-input
          v-model="module.data[`headline${orderIndex}`]"
          v-bind="$inri.input"
          hide-bottom-space
          :label="$t('syndicate_plus.aplus_define_template.headline')"
          :maxlength="Validation.imagesAndText.headline.maxLength"
          counter
          @keyup.enter="updateModel"
          @blur="updateModel"
          @drop="onDrop(module.data[`headline${orderIndex}`], `headline${orderIndex}`)"
        />
        <c-text-editor
          v-model="module.data[`body${orderIndex}`]"
          :max-length="Validation.imagesAndText.body.maxLength"
          @on-field-drop="onDrop(module.data[`body${orderIndex}`], `body${orderIndex}`)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onUpdated } from 'vue';
import { useTemplateBuilderStore, useFieldsTabStore } from '@stores/TemplateBuilder';
import { CTextEditor, CCaptionAngleImageBlock } from '@components/TemplateBuilder/Blocks';
import { useFourImagesAndText, useThreeImagesAndText } from '@composables/AplusTemplateBuilder';
import { Dimension } from '@customTypes/Aplus';
import { Validation } from '@const';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
  numberOfBlocks: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();
const fieldsStore = useFieldsTabStore();

// Composables
const { module, initData } = props.numberOfBlocks === 4 ? useFourImagesAndText() : useThreeImagesAndText();

// Variables
const dimensions4images = { width: 220, height: 220 } as Dimension;
const dimensions3images = { width: 300, height: 300 } as Dimension;

// Functions
const handleUrlUpdate = (url: string, orderIndex: number) => {
  module.value.data[`image${orderIndex}`].angle = url;
  updateModel();
};

const onDrop = (value: string, moduleParameter) => {
  const result = fieldsStore.onDrop(value);
  if (result) {
    module.value.data[moduleParameter] = result;
    updateModel();
  }
};

const updateModel = () => {
  if (!module.value) {
    return;
  }

  store.commitChanges(props.index, module.value);
};

const init = () => {
  module.value = store.getModuleByIndex(props.index);
  initData();
};

// Lifecycle methods
onBeforeMount(() => init());

onUpdated(() => init());
</script>

<style lang="scss" scoped>
.module-block {
  &.three-image {
    width: 310px;
  }

  &.four-image {
    width: 230px;
  }
}
</style>
