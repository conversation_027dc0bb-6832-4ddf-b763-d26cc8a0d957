import { PreflightColumnDto } from '@dtos';
import { findLastIndex } from 'lodash';

export abstract class BaseStrategy {
  private readonly rows: any[];
  private readonly columns: PreflightColumnDto[];

  constructor(rows: any[], columns: PreflightColumnDto[]) {
    this.rows = rows;
    this.columns = columns;
  }

  protected getNextAvailableRowId = (currentRowId: number): number => {
    const tempNextRowId = currentRowId + 1;

    return tempNextRowId > this.rows.length - 1 ? 0 : tempNextRowId;
  };

  protected getPreviousAvailableRowId = (currentRowId: number): number => {
    const tempPreviousRowId = currentRowId - 1;

    return tempPreviousRowId < 0 ? this.rows.length - 1 : tempPreviousRowId;
  };

  protected getNextAvailableColumnId = (currentColumnId: number): number =>
    this.columns.findIndex((col: PreflightColumnDto, index: number) => index > currentColumnId && col.editable);

  protected getFirstAvailableColumnId = (): number => this.columns.findIndex((col: PreflightColumnDto) => col.editable);

  protected getPreviousAvailableColumnId = (currentColumnId: number): number =>
    findLastIndex(this.columns, (col: PreflightColumnDto, index: number) => index < currentColumnId && col.editable);

  protected getLastAvailableColumnId = (): number =>
    findLastIndex(this.columns, (col: PreflightColumnDto) => col.editable);
}
