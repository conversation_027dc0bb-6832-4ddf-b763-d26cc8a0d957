import { Ref, computed } from 'vue';
import { ProductGrouping, SyndicationPageTabNames } from '@enums';
import { useRouter } from '@composables/useRouter';
import { notify } from '@inriver/inri';
import { goToScopedPageWithSelect } from '@services/Details/ScopedProductSelectionService';

export default function useEditPriceButton(
  useI18n: Function,
  tab: Ref<SyndicationPageTabNames | null>,
  numberOfSelectedProducts: Ref<number>,
  destinationId: number,
  subCatalogId: number
) {
  // Variables
  const maxNumberOfProducts = 5;

  const { t } = useI18n();
  // Composables
  const { goToPage } = useRouter();

  // Computed
  const isEditPriceButtonVisible = computed(
    () => (!tab.value || tab.value == SyndicationPageTabNames.Products) && !!numberOfSelectedProducts.value
  );
  const isGoToEditPriceAllowed = computed(
    () => numberOfSelectedProducts.value > 0 && numberOfSelectedProducts.value <= maxNumberOfProducts
  );

  // Functions
  const goToEditPricePage = (
    groupBy: ProductGrouping,
    selectedProductIds: string[],
    selectedProductNames: string[]
  ) => {
    if (!isEditPriceButtonVisible.value || !isGoToEditPriceAllowed.value) {
      notify.error(`${t('syndicate_plus.syndication.price_disabled')}`, {
        position: 'bottom-right',
      });

      return;
    }

    goToScopedPageWithSelect(
      goToPage,
      'price-page',
      {
        subCatalogId,
        destinationId,
      },
      groupBy,
      selectedProductIds,
      selectedProductNames
    );
  };

  return { isGoToPriceAllowed: isGoToEditPriceAllowed, isEditPriceButtonVisible, goToEditPricePage };
}
