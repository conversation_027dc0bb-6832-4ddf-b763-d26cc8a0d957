<template>
  <c-dialog
    class="c-dialog"
    data-testid="confirm-delete-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-confirm-button
    hide-cancel-button
    @cancel="cancel"
  >
    <h3>{{ text }}</h3>

    <div class="action-buttons mt-10">
      <c-btn
        :label="$t('syndicate_plus.common.delete')"
        @click="logEventAndClick(ActionName.CONFIRM_DELETE_DIALOG_DELETE, confirm)"
      >
      </c-btn>
      <c-btn
        class="ml-10px"
        color="surface"
        :label="$t('syndicate_plus.common.cancel')"
        @click="logEventAndClick(ActionName.CONFIRM_DELETE_DIALOG_CANCEL, cancel)"
      >
      </c-btn>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';

// Props
defineProps({
  text: {
    type: String,
    default: '',
  },
});

// Composables
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel } = useDialog();

const emit = defineEmits(['confirm']);

// Functions
const confirm = async () => {
  emit('confirm');
};
</script>

<style scoped lang="scss"></style>
