export interface Syndication {
  Id: number;
  FileFormatId?: number;
  DynamicFormatId?: number;
  WorkareaName?: string;
  MappingId: number;
  MappingName: string;
  ExtensionId: string;
  IsPreviewEnabled: boolean;
  RunPreview: boolean;
  EnableSKU: boolean;
  Name?: string;
  WorkareaId?: string;
  OutputFormat?: string;
  ExtensionDisplayName?: string;
  EntityIds?: number[];
  OutputDestination?: string;
  MappingSource?: string;
  ChannelId?: number;
  IdentifierType?: string;
}
