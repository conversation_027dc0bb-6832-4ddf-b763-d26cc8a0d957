import { ref, computed, Ref } from 'vue';
import { CollectionDetails } from '@customTypes';

export default function useAddToCollection(isLoading: Ref<boolean>) {
  // Refs
  const selectedCollection = ref<CollectionDetails>();

  // Computed
  const addToCollectionIsDisabled = computed(() => {
    return !selectedCollection.value || isLoading.value;
  });

  return { addToCollectionIsDisabled, selectedCollection };
}
