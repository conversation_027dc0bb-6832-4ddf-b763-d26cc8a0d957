import { test, expect } from '@fixtures/localPageFixture';
import { WelcomeToSyndicatePlusPage } from '@pages/welcomePage/welcomeToSyndicatePlus.page';

test.describe('trading partners', () => {
  let welcomeToSyndicatePlusPage: WelcomeToSyndicatePlusPage;

  test.beforeAll(async ({ localPage }) => {
    welcomeToSyndicatePlusPage = new WelcomeToSyndicatePlusPage(localPage);
  });

  test.beforeEach(async ({ localPage, envConfig }) => {
    await localPage.goto(envConfig.Url);
  });

  test('Get trading partner cards main view (connected)', async () => {
    await expect.soft(welcomeToSyndicatePlusPage.retailerCards, 'Unexpected retailer cards').toHaveCount(2);
    await expect.soft(welcomeToSyndicatePlusPage.marketCards).toHaveCount(1);
    const numberOfCoreCards = await welcomeToSyndicatePlusPage.coreCards.count();
    await expect(numberOfCoreCards).toBeGreaterThanOrEqual(6);
  });

  test('Get trading partner cards main view (show all)', async () => {
    await welcomeToSyndicatePlusPage.showAllButton.click();

    await expect(welcomeToSyndicatePlusPage.retailerCards.first()).toBeVisible();
    await expect
      .soft(await welcomeToSyndicatePlusPage.retailerCards.count(), 'Unexpected number of retailer cards')
      .toBeGreaterThanOrEqual(15);
    await expect
      .soft(await welcomeToSyndicatePlusPage.marketCards.count(), 'Unexpected number of Marketplace cards')
      .toBeGreaterThanOrEqual(6);
  });
});
