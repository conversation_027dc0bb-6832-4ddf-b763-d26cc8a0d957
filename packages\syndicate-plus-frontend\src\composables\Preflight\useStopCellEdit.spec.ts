import { vi, describe, it, expect } from 'vitest';
import { ref, computed } from 'vue';
import { useStopCellEdit } from '@composables/Preflight';
import { StopCellEditType, EditableTableViewFilterType, TableViewFilterType } from '@enums';
import { DataToUpdateModel } from '@customTypes';
import { PreflightColumnDto } from '@dtos';

vi.mock('@httpservices/ApiSyndicateHttpService');

describe('useStopCellEdit', () => {
  describe('calculateNextRowAndColumn', () => {
    it('sets next row and column id correctly for Esc type', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref(null);
      const columns = ref(null);
      const data = {} as DataToUpdateModel;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Esc);

      // Assert
      expect(nextColumnId.value).toBe(-1);
      expect(nextRowId.value).toBe(-1);
    });

    it('sets next row and column id correctly for Enter type', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref(null);
      const currentColumnId = 10;
      const currentRowId = 0;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Enter);

      // Assert
      expect(nextColumnId.value).toBe(currentColumnId);
      expect(nextRowId.value).toBe(currentRowId + 1);
    });

    it('sets next row and column id correctly for Enter type if the row is last', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref(null);
      const currentColumnId = 10;
      const currentRowId = 5;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Enter);

      // Assert
      expect(nextColumnId.value).toBe(currentColumnId);
      expect(nextRowId.value).toBe(0);
    });

    it('sets previous row and column id correctly for ShiftEnter type', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref(null);
      const currentColumnId = 10;
      const currentRowId = 5;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.ShiftEnter);

      // Assert
      expect(nextColumnId.value).toBe(currentColumnId);
      expect(nextRowId.value).toBe(currentRowId - 1);
    });

    it('sets next row and column id correctly for Tab type', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref([
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ]);
      const currentColumnId = 2;
      const currentRowId = 0;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Tab);

      // Assert
      expect(nextColumnId.value).toBe(4);
      expect(nextRowId.value).toBe(currentRowId);
    });

    it('sets next row and column id correctly for Tab type if preflightEditViewFilter is true', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref([
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
      ]);
      const currentColumnId = 2;
      const currentRowId = 0;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Tab);

      // Assert
      expect(nextColumnId.value).toBe(4);
      expect(nextRowId.value).toBe(currentRowId);
    });

    it('sets next row and column id correctly for Tab type if available column is last', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref([
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
      ]);
      const currentColumnId = 2;
      const currentRowId = 0;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Tab);

      // Assert
      expect(nextColumnId.value).toBe(0);
      expect(nextRowId.value).toBe(currentRowId + 1);
    });

    it('sets next row and column id correctly for Tab type if available column and row is last', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const columns = ref([
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
        {
          editable: true,
        } as PreflightColumnDto,
        {
          editable: false,
        } as PreflightColumnDto,
      ]);
      const currentColumnId = 2;
      const currentRowId = 5;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Tab);

      // Assert
      expect(nextColumnId.value).toBe(0);
      expect(nextRowId.value).toBe(0);
    });

    it('sets next row and column id correctly for Blur type', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref(null);
      const columns = ref(null);
      const data = {} as DataToUpdateModel;
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(table, rows, columns);

      // Act
      calculateNextRowAndColumn(data, StopCellEditType.Blur);

      // Assert
      expect(nextColumnId.value).toBe(-1);
      expect(nextRowId.value).toBe(-1);
    });
  });

  describe('calculateNextRowAndColumn with filter view changes', () => {
    it('sets next row and column id correctly for Tab type if preflightEditViewFilter is changed several times', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const preflightEditViewFilter = ref<EditableTableViewFilterType>(EditableTableViewFilterType.SHOW_ALL);
      const columns = ref([
        {
          name: 'column1',
          editable: true,
        },
        {
          name: 'column2',
          editable: true,
        },
        {
          name: 'column3',
          editable: false,
        },
        {
          name: 'column4',
          editable: true,
        },
        {
          name: 'column5',
          editable: false,
        },
        {
          name: 'column6',
          editable: true,
        },
      ]);
      const currentColumnId = 3;
      const currentRowId = 0;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const displayedColumns = computed(() => {
        if (preflightEditViewFilter.value === EditableTableViewFilterType.SHOW_ALL) {
          return columns.value.map((column) => column.name);
        }
        return columns.value.filter((column) => column.editable).map((column) => column.name);
      });
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(
        table,
        rows,
        columns,
        displayedColumns
      );

      // Act
      preflightEditViewFilter.value = EditableTableViewFilterType.SHOW_ONLY_EDITABLE;
      calculateNextRowAndColumn(data, StopCellEditType.Tab);
      preflightEditViewFilter.value = EditableTableViewFilterType.SHOW_ALL;
      calculateNextRowAndColumn(data, StopCellEditType.Tab);

      // Assert
      expect(nextColumnId.value).toBe(5);
      expect(nextRowId.value).toBe(currentRowId);
    });

    it('sets next row and column id correctly for Tab type if available column is last and preflightEditViewFilter is changed to true', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const preflightEditViewFilter = ref<EditableTableViewFilterType>(EditableTableViewFilterType.SHOW_ALL);
      const columns = ref([
        {
          name: 'column1',
          editable: true,
        },
        {
          name: 'column2',
          editable: true,
        },
        {
          name: 'column3',
          editable: false,
        },
        {
          name: 'column4',
          editable: true,
        },
        {
          name: 'column5',
          editable: false,
        },
        {
          name: 'column6',
          editable: true,
        },
      ]);
      const currentColumnId = 3;
      const currentRowId = 0;
      const data = {
        columnIndex: currentColumnId,
        rowId: currentRowId,
      } as DataToUpdateModel;
      rows.value.length = 5;
      const displayedColumns = computed(() => {
        if (preflightEditViewFilter.value === EditableTableViewFilterType.SHOW_ALL) {
          return columns.value.map((column) => column.name);
        }
        return columns.value.filter((column) => column.editable).map((column) => column.name);
      });
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(
        table,
        rows,
        columns,
        displayedColumns
      );

      // Act
      preflightEditViewFilter.value = EditableTableViewFilterType.SHOW_ONLY_EDITABLE;
      calculateNextRowAndColumn(data, StopCellEditType.Tab);

      // Assert
      expect(nextColumnId.value).toBe(0);
      expect(nextRowId.value).toBe(currentRowId + 1);
    });
  });

  describe('calculateNextRowAndColumn with TableViewFilterType changes', () => {
    it('sets next row and column id correctly for Tab type if the mandatory fields filter is on', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const tableViewFilterType = ref<TableViewFilterType>(TableViewFilterType.SHOW_ALL);
      const columns = ref([
        {
          name: 'column1',
          mandatory: true,
          editable: true,
        },
        {
          name: 'column2',
          mandatory: true,
          editable: true,
        },
        {
          name: 'column3',
          mandatory: false,
          editable: false,
        },
        {
          name: 'column4',
          mandatory: true,
          editable: true,
        },
        {
          name: 'column5',
          mandatory: false,
          editable: false,
        },
        {
          name: 'column6',
          mandatory: true,
          editable: true,
        },
        {
          name: 'column7',
          mandatory: true,
          editable: false,
        },
        {
          name: 'column8',
          mandatory: false,
          editable: true,
        },
        {
          name: 'column9',
          mandatory: true,
          editable: false,
        },
      ]);
      const currentColumnId = 3;
      const currentRowId = 0;
      rows.value.length = 5;
      const displayedColumns = computed(() => {
        if (tableViewFilterType.value === TableViewFilterType.SHOW_ALL) {
          return columns.value.map((column) => column.name);
        }
        return columns.value.filter((column) => column.mandatory).map((column) => column.name);
      });
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(
        table,
        rows,
        columns,
        displayedColumns
      );

      // Act
      tableViewFilterType.value = TableViewFilterType.SHOW_ONLY_MANDATORY;
      nextColumnId.value = currentColumnId;
      nextRowId.value = currentRowId;
      calculateNextRowAndColumn(
        {
          rowId: nextRowId.value,
          columnIndex: nextColumnId.value,
        } as DataToUpdateModel,
        StopCellEditType.Tab
      );
      calculateNextRowAndColumn(
        {
          rowId: nextRowId.value,
          columnIndex: nextColumnId.value,
        } as DataToUpdateModel,
        StopCellEditType.Tab
      );
      calculateNextRowAndColumn(
        {
          rowId: nextRowId.value,
          columnIndex: nextColumnId.value,
        } as DataToUpdateModel,
        StopCellEditType.Tab
      );

      // Assert
      expect(nextColumnId.value).toBe(2);
      expect(nextRowId.value).toBe(currentRowId + 1);
    });

    it('sets next row and column id correctly for Tab type if the required fields filter is on', async () => {
      // Arrange
      const table = ref(null);
      const rows = ref([]);
      const tableViewFilterType = ref<TableViewFilterType>(TableViewFilterType.SHOW_ALL);
      const columns = ref([
        {
          name: 'column1',
          recommended: true,
          editable: true,
        },
        {
          name: 'column2',
          recommended: false,
          editable: true,
        },
        {
          name: 'column3',
          recommended: false,
          editable: false,
        },
        {
          name: 'column4',
          recommended: true,
          editable: true,
        },
        {
          name: 'column5',
          recommended: false,
          editable: false,
        },
        {
          name: 'column6',
          recommended: true,
          editable: true,
        },
        {
          name: 'column7',
          recommended: true,
          editable: false,
        },
        {
          name: 'column8',
          recommended: false,
          editable: true,
        },
        {
          name: 'column9',
          recommended: true,
          editable: true,
        },
      ]);
      const currentColumnId = 4;
      const currentRowId = 0;
      rows.value.length = 5;
      const displayedColumns = computed(() => {
        if (tableViewFilterType.value === TableViewFilterType.SHOW_ALL) {
          return columns.value.map((column) => column.name);
        }
        return columns.value.filter((column) => column.recommended).map((column) => column.name);
      });
      const { calculateNextRowAndColumn, nextColumnId, nextRowId } = useStopCellEdit(
        table,
        rows,
        columns,
        displayedColumns
      );

      // Act
      tableViewFilterType.value = TableViewFilterType.SHOW_ONLY_RECOMMENDED;
      nextColumnId.value = currentColumnId;
      nextRowId.value = currentRowId;
      calculateNextRowAndColumn(
        {
          rowId: nextRowId.value,
          columnIndex: nextColumnId.value,
        } as DataToUpdateModel,
        StopCellEditType.Tab
      );
      calculateNextRowAndColumn(
        {
          rowId: nextRowId.value,
          columnIndex: nextColumnId.value,
        } as DataToUpdateModel,
        StopCellEditType.Tab
      );
      calculateNextRowAndColumn(
        {
          rowId: nextRowId.value,
          columnIndex: nextColumnId.value,
        } as DataToUpdateModel,
        StopCellEditType.Tab
      );

      // Assert
      expect(nextColumnId.value).toBe(2);
      expect(nextRowId.value).toBe(currentRowId + 1);
    });
  });
});
