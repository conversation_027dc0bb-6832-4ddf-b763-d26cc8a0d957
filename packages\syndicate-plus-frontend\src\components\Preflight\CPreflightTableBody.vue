<template>
  <q-tr :class="isRowSelected ? 'selected' : ''" @click="emits('row-click', row)">
    <q-td
      v-for="(col, index) in cols"
      :id="`${index}-${row.processedId}`"
      :key="col.name"
      class="table-cell"
      :class="getCellStyle(row, col, index)"
      @click="startEditing(row, col, index)"
    >
      <c-error-tooltip
        v-if="cellHasValidationErrors(row, col)"
        :errors="cellErrorsMessage(row, col, ValidationResult.ERROR)"
        :warnings="cellErrorsMessage(row, col, ValidationResult.WARNING)"
      />
      <div v-if="col.name == PreflightColumnHelper.extendedColumnStatus">
        <c-small-square v-if="rowHasErrorsOrWarnings(row) === ValidationResult.NONE" :color="SquareColor.GREEN">
          <q-tooltip>{{ $t('syndicate_plus.preflight.status.success') }}</q-tooltip>
        </c-small-square>
        <c-small-square
          v-else-if="rowHasErrorsOrWarnings(row) === ValidationResult.WARNING"
          :color="SquareColor.ORANGE"
        >
          <q-tooltip>{{ $t('syndicate_plus.preflight.status.warning') }}</q-tooltip>
        </c-small-square>
        <c-small-square
          v-if="[ValidationResult.ERROR, ValidationResult.ERRORANDWARNING].includes(rowHasErrorsOrWarnings(props.row))"
          :color="SquareColor.RED"
        >
          <q-tooltip>{{ $t('syndicate_plus.preflight.status.error') }}</q-tooltip>
        </c-small-square>
      </div>
      <div v-if="col.isCustom" style="color: var(--color-grey-dark)">
        {{ row.productRow[col.field] }}
        <q-tooltip>{{ row.productRow[col.field] }}</q-tooltip>
      </div>
      <div v-else class="editable-cell">
        <c-editable-cell
          :value="row.row[col.field]"
          :is-edit-mode="isSelectedCell(row, index) && isEditingMode"
          @stop-editing="stopEditing"
        />
      </div>
    </q-td>
  </q-tr>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { SquareColor, StopCellEditType } from '@enums';
import { ValidationResult, ValidationError, ValidationErrors } from '@customTypes';
import { PreflightColumnHelper } from '@services/helper';
import { CSmallSquare, CErrorTooltip } from '@components';
import { CEditableCell } from '@components/Shared';
import { DataToUpdateModel } from '@customTypes';
import { usePreflightEditStore } from '@stores/PreflightStore';
import { storeToRefs } from 'pinia';

const props = defineProps({
  cols: {
    type: Array<any>,
    default: [],
  },
  row: {
    type: Object,
    default: {} as Object,
  },
  isEditingMode: {
    type: Boolean,
    default: false,
  },
  nextEditColumnId: {
    type: Number,
    reqired: false,
    default: -1,
  },
  nextEditRowId: {
    type: Number,
    reqired: false,
    default: -1,
  },
  isSelected: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['stop-editing', 'row-click']);

// Refs
const currentEdit = ref<DataToUpdateModel | null>(null);
const isRowSelected = ref<boolean>(props.isSelected);
const { dataToUpdate } = storeToRefs(usePreflightEditStore());

watchEffect(() => {
  isRowSelected.value = props.isSelected;
});

// Functions
const startEditing = (row, col, columnIndex: number) => {
  if (col.isCustom || !props.isEditingMode || !col.editable) {
    return;
  }

  currentEdit.value = {
    columnIndex,
    rowId: row.processedId,
    fieldName: col.internalField,
    productId: row.productRow.productId,
  } as DataToUpdateModel;
};

watchEffect(() => {
  if (props.nextEditColumnId >= 0 && props.row.processedId === props.nextEditRowId) {
    const column = props.cols[props.nextEditColumnId];
    column && startEditing(props.row, column, props.nextEditColumnId);
  }
});

const stopEditing = (newValue: string, initialValue: string, type: StopCellEditType): void => {
  const value = type === StopCellEditType.Esc ? initialValue : newValue;
  const data = {
    newValue: value,
    initialValue,
    ...currentEdit.value,
  } as DataToUpdateModel;
  emits('stop-editing', data, type);
  currentEdit.value = null;
};

const isSelectedCell = (row, columnIndex: number): boolean =>
  currentEdit.value !== null &&
  currentEdit.value.columnIndex === columnIndex &&
  currentEdit.value.rowId === row.processedId;

const isUpdatedCell = (row, col): boolean => {
  const values: DataToUpdateModel[] = Object.values(dataToUpdate.value);
  return values.some(
    (data: DataToUpdateModel) => data.fieldName === col.internalField && data.rowId === row.processedId
  );
};

const getCellStyle = (row, col, columnIndex: number): string => {
  if (isSelectedCell(row, columnIndex)) {
    return 'editing';
  }

  if (isUpdatedCell(row, col)) {
    return 'edited';
  }

  if (props.isEditingMode && !col.editable && !col.isCustom) {
    return 'not-editable';
  }

  if (col.isExtraColumn) {
    return isRowSelected.value ? 'sticky extra-column-selected-cell' : 'sticky extra-column-cell';
  }

  const errors = cellErrorsMessage(row, col, ValidationResult.ERROR);
  const warnings = cellErrorsMessage(row, col, ValidationResult.WARNING);

  if (errors?.length > 0) {
    return 'required-error';
  }

  if (warnings?.length > 0) {
    return 'warning';
  }

  return '';
};

const cellErrorsMessage = (row, col, severity: ValidationResult): string[] => {
  const errors = row.errors[col.field] as ValidationError[];

  return errors?.filter((e) => e.severity == severity).map((e) => e.message);
};

const cellHasValidationErrors = (row, col): boolean => {
  const errors = row.errors[col.field] as ValidationError[];

  return !!errors && errors.length !== 0;
};

const rowHasErrorsOrWarnings = (row: any): ValidationResult => {
  const errors = row.errors as ValidationErrors;
  const hasAnyValidationReponses = (errors: ValidationErrors, severity: string): boolean => {
    return Object.keys(errors).some((key) => errors[key].some((e) => e.severity === severity));
  };

  const hasError = hasAnyValidationReponses(errors, ValidationResult.ERROR);
  const hasWarning = hasAnyValidationReponses(errors, ValidationResult.WARNING);

  if (hasError && hasWarning) {
    return ValidationResult.ERRORANDWARNING;
  } else if (hasError) {
    return ValidationResult.ERROR;
  } else if (hasWarning) {
    return ValidationResult.WARNING;
  }
  return ValidationResult.NONE;
};
</script>

<style scoped lang="scss">
td:nth-child(1),
td:nth-child(2) {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

td:nth-child(1) {
  left: 0;
  min-width: 150px;
  max-width: 150px;
}

td:nth-child(2) {
  left: 150px;
  min-width: 150px;
  max-width: 150px;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;

  &.extra-column-cell {
    background-color: #fafafa;
    z-index: 1;
  }

  &.extra-column-selected-cell {
    z-index: 1;
  }
}

.editing {
  border: 1px solid rgba(173, 102, 242);
}

.edited {
  border: 1px solid rgba(173, 102, 242);
  background-color: rgba(173, 102, 242, 0.1);
}

.not-editable {
  background-color: var(--color-grey-10);
}

.required-error {
  border-color: var(--color-red);
  background-color: var(--color-red-light);
}

.warning {
  border-color: var(--color-yellow);
  background-color: var(--color-yellow-light);
}

.editable-cell {
  height: 19.5px;
}
</style>
