import { Ref, computed } from 'vue';
import { SyndicationPageTabNames } from '@enums';
import { notify } from '@inriver/inri';
import { useRouter } from '@composables/useRouter';
import { ProductFilterDto } from '@dtos';
import { goToScopedPageWithSelect } from '@services/Details/ScopedProductSelectionService';

export default function useProductDetailsButton(
  useI18n: Function,
  tab: Ref<SyndicationPageTabNames | null>,
  filter: Ref<ProductFilterDto>,
  numberOfSelectedProducts: Ref<number>
) {
  // Variables
  const maxNumberOfProducts = 5;

  // Composables
  const { t } = useI18n && useI18n();
  const { goToPage } = useRouter();

  // Computed
  const isDetailsButtonVisible = computed(
    () => (!tab.value || tab.value == SyndicationPageTabNames.Products) && !!numberOfSelectedProducts.value
  );
  const isGoToDetailsAllowed = computed(
    () => numberOfSelectedProducts.value > 0 && numberOfSelectedProducts.value <= maxNumberOfProducts
  );

  // Functions
  const goToDetailsPage = (selectedProductIds: string[], selectedProductNames: string[]) => {
    goToDetailsPageBase('details-page', selectedProductIds, selectedProductNames);
  };

  const goToDetailsPageForCollection = (selectedProductIds: string[], selectedProductNames: string[]) => {
    goToDetailsPageBase('collection-details-page', selectedProductIds, selectedProductNames);
  };

  const goToDetailsPageForMedia = (selectedProductIds: string[], selectedProductNames: string[]) => {
    goToDetailsPageBase('media-details-page', selectedProductIds, selectedProductNames);
  };

  const goToDetailsPageBase = (pageId: string, selectedProductIds: string[], selectedProductNames: string[]) => {
    if (!isDetailsButtonVisible.value) {
      return;
    }

    if (!isGoToDetailsAllowed.value) {
      notify.error(`${t('syndicate_plus.syndication.details_disabled')}`, {
        position: 'bottom-right',
      });

      return;
    }

    const { groupBy } = filter.value;
    goToScopedPageWithSelect(goToPage, pageId, {}, groupBy.id, selectedProductIds, selectedProductNames);
  };

  return {
    isGoToDetailsAllowed,
    isDetailsButtonVisible,
    goToDetailsPage,
    goToDetailsPageForCollection,
    goToDetailsPageForMedia,
  };
}
