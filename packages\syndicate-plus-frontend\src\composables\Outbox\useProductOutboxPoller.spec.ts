import { it, describe, vi, vitest, expect, afterEach } from 'vitest';
import { useProductOutboxPoller } from '@composables/Outbox';
import { StatusModel } from '@utils/statusHelper';
import { ProductOutboxRow } from '@customTypes';
import { SquareColor } from '@enums/SquareColor';
import { ref, nextTick } from 'vue';
import { SyndicationPageTabNames } from '@enums';

import useInterval from '@composables/useInterval';
import { useProductOutboxStore } from '@stores/ProductOutboxStore';
import { OutboxStatus } from '@enums/OutboxStatus';

vi.mock('@composables/useInterval');
vi.mock('@stores/ProductOutboxStore');

const mockedStartInterval = vitest.fn();
const mockedStopInterval = vitest.fn();

(useInterval as any).mockReturnValue({ startInterval: mockedStartInterval, stopInterval: mockedStopInterval });
(useProductOutboxStore as any).mockReturnValue({ fetchDataSilently: vitest.fn(), getProductOutboxRows: vitest.fn() });

const { getProductOutboxRows } = useProductOutboxStore();

describe('useProductOutboxPoller', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should not start polling if there are no unfinished jobs and outbox tab is active', async () => {
    // Arrange
    const activeTab = ref(SyndicationPageTabNames.Outbox);

    const productOutboxRows = [
      {
        status: new StatusModel(OutboxStatus.COMPLETED, SquareColor.UNKNOWN),
        startDateTimeStamp: Date.now(),
      },
    ] as ProductOutboxRow[];
    getProductOutboxRows.mockReturnValue(productOutboxRows);

    // Act
    useProductOutboxPoller(activeTab, 0);
    await nextTick();

    // Assert
    expect(mockedStartInterval).not.toHaveBeenCalled();
  });

  it('should not start polling if there are unfinished jobs but outbox tab is not active', async () => {
    // Arrange
    const activeTab = ref(SyndicationPageTabNames.Products);

    const productOutboxRows = [
      {
        status: new StatusModel(OutboxStatus.IN_QUEUE, SquareColor.UNKNOWN),
        startDateTimeStamp: Date.now(),
      },
    ] as ProductOutboxRow[];
    getProductOutboxRows.mockReturnValue(productOutboxRows);

    // Act
    useProductOutboxPoller(activeTab, 0);
    await nextTick();

    // Assert
    expect(mockedStartInterval).not.toHaveBeenCalled();
  });

  it('should not start polling if there are unfinished jobs that are too old', async () => {
    // Arrange
    const activeTab = ref(SyndicationPageTabNames.Outbox);

    const yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).getTime();

    const productOutboxRows = [
      {
        status: new StatusModel(OutboxStatus.IN_QUEUE, SquareColor.UNKNOWN),
        startDateTimeStamp: yesterday,
      },
    ] as ProductOutboxRow[];
    getProductOutboxRows.mockReturnValue(productOutboxRows);

    // Act
    useProductOutboxPoller(activeTab, 0);
    await nextTick();

    // Assert
    expect(mockedStartInterval).not.toHaveBeenCalled();
  });

  it('should start polling if there are unfinished jobs and outbox tab is active', async () => {
    // Arrange
    const activeTab = ref(SyndicationPageTabNames.Outbox);

    const productOutboxRows = [
      {
        status: new StatusModel(OutboxStatus.IN_PROGRESS, SquareColor.UNKNOWN),
        startDateTimeStamp: new Date().getTime(),
      },
    ] as ProductOutboxRow[];
    getProductOutboxRows.mockReturnValue(productOutboxRows);

    // Act
    useProductOutboxPoller(activeTab, 0);
    await nextTick();

    // Assert
    expect(mockedStartInterval).toHaveBeenCalledOnce();
  });

  it('should stop polling once there are no more unfinished jobs', async () => {
    // Arrange
    const mockedUseInterval = useInterval as any;
    const unfinishedProductOutboxRows = [
      {
        status: new StatusModel(OutboxStatus.IN_PROGRESS, SquareColor.UNKNOWN),
        startDateTimeStamp: new Date().getTime(),
      },
    ] as ProductOutboxRow[];
    const completedProductOutboxRows = [
      {
        status: new StatusModel(OutboxStatus.COMPLETED, SquareColor.UNKNOWN),
        startDateTimeStamp: new Date().getTime(),
      },
    ] as ProductOutboxRow[];
    getProductOutboxRows.mockReturnValueOnce(unfinishedProductOutboxRows);
    getProductOutboxRows.mockReturnValueOnce(completedProductOutboxRows);

    // Act
    useProductOutboxPoller(ref(SyndicationPageTabNames.Outbox), 0);
    await nextTick();
    const refreshDataHandle = mockedUseInterval.mock.calls[mockedUseInterval.mock.calls.length - 1][2];

    await refreshDataHandle();

    // Assert
    expect(mockedStartInterval).toHaveBeenCalledOnce();
    expect(mockedStopInterval).toHaveBeenCalledOnce();
  });
});
