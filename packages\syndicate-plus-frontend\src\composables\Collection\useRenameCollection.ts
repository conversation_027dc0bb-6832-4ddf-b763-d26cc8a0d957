import { ref, computed, Ref } from 'vue';
import { CollectionService } from '@services';

export default function useRenameCollection(isLoading: Ref<boolean>, collectionName: string, collectionId: number) {
  // Refs
  const newCollectionName = ref(collectionName);

  // Computed
  const renameCollectionIsDisabled = computed(
    () =>
      isLoading.value || !newCollectionName.value?.trim() || newCollectionName.value?.trim() === collectionName?.trim()
  );

  // Functions
  const renameCollection = async (tradingPartnerName: string) => {
    isLoading.value = true;
    await CollectionService.renameCollection(collectionId, newCollectionName.value, tradingPartnerName);
    isLoading.value = false;
  };

  return { newCollectionName, renameCollectionIsDisabled, renameCollection };
}
