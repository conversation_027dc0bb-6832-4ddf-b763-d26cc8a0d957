<template>
  <c-dialog
    class="c-dialog"
    :title="title"
    :model-value="showDialog"
    :confirm-button-text="confirmButtonText"
    :hide-confirm-button="hideConfirmButton"
    :cancel-button-text="cancelButtonText"
    @keyup.enter="onConfirm"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    {{ text }}
    <div v-if="listText.length">
      <span v-for="(item, index) in listText" :key="index" class="mr-1">
        {{ item }}{{ index < listText.length - 1 ? ', ' : '.' }}
      </span>
    </div>
    <div v-if="text2">{{ text2 }}</div>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog } from '@inriver/inri';

defineProps({
  title: {
    type: String,
    default: '',
  },
  text: {
    type: String,
    default: '',
  },
  text2: {
    type: String,
    required: false,
    default: '',
  },
  confirmButtonText: {
    type: String,
    default: '',
  },
  hideConfirmButton: {
    type: Boolean,
    default: false,
  },
  cancelButtonText: {
    type: String,
    default: 'cancel',
  },
  listText: {
    type: Array,
    required: false,
    default: () => [],
  },
});

const emit = defineEmits(['handle-confirm', 'handle-cancel']);

// Composables
const { showDialog, cancel, confirmSuccess } = useDialog();

// Functions
const onConfirm = () => {
  confirmSuccess(null);
  emit('handle-confirm');
};

const onCancel = () => {
  cancel();
  emit('handle-cancel');
};
</script>
