<template>
  <c-section>
    <div class="c-value-mapping-tab-panel">
      <q-inner-loading :showing="isLoading" color="primary" class="z-10">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
      <div class="filter-panel">
        <c-select
          v-model="selectedTemplate"
          :label="$t('syndicate_plus.common.template')"
          :options="allTemplates"
          class="w-400px"
          clearable
          @update:model-value="onTemplateChange"
        />
        <c-select
          v-model="selectedMapping"
          :label="$t('syndicate_plus.common.cvl')"
          option-label="label"
          :options="templateMappings"
          clearable
          @update:model-value="onValueMappingChange"
        />
      </div>
      <c-no-data
        v-if="!allTemplates?.length && !isLoading"
        src="nothing-to-see"
        image-height="195px"
        :title="$t('syndicate_plus.mapping.value_mapping_tab.no_data.no_mappings_title')"
        :text="$t('syndicate_plus.mapping.value_mapping_tab.no_data.no_mappings_message')"
      />
      <div>
        <q-table
          v-if="allTemplates?.length"
          ref="tableRef"
          v-model:pagination="pagination"
          v-model:selected="selectedValueMappingRows"
          row-key="id"
          separator="cell"
          :columns="columns"
          :rows="displayedRows"
          dense
          :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
          flat
          :hide-header="false"
          class="hide-checkboxes value-mapping-table"
          hide-bottom
          @row-click="onRowClick"
        >
          <template #body-cell-columnValueBrand="tableProps">
            <q-td class="w-1/2 custom-cell">
              <c-select
                v-if="isEditing && tableProps.row.isTempValue"
                v-model="tableProps.row.columnValueBrand"
                :placeholder="$t('syndicate_plus.mapping.value_mapping_tab.select_syndication_value')"
                :label="$t('syndicate_plus.mapping.value_mapping_tab.select_syndication_value')"
                option-label="name"
                :options="availableBrandValues"
                hide-bottom-space
                @update:model-value="() => onRowValueChange(tableProps.row.id)"
              />
              <span v-else-if="!!tableProps.row.columnValueBrand?.name">{{
                tableProps.row.columnValueBrand.name
              }}</span>
            </q-td>
          </template>
          <template #body-cell-actions>
            <q-td class="w-31px custom-cell">
              <q-icon name="mdi-arrow-right" size="xs" class="grey-dark" />
            </q-td>
          </template>
          <template #body-cell-columnValueTradingPartner="tableProps">
            <q-td class="w-1/2 custom-cell">
              <c-select
                v-if="isEditing"
                v-model="tableProps.row.columnValueTradingPartner"
                :placeholder="
                  $t('syndicate_plus.mapping.value_mapping_tab.select_trading_partner_value', {
                    tradingPartner: tradingPartnerName,
                  })
                "
                :label="
                  $t('syndicate_plus.mapping.value_mapping_tab.select_trading_partner_value', {
                    tradingPartner: tradingPartnerName,
                  })
                "
                option-label="name"
                :options="allTradingPartnerValues"
                hide-bottom-space
                @update:model-value="() => onRowValueChange(tableProps.row.id)"
              />
              <span v-else-if="!!tableProps.row.columnValueTradingPartner?.name">{{
                tableProps.row.columnValueTradingPartner.name
              }}</span>
            </q-td>
          </template>
        </q-table>
      </div>
    </div>
  </c-section>
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, onUnmounted, toRef } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ValueMapView } from '@customTypes/valueMapping';
import { CNoData } from '@components';
import { useAppInsightsStore } from '@stores';
import { useValueMappingStore } from '@stores/ValueMapping';
import { PageName } from '@enums';
import { storeToRefs } from 'pinia';

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  tradingPartnerName: {
    type: String,
    default: '',
  },
  isEditingMode: {
    type: Boolean,
    default: false,
  },
});

const { t } = useI18n();
const route = useRoute();
const valueMappingStore = useValueMappingStore();

// Variables
const destinationId: number = parseInt(route.params.destinationId as string);
const pagination = {
  page: 1,
  rowsPerPage: 0,
};

// Refs
const isEditing = toRef(() => props.isEditingMode);
const {
  availableBrandValues,
  allTradingPartnerValues,
  displayedRows,
  templateMappings,
  selectedTemplate,
  selectedMapping,
  allTemplates,
  selectedValueMappingRows,
  isLoading,
} = storeToRefs(valueMappingStore);

// Computed
const columns = computed(() => [
  {
    name: 'columnValueBrand',
    field: 'columnValueBrand',
    label: t('syndicate_plus.mapping.value_mapping_tab.syndication_value'),
    align: 'left' as const,
    style: 'width: 50%',
  },
  {
    name: 'actions',
    field: 'actions',
    label: '',
    align: 'center' as const,
  },
  {
    name: 'columnValueTradingPartner',
    field: 'columnValueTradingPartner',
    label: t('syndicate_plus.mapping.value_mapping_tab.trading_partner_value', {
      tradingPartner: props.tradingPartnerName,
    }),
    align: 'left' as const,
    style: 'width: 50%',
  },
]);

// Composables
const { setScreenName } = useAppInsightsStore();

// Functions
const onTemplateChange = async () => {
  selectedMapping.value = templateMappings.value.length ? templateMappings.value[0] : undefined;
  await onValueMappingChange();
};

const onValueMappingChange = async () => {
  if (!selectedMapping.value) {
    return;
  }

  isLoading.value = true;
  await valueMappingStore.fetchAvailableValues(destinationId);
  valueMappingStore.setDefaultValues();
  await valueMappingStore.setValuesForSelectedTemplate(destinationId);
  isLoading.value = false;
};

const onRowValueChange = async (rowId: string) => {
  valueMappingStore.markSelectedValueMappingAsUpdated(rowId);
};

const onRowClick = (event, row: ValueMapView): void => {
  if (!event.target.classList.contains('custom-cell') && !event.target.classList.contains('mdi-arrow-right')) {
    return;
  }

  if (!isEditing.value) {
    return;
  }

  removeTextSelection();

  const isRowSelected = selectedValueMappingRows.value?.includes(row);
  if (event.ctrlKey || event.metaKey) {
    if (isRowSelected) {
      selectedValueMappingRows.value = selectedValueMappingRows.value.filter((x) => x != row);
    } else {
      selectedValueMappingRows.value.push(row);
    }

    return;
  }

  if (event.shiftKey && !selectedValueMappingRows.value?.length && !isRowSelected) {
    selectedValueMappingRows.value.push(row);
    return;
  }

  if (event.shiftKey && selectedValueMappingRows.value?.length && !isRowSelected) {
    const currentRowIndex = displayedRows.value?.indexOf(row);
    const lastSelectedRow = selectedValueMappingRows.value[selectedValueMappingRows.value.length - 1];
    const lastSelectedRowIndex = displayedRows.value.indexOf(lastSelectedRow);

    const startIndex = lastSelectedRowIndex < currentRowIndex ? lastSelectedRowIndex : currentRowIndex;
    const stopIndex = lastSelectedRowIndex < currentRowIndex ? currentRowIndex : lastSelectedRowIndex;

    for (let i = startIndex; i <= stopIndex; i++) {
      if (selectedValueMappingRows.value.indexOf(displayedRows.value[i]) < 0) {
        selectedValueMappingRows.value.push(displayedRows.value[i]);
      }
    }

    return;
  }

  selectedValueMappingRows.value = selectedValueMappingRows.value?.includes(row)
    ? []
    : (selectedValueMappingRows.value = [row]);
};

const removeTextSelection = () => document?.getSelection()?.removeAllRanges();

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.VALUE_MAPPINGS);
  await valueMappingStore.fetchValueMappings(destinationId);
});

onUnmounted(() => {
  valueMappingStore.clear();
});
</script>

<style lang="scss" scoped>
$value-mapping-tab-panel-offset: 309px;

.c-value-mapping-tab-panel {
  min-height: calc(100vh - $value-mapping-tab-panel-offset);

  .spinner {
    margin: auto;
    width: min-content;
  }

  .filter-panel {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .value-mapping-table {
    color: var(--color-grey-dark);
    width: 45vw;
    max-height: calc(100vh - $value-mapping-tab-panel-offset);
    border-bottom: 1px solid var(--color-grey-light);

    .custom-cell {
      height: auto;

      :deep(.q-field__bottom) {
        display: none;
      }
    }
  }
}
</style>
