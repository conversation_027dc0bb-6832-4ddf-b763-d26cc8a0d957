<template>
  <c-layout-with-back-button :title="title">
    <template #left-sidebar>
      <div id="left-sidebar"></div>
    </template>
    <template #right-sidebar>
      <c-tile-btn
        icon="mdi-cog-outline"
        :icon-size="20"
        :tooltip-left="$t('core.trading_partners.view_mappings')"
        class="pendo__go_to_settings_page"
        @click="logEventAndClick(ActionName.GO_TO_SETTINGS_PAGE, goToSettingsPage)"
      />
      <div id="right-sidebar"></div>
    </template>
    <section class="p-0">
      <div class="col">
        <q-tabs
          v-model="tab"
          class="bg-white text-grey-7"
          active-color="primary"
          active-class="active-tab"
          indicator-color="transparent"
          align="justify"
          no-caps
        >
          <q-tab
            :name="TradingPartnerTabNames.COLLECTIONS"
            :label="$t('core.trading_partners.collections.tab_name')"
            @click="tabClicked(TradingPartnerTabNames.COLLECTIONS)"
          />
          <q-tab
            :name="TradingPartnerTabNames.PRODUCTS"
            :label="$t('core.trading_partners.products.tab_name')"
            @click="tabClicked(TradingPartnerTabNames.PRODUCTS)"
          />
          <q-tab
            :name="TradingPartnerTabNames.HISTORY"
            :label="$t('core.trading_partners.history.tab_name')"
            @click="tabClicked(TradingPartnerTabNames.HISTORY)"
          />
        </q-tabs>
        <collections-tab-panel
          v-show="tab === TradingPartnerTabNames.COLLECTIONS"
          :active-tab="tab === TradingPartnerTabNames.COLLECTIONS"
        />
        <c-products-tab-panel
          v-show="tab === TradingPartnerTabNames.PRODUCTS"
          :active-tab="tab === TradingPartnerTabNames.PRODUCTS"
        />
        <c-history-tab-panel v-if="tab === TradingPartnerTabNames.HISTORY" :trading-partner-id="tradingPartnerId" />
      </div>
    </section>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, computed, watch } from 'vue';
import { useRouter } from '@composables/useRouter';
import { TradingPartnerTabNames } from '@core/enums';
import { useAppInsightsStore } from '@stores';
import { CollectionsTabPanel } from '@core/components/Collections';
import { CHistoryTabPanel } from '@core/components/History';
import { CProductsTabPanel } from '@core/components/Products';
import { ActionName, PageName } from '@enums';
import { useCurrentCoreTradingPartnerStore } from '@core/stores';
import { getInitialActiveTab, setLastActiveTab } from '@core/services/TradingPartnerPage/InitActiveTabService';

const { route, goToPage } = useRouter();
const currentCoreTradingPartnerStore = useCurrentCoreTradingPartnerStore();

// Refs
const tab = ref(TradingPartnerTabNames.COLLECTIONS);
const tradingPartnerId = ref<string>(route.params?.tradingPartnerId as string);

// Computed
const decodedTradingPartnerId = computed(() => decodeURIComponent(tradingPartnerId.value));
const title = computed(() => {
  return tradingPartnerId.value && decodedTradingPartnerId.value;
});

// Composables
const { setScreenName, logEventAndClick, startPageLoadAndRender, endPageLoadAndRender } = useAppInsightsStore();

// Functions
const tabClicked = (tabName: TradingPartnerTabNames): void => {
  setScreenNameForTab(tabName);
  // Update the active tab in UI
  tab.value = tabName;
  // Save the last active tab in session storage
  setLastActiveTab(tradingPartnerId.value, tabName);
};

const setScreenNameForTab = (tabName: TradingPartnerTabNames): void => {
  if (tabName === TradingPartnerTabNames.COLLECTIONS) {
    setScreenName(PageName.COLLECTIONS);
  }

  if (tabName === TradingPartnerTabNames.PRODUCTS) {
    setScreenName(PageName.PRODUCTS);
  }

  if (tabName === TradingPartnerTabNames.HISTORY) {
    setScreenName(PageName.HISTORY);
  }
};

const goToSettingsPage = () => goToPage('trading-partner-settings-page');

// Watch for trading partner ID changes to update tab when navigating
watch(
  () => tradingPartnerId.value,
  (newPartnerId) => {
    if (newPartnerId) {
      const savedTab = getInitialActiveTab(newPartnerId);
      if (savedTab) {
        tab.value = savedTab;
        setScreenNameForTab(savedTab);
      }
    }
  },
  { immediate: true }
);

// Lifecycle methods
onBeforeMount(async () => {
  startPageLoadAndRender();
  setScreenName();
  await currentCoreTradingPartnerStore.init(decodedTradingPartnerId.value);

  // Initialize tab from session storage
  const savedTab = getInitialActiveTab(tradingPartnerId.value);
  if (savedTab) {
    tab.value = savedTab;
    setScreenNameForTab(savedTab);
  }

  endPageLoadAndRender('CoreCollectionsLoadAndRender');
});
</script>

<style lang="scss" scoped>
.q-panel {
  overflow: hidden;

  .q-tab-panel {
    padding-top: 0px;
    padding-left: 0px;
    padding-right: 0px;
  }
}
</style>
