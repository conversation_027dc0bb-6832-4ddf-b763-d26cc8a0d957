import { useRoute } from 'vue-router';
import { notify } from '@inriver/inri';
import { AmazonCallbackStatus } from '@enums/Amazon';
import { onMounted } from 'vue';

export default function useAmazonCallbackStatus(useI18n: Function) {
  // Variables
  const route = useRoute();
  const status = route.query.status as string | undefined;
  const { t } = useI18n && useI18n();
  const timeout = 10000;

  // Functions
  const showAmazonCallbackStatusNotification = () => {
    const responseStatus = status ? status.toLowerCase() : '';
    if (responseStatus === AmazonCallbackStatus.SUCCESS) {
      notify.success(t('syndicate_plus.mapping.amazon.login_callback.success'), { timeout });
    }

    if (responseStatus === AmazonCallbackStatus.ERROR) {
      notify.error(t('syndicate_plus.mapping.amazon.login_callback.error'), { timeout });
    }
  };

  // Lifecycle methods
  onMounted(() => showAmazonCallbackStatusNotification());

  return { showAmazonCallbackStatusNotification };
}
