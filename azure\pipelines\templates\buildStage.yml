parameters:
  - name: 'disableApplicationBuild'
    type: 'boolean'

  - name: 'disableUnitTests'
    default: 'false'
    type: 'boolean'

  - name: stack
    default: euwDev1a
    type: string

stages:
  - stage: Build_${{ parameters.stack }}
    dependsOn:
    variables:
      - template: '../variables/${{ parameters.stack }}.yml'
      - group: inri-access-token
    jobs:
      - job: Build
        condition: eq('${{ parameters.disableApplicationBuild }}', 'false')
        displayName: Build and package application

        steps:
          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
              path: $(Pipeline.Workspace)/.pnpm-store
            displayName: Cache pnpm

          - script: |
              corepack enable
              corepack prepare pnpm@9.9.0 --activate
            displayName: 'Setup pnpm'

          - script: pnpm config set store-dir $(Pipeline.Workspace)/.pnpm-store
            displayName: 'setup pnpm config'

          - task: NodeTool@0
            inputs:
              versionSpec: 20.17.0
            displayName: Install Node.js

          - script: |
              echo "VITE_INSTRUMENTATION_KEY=${{ variables.instrumentationKey }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_AUTH0_DOMAIN=${{ variables.auth0Domain }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_AUTH0_CLIENTID=${{ variables.auth0ClientId }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_API_URL=${{ variables.apiUrl }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_ALLOW_MISSING_ORG_MAPPING=${{ variables.allowMissingOrgMapping }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo 'VITE_FILE_SERVER_URL=${{ variables.fileServerUrl }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_AUTH0_CONNECTION=${{ variables.auth0Connection }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_STATIC_WEB_APP_URL='https://${{ variables.customDomain }}'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_IS_DATA_EDITING_ENABLED='false'" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_INFRARED_INSTRUMENTATION_KEY=${{ variables.infraredInstrumentationKey }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_INFRARED_AUTH0_DOMAIN=${{ variables.infraredAuth0Domain }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_INFRARED_AUTH0_CLIENTID=${{ variables.infraredAuth0ClientId }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_INFRARED_AUTH0_CONNECTION=${{ variables.infraredAuth0Connection }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_INFRARED_API_URL=${{ variables.infraredApiUrl }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_INFRARED_ENVS=${{ variables.infraredEnvs }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo 'VITE_INFRARED_FILE_SERVER_URL=${{ variables.infraredFileServerUrl }}' >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}
              echo "VITE_PRICE_IMPORT_ENABLED_ENVS=${{ variables.priceImportEnabledEnvs }}" >> $(Build.SourcesDirectory)/packages/syndicate-plus-frontend/.env.${{ parameters.stack }}

            displayName: 'Set environment variables for stack'

          - bash: |
              echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" > .npmrc
            env:
              NPM_TOKEN: $(NPM_TOKEN)
            displayName: Init .npmrc file

          - script: |
              npm install -g pnpm@9.9.0
              pnpm install --frozen-lockfile
            displayName: install packages

          - script: |
              set -e
              pnpm run frontend:lint
            displayName: Check lint

          - script: |
              pnpm run frontend:build --mode ${{ parameters.stack }}
            displayName: Build application

          - script: |
              pnpm coverage
            displayName: run unit tests with coverage
            condition: eq('${{ parameters.disableUnitTests }}', 'false')

          - task: CopyFiles@2
            displayName: Copy dist files
            inputs:
              sourceFolder: $(Build.SourcesDirectory)/packages/syndicate-plus-frontend
              contents: dist/**/*.*
              targetFolder: $(Build.ArtifactStagingDirectory)

          - task: PublishCodeCoverageResults@1
            displayName: 'Publish Coverage Results'
            inputs:
              codeCoverageTool: 'Cobertura'
              pathToSources: '$(Build.SourcesDirectory)/packages/syndicate-plus-frontend/'
              summaryFileLocation: '$(Build.SourcesDirectory)/packages/syndicate-plus-frontend/coverage/cobertura-coverage.xml'
              reportDirectory: '$(Build.SourcesDirectory)/coverage/lcov-report'
            condition: eq('${{ parameters.disableUnitTests }}', 'false')

          - task: PublishBuildArtifacts@1
            inputs:
              pathtoPublish: $(Build.ArtifactStagingDirectory)
              artifactName: SyndicatePlusFrontendArtifact-${{ parameters.stack }}
