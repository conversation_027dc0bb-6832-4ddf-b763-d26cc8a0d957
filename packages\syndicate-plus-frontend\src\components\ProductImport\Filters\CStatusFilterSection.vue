<template>
  <c-separated-expansion-item :is-expanded="isExpanded" :label="$t('syndicate_plus.product_import.filter.status')">
    <div class="flex flex-col">
      <div class="flex flex-col filter-buttons">
        <a class="clear-btn" aria-label="clear filter" @click="clearFilter">
          {{ $t('syndicate_plus.common.filter.reset_filters') }}
        </a>
      </div>
      <q-radio
        v-for="status in statuses"
        v-bind="$inri.radio"
        :key="status"
        v-model="selectedStatus"
        :data-testid="status"
        :val="status"
        :label="status.toLocaleLowerCase()"
        @click="applyFilterSelections"
      />
    </div>
  </c-separated-expansion-item>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CSeparatedExpansionItem } from '@components/Shared';
import { ProductImportStatus } from '@enums';

defineProps({
  isExpanded: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['apply-filter']);

// Variables
const statuses = [ProductImportStatus.COMPLETE, ProductImportStatus.IN_QUEUE];

// Refs
const selectedStatus = ref<string>();

// Functions
const clearFilter = (): void => {
  selectedStatus.value = undefined;
  applyFilterSelections();
};

const applyFilterSelections = (): void => {
  emit('apply-filter', selectedStatus.value);
};
</script>

<style scoped lang="scss">
.filter-buttons {
  flex-direction: row;
  min-height: 21px;
  align-items: center;

  .clear-btn {
    cursor: pointer;
    margin-left: auto;
  }
}

.separator,
.search {
  margin: 10px 0;
}

.spinner {
  margin: auto;
}
</style>
