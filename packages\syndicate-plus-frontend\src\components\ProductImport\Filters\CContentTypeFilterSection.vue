<template>
  <c-separated-expansion-item
    :is-expanded="isExpanded"
    :label="$t('syndicate_plus.product_import.filter.content_type')"
  >
    <div class="flex flex-col">
      <div class="flex flex-col filter-buttons">
        <a class="clear-btn" aria-label="clear filter" @click="clearFilter">
          {{ $t('syndicate_plus.common.filter.reset_filters') }}
        </a>
      </div>
      <q-radio
        v-for="contentType in contentTypes"
        v-bind="$inri.radio"
        :key="contentType"
        v-model="selectedContentType"
        :data-testid="contentType"
        :val="contentType"
        :label="contentType.toLocaleLowerCase()"
        @click="applyFilterSelections"
      />
    </div>
  </c-separated-expansion-item>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { CSeparatedExpansionItem } from '@components/Shared';
import { ProductImportContentType } from '@enums';

defineProps({
  isExpanded: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['apply-filter']);

// Variables
const contentTypes = [
  ProductImportContentType.PRODUCT,
  ProductImportContentType.MEDIA,
  ProductImportContentType.ENHANCED_CONTENT,
  ProductImportContentType.DATA_FLOW,
];

// Refs
const selectedContentType = ref<string>();

// Functions
const clearFilter = (): void => {
  selectedContentType.value = undefined;
  applyFilterSelections();
};

const applyFilterSelections = (): void => {
  emit('apply-filter', selectedContentType.value);
};
</script>

<style scoped lang="scss">
.filter-buttons {
  flex-direction: row;
  min-height: 21px;
  align-items: center;

  .clear-btn {
    cursor: pointer;
    margin-left: auto;
  }
}

.separator,
.search {
  margin: 10px 0;
}

.spinner {
  margin: auto;
}
</style>
