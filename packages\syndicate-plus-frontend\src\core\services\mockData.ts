import { JobState } from '@core/enums';
import { DataSubmissionEntitiesResponse, DataSubmissionResponse } from '@core/interfaces';

export const mockDataSubmissions = (page: number, pageSize: number): DataSubmissionResponse => {
  const totalSubmissions = 500;
  const start = (page - 1) * pageSize;
  const end = Math.min(start + pageSize, totalSubmissions);

  const dataSubmissions = Array.from({ length: end - start }, (_, index) => ({
    id: start + index + 1,
    name: `Submission ${start + index + 1}`,
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
    jobId: Math.floor(Math.random() * 1000),
    environmentGid: `env-${Math.floor(Math.random() * 100)}`,
    tradingPartnerId: `partner-${Math.floor(Math.random() * 50)}`,
    state: JobState.SUCCESS || JobState.PENDING || JobState.FAILED,
  }));

  return {
    dataSubmissions,
    totalCount: totalSubmissions,
    pageSize,
    page,
  };
};

export const mockDataSubmissionEntries = (
  dataSubmissionId: number,
  page: number,
  pageSize: number,
  state?: JobState
): DataSubmissionEntitiesResponse => {
  const totalEntries = 200;
  const start = (page - 1) * pageSize;
  const end = Math.min(start + pageSize, totalEntries);

  const entities = Array.from({ length: end - start }, (_, index) => ({
    id: start + index + 1,
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString(),
    dataSubmissionId: dataSubmissionId,
    entityId: start + index + 1,
    correlationId: dataSubmissionId.toString(),
    state: state ?? JobState.SUCCESS,
    apiFailureResponse: `Entry ${start + index + 1}`,
  }));

  return {
    entities,
    totalCount: totalEntries,
    pageSize,
    page,
  };
};
