<template>
  <div class="mx-auto min-w-1000px w-1000px single-images-highlight-preview" data-testid="single-images-and-highlight">
    <div class="flex justify-center flex-col">
      <div class="flex flex-row flex-nowrap">
        <div class="left-column m-10px">
          <c-caption-angle-image-block-preview
            :image-data="module?.data.image"
            image-data-class="image mb-10px ml-5px mr-20px"
          />
        </div>
        <div class="middle-column m-10px pt-10px">
          <div class="headline mb-10px ml-10px">{{ module.data.headline1 }}</div>
          <div class="subheadline">{{ module.data.subheadline1 }}</div>
          <c-html-preview :html="module.data.body1" />
          <div class="subheadline">{{ module.data.subheadline2 }}</div>
          <c-html-preview :html="module.data.body2" />
          <div class="subheadline">{{ module.data.subheadline3 }}</div>
          <c-html-preview :html="module.data.body3" />
        </div>
        <div class="right-column m-10px pl-10px">
          <div class="headline">{{ module.data.headline2 }}</div>
          <c-bullet-list-preview v-if="module.data.bullets.length" class="bullets" :bullet-list="module.data.bullets" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, SingleImageHighlightsData } from '@customTypes/Aplus';
import { CBulletListPreview, CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<SingleImageHighlightsData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.single-images-highlight-preview {
  :deep() {
    .image {
      object-fit: unset;
      width: 300px;
      height: 300px;
      min-width: 300px;
      min-height: 300px;
    }
  }

  .bullets {
    background-color: var(--color-grey-10);
    border: 1px solid var(--color-grey-light);
    margin-top: 0px;
  }

  .left-column {
    width: 300px;
  }

  .middle-column {
    width: 380px;
  }

  .right-column {
    width: 230px;
  }

  .sidebar-body {
    font-size: 13px;
  }

  .bullet-list {
    background-color: var(--surface-color);
    border: none;
    margin-top: 0px;

    :deep(.bullet-text) {
      font-weight: 700;
    }
  }

  .main-headline {
    font-size: 18px;
    font-weight: bold;
  }

  .headline {
    font-size: 14px;
    font-weight: bold;
  }

  .subheadline {
    font-size: 13px;
    color: var(--color-grey-dark);
    margin-top: 10px;
    margin-left: 10px;
  }
}
</style>
