<template>
  <div class="mx-auto p-20px min-w-800px w-800px company-logo-preview">
    <div class="company-logo-content">
      <img
        v-if="module?.data.logoUrl"
        :src="module?.data.logoUrl"
        :data-testid="`${index}-company-logo-image`"
        alt="Company Logo"
      />
      <div v-else class="gray-rectangle h-180px w-600px"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { ContentModule, CompanyLogoData } from '@customTypes/Aplus';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

// Composables
const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<CompanyLogoData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>
<style lang="scss" scoped>
.company-logo-preview {
  .company-logo-content {
    height: 200px;
    padding: 10px 20px;
    display: flex;
    justify-content: center;

    img {
      display: block;
      height: 180px;
      width: 600px;
      min-width: 600px;
      object-fit: cover;
    }
  }
}
</style>
