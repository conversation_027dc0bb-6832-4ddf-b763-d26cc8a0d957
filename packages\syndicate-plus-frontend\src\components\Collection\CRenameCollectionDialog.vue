<template>
  <c-dialog
    class="c-dialog"
    :model-value="showDialog"
    :is-loading="isLoading"
    hide-cancel-button
    hide-confirm-button
    @cancel="cancel"
  >
    <div class="row q-gutter-lg w-1/2">
      <div class="col section">
        <h3>
          {{ $t('syndicate_plus.collections.rename_collection.title', { collectionName: props.collectionName }) }}
        </h3>
        <q-input
          v-model="newCollectionName"
          v-bind="$inri.input"
          :label="$t('syndicate_plus.collections.create_collection.collection_name')"
          autofocus
          @keyup.enter="logEventAndClick(ActionName.CREATE_COLLECTION, onRename)"
        />

        <div class="action-buttons">
          <c-btn
            :label="$t('syndicate_plus.common.save')"
            :disabled="renameCollectionIsDisabled"
            @click="logEventAndClick(ActionName.RENAME_COLLECTION, onRename)"
          >
          </c-btn>
        </div>
      </div>
    </div>
  </c-dialog>
</template>

<script setup lang="ts">
import { useDialog, notify } from '@inriver/inri';
import { useI18n } from 'vue-i18n';
import { useRenameCollection } from '@composables/Collection';
import { useEmitter } from '@composables';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';
import { CollectionService } from '@services';

const props = defineProps({
  collectionName: {
    type: String,
    required: true,
  },
  collectionId: {
    type: Number,
    required: true,
  },
  tradingPartnerName: {
    type: String,
    required: true,
  },
});

// Composables
const { t } = useI18n();
const { logEventAndClick } = useAppInsightsStore();
const { showDialog, isLoading, cancel, confirmSuccess } = useDialog();
const { newCollectionName, renameCollectionIsDisabled, renameCollection } = useRenameCollection(
  isLoading,
  props.collectionName,
  props.collectionId
);
const emitter = useEmitter();

// Functions
const onRename = async () => {
  const nameExists = await CollectionService.checkIfCollectionNameExists(
    props.tradingPartnerName,
    newCollectionName.value
  );

  if (nameExists) {
    notify.error(t('syndicate_plus.collections.notifications.name_exists'), {
      position: 'bottom-right',
    });
    return;
  }

  await renameCollection(props.tradingPartnerName);
  confirmSuccess(t('syndicate_plus.collections.rename_collection.renamed_successfully'));
  emitter.emit('collection-renamed');
};
</script>

<style scoped lang="scss">
.section {
  margin-top: 3px;

  h3 {
    padding-bottom: 20px;
  }

  .action-buttons {
    padding-top: 10px;
  }
}
</style>
