<template>
  <c-dialog
    class="c-dialog"
    :title="$t('syndicate_plus.common.save_to_outbox')"
    :model-value="showDialog"
    :confirm-button-text="$t('syndicate_plus.common.save')"
    :is-loading="isLoading"
    :disable-confirm="confirmIsDisabled"
    @confirm="confirm"
    @cancel="cancel"
  >
    <c-template-select
      :rules="[$validate.required()]"
      @template-selected-event="selectedTemplateChanged"
      @single-template-selected-event="singleTemplateSelectedEvent"
    />
  </c-dialog>
</template>

<script setup lang="ts">
import { computed, ref, PropType } from 'vue';
import { notify, useDialog } from '@inriver/inri';
import { Field, CollectionDetails } from '@customTypes';
import { ExportService } from '@services';
import { useRouter } from '@composables/useRouter';
import { SyndicationPageTabNames } from '@enums';
import { ProductFilterDto } from '@dtos';
import CTemplateSelect from '@components/CTemplateSelect.vue';

const props = defineProps({
  tab: {
    type: Object as PropType<SyndicationPageTabNames>,
    default: SyndicationPageTabNames.Products,
  },
  selectedCollection: {
    type: (Object as PropType<CollectionDetails>) || null,
    default: null,
  },
  selectAllProductsFilterApplied: {
    type: Boolean,
    default: false,
  },
  searchValue: {
    type: String,
    default: '',
  },
  filter: {
    type: Object as PropType<ProductFilterDto>,
    required: true,
  },
  selectedProductIds: {
    type: Array<string>,
    default: [],
  },
});

// Refs
const selectedTemplate = ref<Field | null>(null);

// Composables
const { route, goToPage } = useRouter();
const { showDialog, isLoading, cancel, confirmSuccess } = useDialog({
  selectedTemplate,
});

// Variables
const destinationId = parseInt(route.params.destinationId as string);
const subCatalogId = parseInt(route.params.subCatalogId as string);

// Computed fields
const confirmIsDisabled = computed(() => {
  return !selectedTemplate.value;
});

// Functions
async function confirm() {
  isLoading.value = true;
  const template = selectedTemplate.value;
  if (!template) {
    return;
  }

  const { tab, selectedCollection } = props;
  if (tab === SyndicationPageTabNames.Collection) {
    const collectionFilter = {
      collections: [selectedCollection.id.toString()],
      groupBy: props.filter.groupBy,
    } as ProductFilterDto;
    await ExportService.exportFilterSelection(
      subCatalogId,
      destinationId,
      template.id,
      collectionFilter,
      props.searchValue
    );
  } else if (props.selectAllProductsFilterApplied) {
    await ExportService.exportFilterSelection(
      subCatalogId,
      destinationId,
      template.id,
      props.filter,
      props.searchValue
    );
  } else {
    await ExportService.exportSelection(
      subCatalogId,
      destinationId,
      template.id,
      props.selectedProductIds,
      props.filter.groupBy.id
    );
  }

  isLoading.value = false;
  confirmSuccess(null);

  notify.success(`save to outbox started!`, {
    actions: [
      {
        label: 'go to outbox',
        color: 'black',
        handler: () => {
          navigateToProductOutbox();
        },
      },
    ],
  });
}

function navigateToProductOutbox() {
  goToPage('syndication-page', {
    subCatalogId: subCatalogId,
    destinationId: destinationId,
    tabName: 'outbox_tab',
  });
}

function selectedTemplateChanged(template: Field) {
  selectedTemplate.value = template;
}

async function singleTemplateSelectedEvent(template: Field) {
  selectedTemplateChanged(template);
  await confirm();
}
</script>
