import type { Preview } from '@storybook/vue3';
import 'virtual:windi.css';
import '../src/scss/main.scss'
import { createPinia } from 'pinia';
import i18n from '../src/i18n';
import { initInriFramework } from '@inriver/inri';
import { setup } from '@storybook/vue3';

setup((app) => {
  app.use(createPinia());
  initInriFramework(app);
  app.use(i18n);
});

const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
