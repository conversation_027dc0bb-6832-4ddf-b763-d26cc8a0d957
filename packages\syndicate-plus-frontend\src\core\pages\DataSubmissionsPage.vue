<template>
  <c-layout-with-back-button :title="title">
    <template #left-sidebar>
      <div id="left-sidebar"></div>
    </template>
    <template #right-sidebar>
      <div id="right-sidebar">
        <c-tile-btn
          v-if="isDetailsButtonVisible"
          icon="mdi-text-box-outline"
          data-testid="entity-details-button"
          :tooltip-left="$t('core.common.view_details')"
          :icon-size="20"
          @click="showDialog"
        />
      </div>
    </template>
    <section class="c-inri-section">
      <template v-if="dataSubmissions.length > 0">
        <data-submissions-table
          :data-submissions="dataSubmissions"
          :is-loading="isLoadingSubmissions"
          :job-id="jobId"
          @update:selected="handleSubmissionSelected"
          @load-more="loadMoreSubmissions"
        />
      </template>
      <c-no-data
        v-else-if="!isLoadingSubmissions"
        src="nothing-to-see"
        image-height="195px"
        text="no data submissions found for the selected job"
      />
      <data-submission-entities-table
        v-if="dataSubmissions.length > 0 && selectedSubmission"
        :entities="dataSubmissionEntities"
        :is-loading="isLoadingEntities"
        :data-submission-id="selectedSubmission.id"
        @update:selected="handleEntitySelected"
        @load-more="loadMoreEntities"
        @show-details="showApiFailureDialog = true"
      />
      <c-api-failure-response-dialog
        v-if="showApiFailureDialog"
        v-model:show="showApiFailureDialog"
        :entity="selectedEntity"
      />
    </section>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, computed, watch } from 'vue';
import { useRouter } from '@composables/useRouter';
import { useAppInsightsStore } from '@stores';
import { PageName } from '@enums';
import { DataSubmission, DataSubmissionEntity } from '@core/interfaces';
import { useDataSubmissionStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { CNoData } from '@components';
import {
  CApiFailureResponseDialog,
  DataSubmissionsTable,
  DataSubmissionEntitiesTable,
} from '@core/components/DataSubmissions';

// Composables
const dataSubmissionStore = useDataSubmissionStore();
const { setScreenName } = useAppInsightsStore();
const { route } = useRouter();

// Variables
const jobIdParam = route.params?.jobId;
const jobId = typeof jobIdParam === 'string' ? Number(jobIdParam) : undefined;
const tradingPartnerId = route.params?.tradingPartnerId as string;

// Refs
const selectedSubmission = ref<DataSubmission | null>(null);
const selectedEntity = ref<DataSubmissionEntity | null>(null);
const showApiFailureDialog = ref(false);
const { dataSubmissions, isLoadingSubmissions, isLoadingEntities, dataSubmissionEntities } =
  storeToRefs(dataSubmissionStore);

// Computed
const decodedTradingPartnerId = computed(() => decodeURIComponent(tradingPartnerId));
const title = computed(() => {
  return jobId
    ? `data submissions - ${decodedTradingPartnerId.value} - job id: ${jobId}`
    : `data submissions - ${decodedTradingPartnerId.value}`;
});
const isDetailsButtonVisible = computed(() => selectedEntity.value?.apiFailureResponse);

// Functions
const handleSubmissionSelected = async (submission: DataSubmission | null): Promise<void> => {
  selectedSubmission.value = submission;
  if (submission) {
    const isDataSubmissionIdChanged = true;
    await dataSubmissionStore.loadDataSubmissionEntities(submission.id, isDataSubmissionIdChanged);
  }
};

const handleEntitySelected = (entity: DataSubmissionEntity | null) => {
  selectedEntity.value = entity;
};

const loadMoreSubmissions = async () => {
  if (!isLoadingSubmissions.value && !dataSubmissionStore.isLastPage) {
    await dataSubmissionStore.loadDataSubmissions(jobId);
  }
};

const loadMoreEntities = async () => {
  if (!isLoadingEntities.value && !dataSubmissionStore.isLastEntitiesPage && selectedSubmission.value) {
    await dataSubmissionStore.loadDataSubmissionEntities(selectedSubmission.value.id);
  }
};

const refreshData = async () => {
  dataSubmissionStore.resetState();
  await dataSubmissionStore.loadDataSubmissions(jobId);
};

const showDialog = () => {
  if (selectedEntity.value?.apiFailureResponse) {
    showApiFailureDialog.value = true;
  }
};

// Lifecycle methods
watch(
  () => route.params.jobId,
  async (newJobId) => {
    if (newJobId) {
      await refreshData();
    }
  }
);

onBeforeMount(async () => {
  setScreenName(PageName.DATA_SUBMISSIONS_PAGE);
  await refreshData();
});
</script>

<style lang="scss" scoped></style>
