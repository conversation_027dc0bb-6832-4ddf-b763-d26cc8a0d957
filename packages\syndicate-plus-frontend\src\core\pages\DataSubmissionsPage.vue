<template>
  <c-layout-with-back-button :title="title">
    <template #left-sidebar>
      <div id="left-sidebar"></div>
    </template>
    <template #right-sidebar>
      <div id="right-sidebar"></div>
    </template>
    <section class="c-inri-section">
      <q-table
        v-model:selected="selectedRows"
        class="submissions-table sticky-table-header"
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        dense
        hide-bottom
        separator="cell"
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        :rows="dataSubmissions"
        row-key="id"
        :columns="dataSubmissionsColumns"
        :loading="submissionsTableLoading"
        @row-click="onRowClick"
      >
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
        <template #body-cell-createdDate="props">
          <q-td>{{ convertDateToLocalFormat(props.row.createdDate) }}</q-td>
        </template>
        <template #body-cell-updatedDate="props">
          <q-td>{{ convertDateToLocalFormat(props.row.updatedDate) }}</q-td>
        </template>
      </q-table>
      <q-table
        v-model:selected="selectedRows"
        class="submission-entries-table sticky-table-header"
        :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
        flat
        dense
        hide-bottom
        separator="cell"
        :pagination="{
          page: 1,
          rowsPerPage: 0,
        }"
        :rows="dataSubmissionEntities"
        row-key="EntityId"
        :columns="submissionEntriesColumns"
        :loading="isLoadingEntities"
        @row-click="onEntityRowClick"
      >
        <template #loading>
          <q-inner-loading showing color="primary" class="inner-loading">
            <c-spinner color="primary" size="40" />
          </q-inner-loading>
        </template>
        <template #body-cell-createdDate="props">
          <q-td>{{ convertDateToLocalFormat(props.row.createdDate) }}</q-td>
        </template>
        <template #body-cell-updatedDate="props">
          <q-td>{{ convertDateToLocalFormat(props.row.updatedDate) }}</q-td>
        </template>
        <template #body-cell-failureMessage="props">
          <q-td>{{ props.row.apiFailureResponse ? YesNo.YES : YesNo.NO }}</q-td>
        </template>
      </q-table>
    </section>
  </c-layout-with-back-button>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, computed } from 'vue';
import { useRouter } from '@composables/useRouter';
import { useAppInsightsStore } from '@stores';
import { PageName } from '@enums';
import { DataSubmission, DataSubmissionEntity } from '@core/interfaces';
import { dataSubmissionsColumns, submissionEntriesColumns } from '@core/const';
import { convertDateToLocalFormat } from '@services/helper/dateHelpers';
import { useDataSubmissionStore } from '@core/stores';
import { storeToRefs } from 'pinia';
import { YesNo } from '@core/enums';

const dataSubmissionStore = useDataSubmissionStore();

// Refs
const selectedRows = ref<DataSubmission[]>([]);
const { dataSubmissions, isLoadingSubmissions, isLoadingEntities, dataSubmissionEntities } =
  storeToRefs(dataSubmissionStore);

// Composables
const { setScreenName } = useAppInsightsStore();
const { route } = useRouter();

// Variables
const jobIdParam = route.params?.jobId;
const jobId = typeof jobIdParam === 'string' ? Number(jobIdParam) : undefined;
const tradingPartnerId = route.params?.tradingPartnerId as string;

// Computed
const decodedTradingPartnerId = computed(() => decodeURIComponent(tradingPartnerId));
const title = computed(() => {
  return jobId
    ? `data submissions - ${decodedTradingPartnerId.value} - job id: ${jobId}`
    : `data submissions - ${decodedTradingPartnerId.value}`;
});
const submissionsTableLoading = computed(() => {
  return isLoadingSubmissions.value || isLoadingEntities.value;
});

// Functions
const onRowClick = async (_, row: DataSubmission): Promise<void> => {
  const newRowValue = selectedRows.value?.includes(row) ? null : row;
  selectedRows.value = newRowValue ? [newRowValue] : [];
  if (!newRowValue) {
    return;
  }

  await dataSubmissionStore.loadDataSubmissionEntities(newRowValue?.id);
};

const onEntityRowClick = (row: DataSubmissionEntity) => {
  console.log('entity row clicked: ', row);
};

// Lifecycle methods
onBeforeMount(async () => {
  setScreenName(PageName.DATA_SUBMISSIONS_PAGE);
  await dataSubmissionStore.loadDataSubmissions(jobId);
  if (dataSubmissions.value.length) {
    selectedRows.value = [dataSubmissions.value[0]];
    dataSubmissionStore.loadDataSubmissionEntities(selectedRows.value[0].id);
  }
});
</script>

<style lang="scss" scoped>
.submissions-table {
  max-height: 350px;
  margin-bottom: 20px;
  min-height: 120px;
}

.submission-entries-table {
  max-height: 350px;
  min-height: 120px;
}
</style>
