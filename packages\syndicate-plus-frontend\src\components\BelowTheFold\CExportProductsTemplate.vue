<template>
  <h3>{{ pageTitle }}</h3>
  <div class="flex export-container">
    <div v-for="product in selectedProducts" :key="product.id">
      <c-product-card :product="product" :group-by="ProductGrouping.CUSTOM" :is-selected="false" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import dayjs from 'dayjs';
import { Product } from '@customTypes';
import { CProductCard } from '@components';
import { ProductGrouping } from '@enums';

const props = defineProps({
  exportName: {
    type: String,
    required: true,
  },
  selectedProducts: {
    type: Array<Product>,
    default: [],
  },
  groupBy: {
    type: String,
    default: ProductGrouping.CUSTOM,
  },
});

// Computed
const pageTitle = computed(() => `Below the Fold - ${props.exportName} - ${dayjs().format('YYYY/MM/DD')}`);
</script>

<style scoped>
.export-container {
  gap: 24px;
}
</style>
