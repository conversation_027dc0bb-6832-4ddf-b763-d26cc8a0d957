parameters:
  - name: serviceConnection
    displayName: Application Azure Service Connection
    type: string

  - name: appName
    displayName: Application name
    type: string

  - name: environment
    displayName: Environment
    type: string

  - name: "disableApplicationDeploy"
    type: "boolean"
    default: false

  - name: "stack"
    type: "string"

jobs:
  - deployment: DeployApplication
    displayName: Deploy syndicate-plus-frontend
    environment: ${{ parameters.environment }}
    condition: and(not(failed()), not(canceled()), eq('${{ parameters.disableApplicationDeploy }}', 'false'))
    workspace:
      clean: all
    pool:
      vmImage: ubuntu-latest
    strategy:
      runOnce:
        deploy:
          steps:
            - download: current
              artifact: SyndicatePlusFrontendArtifact-${{ parameters.stack }}
            - template: deployApp.yml
              parameters:
                serviceConnection: ${{ parameters.serviceConnection }}
                appName: ${{ parameters.appName }}
                stack: ${{ parameters.stack }}
