<template>
  <c-tile-btn
    :icon="icon"
    :icon-size="iconSize"
    :tooltip-left="tooltip"
    @click="logEventAndClick(ActionName.REQUEST_TEMPLATE, requestTemplate)"
  />
</template>
<script setup lang="ts">
import { ActionName } from '@enums';
import { useAppInsightsStore } from '@stores';
import { getEnvVariable } from '../utils/envVariablesManager';

// Composables
const { logEventAndClick } = useAppInsightsStore();

defineProps({
  icon: {
    type: String,
    default: 'mdi-plus-circle-outline',
  },
  iconSize: {
    type: [String, Number],
    default: undefined,
  },
  tooltip: {
    type: String,
    default: '',
  },
});

const requestTradingPartnerUrl: string = getEnvVariable('REQUEST_TRADING_PARTNER_TEMPLATE_URL');

function requestTemplate(): void {
  window.open(requestTradingPartnerUrl);
}
</script>
