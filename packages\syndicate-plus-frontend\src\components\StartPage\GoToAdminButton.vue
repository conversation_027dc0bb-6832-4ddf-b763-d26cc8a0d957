<template>
  <c-tile-btn
    v-if="hasSyndicatePlusAdminRights"
    icon="mdi-shield-crown-outline"
    :icon-size="20"
    tooltip-left="admin"
    @click="logEventAndClick(ActionName.REDIRECT_TO_SYNDICATE_PLUS, redirectToSyndicatePlus)"
  />
</template>

<script lang="ts" setup>
import { useSyndicatePlusAdminRights } from '@composables';
import navigationService from '@services/NavigationService';
import { useAppInsightsStore } from '@stores';
import { ActionName } from '@enums';
import { getCurrentOrganizationId } from '@services/DataFlow/CurrentOrganizationService';

const { logEventAndClick } = useAppInsightsStore();
const { hasSyndicatePlusAdminRights } = useSyndicatePlusAdminRights();

async function redirectToSyndicatePlus(): Promise<void> {
  const orgId = await getCurrentOrganizationId();
  if (orgId !== undefined) {
    navigationService.open(`/app/syndicateplus?org-id=${orgId}`);
  } else {
    navigationService.open('/app/syndicateplus');
  }
}
</script>
