<template>
  <div>
    <img v-if="url" :class="imageDataClass" class="mx-auto" :src="url" :alt="imageData.altText" />
    <div v-else class="gray-rectangle mx-auto" :class="imageDataClass"></div>
    <div v-if="imageData?.caption" class="font-bold" :class="captionClass">{{ imageData?.caption }}</div>
  </div>
</template>
<script lang="ts" setup>
import { PropType, computed } from 'vue';
import { ImageData } from '@customTypes/Aplus';

const props = defineProps({
  imageData: {
    type: Object as PropType<ImageData>,
    default: () => ({}),
  },
  imageDataClass: {
    type: String,
    default: '',
  },
  captionClass: {
    type: String,
    default: '',
  },
});

// Computed
const url = computed(() => {
  return props.imageData?.url || props.imageData?.angle;
});
</script>

<style scoped lang="scss">
img {
  display: block;
  object-fit: cover;
}
</style>
