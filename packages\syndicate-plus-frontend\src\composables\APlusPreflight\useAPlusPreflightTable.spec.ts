import { vi, it, describe, expect } from 'vitest';
import PreflightService from '@services/PreflightService';
import useAPlusPreflightTable from '@composables/APlusPreflight/useAPlusPreflightTable';
import { APlusTemplate } from '@customTypes/Aplus';
import { ProductGrouping } from '@enums';

vi.mock('@services/PreflightService');
vi.mock('@composables/APlusPreflight/useAPlusColumnsBuilder', () => ({
  default: vi.fn().mockReturnValue({
    buildColumns: vi.fn(),
    columns: [],
  }),
}));

const subCatalogId = 1;
const destinationId = 2;

describe('useAPlusPreflightTable', () => {
  describe('performPreflight', async () => {
    it('does not call server methods when the template is empty', async () => {
      // Arrange
      const { performPreflight } = useAPlusPreflightTable(null, subCatalogId, destinationId, ProductGrouping.SKU);
      const template = {} as APlusTemplate;

      // Act
      await performPreflight(template);

      // Assert
      expect(PreflightService.connectToEventStream).not.toHaveBeenCalled();
    });

    it('calls connectToEventStream methods when the template is not empty', async () => {
      // Arrange
      const { isLoading, performPreflight } = useAPlusPreflightTable(
        null,
        subCatalogId,
        destinationId,
        ProductGrouping.SKU
      );
      const template = {
        aplusTemplateId: 1,
        templateName: 'test',
      } as APlusTemplate;

      // Act
      await performPreflight(template);

      // Assert
      expect(PreflightService.connectToEventStream).toHaveBeenCalled();
      expect(isLoading.value).toBeTruthy();
    });
  });
});
