import { mount } from '@vue/test-utils';
import { vi, expect, it, describe, beforeEach } from 'vitest';
import CTileBtn from '@inriver/inri/src/components/CTileBtn.vue';
import { CEditGroupButton } from '@components/Shared';
import { setActivePinia, createPinia } from 'pinia';

describe('CEditGroupButton', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('displays button if isVisible is true', async () => {
    // Arrange
    vi.stubEnv('VITE_IS_DATA_EDITING_ENABLED', 'true');
    const props = {
      isVisible: true,
    };

    // Act
    const wrapper = mount(CEditGroupButton, {
      props,
      global: {
        mocks: {
          $t: () => 'tooltip text',
        },
        components: {
          'c-tile-btn': CTileBtn,
        },
      },
    });

    // Assert
    expect(wrapper.findComponent(CTileBtn).exists()).toBe(true);
  });

  it('does not display button if isVisible is false', async () => {
    // Arrange
    vi.stubEnv('VITE_IS_DATA_EDITING_ENABLED', 'true');
    const props = {
      isVisible: false,
    };

    // Act
    const wrapper = mount(CEditGroupButton, {
      props,
      global: {
        mocks: {
          $t: () => 'tooltip text',
        },
        components: {
          'c-tile-btn': CTileBtn,
        },
      },
    });

    // Assert
    expect(wrapper.findComponent(CTileBtn).exists()).toBe(false);
  });
});
