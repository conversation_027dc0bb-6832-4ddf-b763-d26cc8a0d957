param location string = resourceGroup().location

param costcenter string

param appName string

param customDomain string

var staticWebAppName = '${appName}-stapp'

resource staticWebApp 'Microsoft.Web/staticSites@2021-03-01' = {
  name: staticWebAppName
  location: location
  tags: {
    costcenter: costcenter
  }
  sku: {
    name: 'Standard'
    tier: 'Standard'
  }
  properties: {
    stagingEnvironmentPolicy: 'Enabled'
    allowConfigFileUpdates: true
  }

  resource cname 'customDomains@2022-03-01' = {
    name: customDomain
    properties: {}
  }
}
