import { mount } from '@vue/test-utils';
import { vi, beforeEach, expect, it, describe, Mock } from 'vitest';
import { useRoute } from 'vue-router';

import CMappingDialog from '@components/FieldMapping/CMappingDialog.vue';
import { InboundTemplateField, OutboundTemplateField } from '@customTypes';
import { createI18n } from 'vue-i18n';
import { useSubcatalogStore } from '@stores/SubcatalogStore';
import { createTestingPinia } from '@pinia/testing';
import { QTable } from 'quasar';

vi.mock('vue-router');

describe('CMappingDialog', () => {
  const subCatalogId = 1;

  beforeEach(() => {
    (useRoute as Mock).mockReturnValue({
      params: [{ subCatalogId }, { destinationId: '1' }],
    });
    const pinia = createTestingPinia({
      createSpy: vi.fn,
    });
    useSubcatalogStore(pinia);
  });

  it('displays the inbound and outbound tables with empty props', async () => {
    // Arrange
    const i18n = createI18n({
      allowComposition: true,
    });
    const wrapper = mount(CMappingDialog, {
      props: {
        title: 'test title',
        inbounds: [] as InboundTemplateField[],
        outbound: {} as OutboundTemplateField,
      },
      global: {
        plugins: [i18n],
      },
    });

    // Act
    const tables = wrapper.findAllComponents(QTable);

    // Assert
    expect(tables?.length).toBe(2);
  });
});
