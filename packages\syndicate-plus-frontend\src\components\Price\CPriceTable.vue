<template>
  <q-table
    ref="tableRef"
    flat
    dense
    hide-bottom
    separator="cell"
    class="price-table sticky-table-header"
    :pagination="{
      page: 1,
      rowsPerPage: 0,
    }"
    :rows="productPrices"
    :table-header-style="{ backgroundColor: 'var(--color-grey-lighter)' }"
    :columns="priceTableColumns"
    virtual-scroll
    :loading="isLoading"
    :virtual-scroll-item-size="31"
    :virtual-scroll-sticky-size-start="31"
    @virtual-scroll="onScroll"
  >
    <template #header="scope">
      <q-tr>
        <q-th v-for="col in scope.cols" :key="col.name" :class="getHeaderCellStyle(col)" :style="getCellOffset(col)">
          <div class="header-cell">
            <span>{{ col.label }}</span>
          </div>
        </q-th>
      </q-tr>
    </template>
    <template #body="scope">
      <q-tr>
        <q-td
          v-for="(col, index) in scope.cols"
          :id="`${index}-${getRowIndex(scope.row)}`"
          :key="col.name"
          :class="getCellClass(scope.row, col, index)"
          :style="getCellOffset(col)"
          @click="startEditing(scope.row, col, index)"
        >
          <div v-if="col.isExtraColumn">
            {{ renderValue(col, scope.row) }}
            <q-tooltip>{{ scope.row[col.field] }}</q-tooltip>
          </div>
          <div v-else class="editable-cell">
            <c-editable-cell
              :value="scope.row[col.field]?.toString()"
              :is-edit-mode="isSelectedCell(scope.row, index) && isEditingMode"
              :data-type="col.type"
              @stop-editing="stopEditing"
            />
          </div>
        </q-td>
      </q-tr>
    </template>
    <template #loading>
      <q-inner-loading showing color="primary" class="inner-loading">
        <c-spinner color="primary" size="40" />
      </q-inner-loading>
    </template>
  </q-table>
</template>
<script setup lang="ts">
import { PropType, ref, nextTick, toRefs, watchEffect } from 'vue';
import { priceTableColumns } from '@const';
import { CEditableCell } from '@components/Shared';
import { PriceDataToUpdateModel } from '@customTypes';
import { OverridedData } from '@customTypes/Products';
import { useStopCellEdit } from '@composables/Preflight';
import { useCellOffset } from '@composables';
import { StopCellEditType } from '@enums';

const props = defineProps({
  prices: {
    type: Array as PropType<any>,
    required: true,
  },
  overriddenValues: {
    type: Array as PropType<OverridedData[]>,
    default: [] as OverridedData[],
  },
  dataToUpdate: {
    type: Object as PropType<PriceDataToUpdateModel>,
    default: {} as PriceDataToUpdateModel,
  },
  isEditingMode: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  lastPage: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['fetch', 'stop-editing']);

// Refs
const { prices: productPrices } = toRefs(props);
const tableRef = ref(null);
const currentEdit = ref<any | null>(null);
const columnsToDisplay = ref<any[]>(priceTableColumns);

// Composables
const { nextRowId, nextColumnId, calculateNextRowAndColumn } = useStopCellEdit(
  tableRef,
  productPrices,
  columnsToDisplay
);
const { getCellOffset } = useCellOffset('.price-table', ref([]), ref([]));

// Functions
async function onScroll(details) {
  const { to, ref } = details;
  const isPageBottom = to === productPrices.value.length - 1;
  if (!props.isLoading && !props.lastPage && isPageBottom) {
    emits('fetch');
    nextTick(() => {
      ref.refresh();
    });
  }
}

const getCellClass = (row, col, columnIndex): string => {
  if (col.isExtraColumn) {
    return 'sticky extra-column-cell';
  }

  if (isUpdatedCell(row, col)) {
    return 'edited';
  }

  if (isSelectedCell(row, columnIndex)) {
    return 'editing';
  }

  if (!col.editable) {
    return 'not-editable';
  }

  return '';
};

const getHeaderCellStyle = (col): string => {
  return col.isExtraColumn ? 'sticky extra-column-header' : '';
};

const renderValue = (col, row): string => {
  return !row[col.field] || row[col.field] === '' ? '-' : row[col.field]?.toString();
};

const isSelectedCell = (row, columnIndex: number): boolean =>
  currentEdit.value !== null &&
  currentEdit.value.columnIndex === columnIndex &&
  currentEdit.value.rowId === getRowIndex(row);

const startEditing = (row, col, columnIndex: number) => {
  if (col.isCustom || !props.isEditingMode || !col.editable) {
    return;
  }

  currentEdit.value = {
    columnIndex,
    rowId: getRowIndex(row),
    productName: row.productName,
    upc: row.upc,
    fieldName: col.field,
  } as PriceDataToUpdateModel;
};

const stopEditing = (newValue: string, initialValue: string, type: any): void => {
  calculateNextRowAndColumn(currentEdit.value, type);
  const value = type === StopCellEditType.Esc ? initialValue : newValue;
  const data = {
    newValue: value,
    initialValue,
    ...currentEdit.value,
  } as PriceDataToUpdateModel;

  const key = `${data.columnIndex}-${data.rowId}`;
  emits('stop-editing', key, data);
  currentEdit.value = null;
};

const getRowIndex = (row) => productPrices.value.indexOf(row);

const isUpdatedCell = (row, col): boolean => {
  const values: PriceDataToUpdateModel[] = Object.values(props.dataToUpdate);

  return values.some((data: PriceDataToUpdateModel) => data.fieldName === col.field && data.rowId === getRowIndex(row));
};

// Lifecycle methods
watchEffect(() => {
  if (nextColumnId.value >= 0) {
    const column = priceTableColumns[nextColumnId.value];
    const row = productPrices.value[nextRowId.value];
    column && startEditing(row, column, nextColumnId.value);
  }
});
</script>
<style scoped lang="scss">
.price-table {
  max-height: 80vh;
  margin-bottom: 200px;
}

.no-data-title-slot {
  padding-top: 9px;
  display: inline;
}

th {
  background-color: var(--color-grey-lighter);
  padding-right: 0px;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;

  &.extra-column-header {
    background-color: var(--color-green-10) !important;
    z-index: 2;
  }

  &.extra-column-cell {
    background-color: #fafafa;
    color: var(--color-grey-dark);
    z-index: 1;
  }
}

.header-cell {
  display: flex;
  flex-flow: row;
  align-items: center;

  span {
    padding-right: 20px;
  }
}

.inner-loading {
  z-index: 3;
}
.editing {
  border: 1px solid rgba(173, 102, 242);
}

.edited {
  border: 1px solid rgba(173, 102, 242);
  background-color: rgba(173, 102, 242, 0.1);
}

.not-editable {
  background-color: var(--color-grey-10);
}

.editable-cell {
  height: 19.5px;
}
</style>
