<template>
  <q-menu v-model="show" anchor="top left" self="top right" @hide="hideMenu">
    <q-list class="min-w-100px">
      <q-item v-close-popup data-testid="menu-preflight" clickable>
        <q-item-section @click="preflight">{{ $t('syndicate_plus.syndication.preflight') }}</q-item-section>
      </q-item>
      <q-item v-if="isApiBtnVisibleRef" v-close-popup data-testid="menu-api" clickable>
        <q-item-section @click="api">
          {{
            isApiViaSftp
              ? $t('syndicate_plus.syndication.perform_api_syndication_via_sftp')
              : $t('syndicate_plus.syndication.perform_api_syndication')
          }}
        </q-item-section>
      </q-item>
      <q-item v-close-popup data-testid="menu-outbox" clickable>
        <q-item-section @click="exportOutbox">{{ $t('syndicate_plus.common.save_to_outbox') }}</q-item-section>
      </q-item>
      <q-item v-close-popup data-testid="menu-zip" clickable>
        <q-item-section @click="downloadZip"
          >{{ $t('syndicate_plus.media_context_menu.export_media_as_zip') }}
        </q-item-section>
      </q-item>
      <q-item
        v-if="isExportBelowTheFoldButtonVisibleRef"
        v-close-popup
        data-testid="menu-export-belowthefold"
        clickable
      >
        <q-item-section @click="exportBelowTheFold">
          {{ $t('syndicate_plus.below_the_fold.export_button.tooltip') }}
        </q-item-section>
      </q-item>
    </q-list>
  </q-menu>
</template>
<script setup lang="ts">
import { toRef, ref, watch } from 'vue';

// Props
const props = defineProps({
  showContextMenu: {
    type: Boolean,
    default: false,
  },
  isApiBtnVisible: {
    type: Boolean,
    required: true,
  },
  isApiViaSftp: {
    type: Boolean,
    required: false,
  },
  isExportBelowTheFoldButtonVisible: {
    type: Boolean,
    required: true,
  },
});

// Emits
const emit = defineEmits([
  'preflight',
  'api-syndication',
  'export',
  'download-media-zip',
  'export-below-the-fold',
  'hide',
]);

// Refs
const isApiBtnVisibleRef = toRef(props, 'isApiBtnVisible');
const isExportBelowTheFoldButtonVisibleRef = toRef(props, 'isExportBelowTheFoldButtonVisible');
const show = ref(false);

// Functions
const preflight = () => {
  emit('preflight');
};

const api = () => {
  emit('api-syndication');
};

const exportOutbox = () => {
  emit('export');
};

const downloadZip = () => {
  emit('download-media-zip');
};

const exportBelowTheFold = () => {
  emit('export-below-the-fold');
};

const hideMenu = () => {
  emit('hide');
};

watch(
  () => props.showContextMenu,
  () => {
    show.value = props.showContextMenu;
  }
);
</script>
