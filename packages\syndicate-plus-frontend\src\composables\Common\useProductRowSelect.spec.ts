import { describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useProductRowSelect } from '@composables/Common';

describe('useProductRowSelect', () => {
  describe('onRowClick', () => {
    it('sets selectedRow correctly', async () => {
      // Arrange
      const isEditingMode = ref(false);
      const row = { id: 1 };
      const { selectedRow, isProductDetailsButtonDisabled, onRowClick } = useProductRowSelect(isEditingMode);
      isProductDetailsButtonDisabled.value = true;

      // Act
      onRowClick(row);

      // Assert
      expect(selectedRow.value).to.deep.equal([row]);
      expect(isProductDetailsButtonDisabled.value).toBeFalsy();
    });

    it('does not set selectedRow if isEditingMode is true', async () => {
      // Arrange
      const isEditingMode = ref(true);
      const row = { id: 1 };
      const { selectedRow, isProductDetailsButtonDisabled, onRowClick } = useProductRowSelect(isEditingMode);
      isProductDetailsButtonDisabled.value = true;

      // Act
      onRowClick(row);

      // Assert
      expect(selectedRow.value).toBe(undefined);
      expect(isProductDetailsButtonDisabled.value).toBeTruthy();
    });
  });

  describe('isProductDetailsButtonVisible', () => {
    it('returns true if row is selected and isEditingMode is false', async () => {
      // Arrange
      const isEditingMode = ref(false);
      const row = { id: 1 };
      const { onRowClick, isProductDetailsButtonVisible } = useProductRowSelect(isEditingMode);

      // Act
      onRowClick(row);

      // Assert
      expect(isProductDetailsButtonVisible.value).toBeTruthy();
    });

    it('returns false if row is not selected and isEditingMode is false', async () => {
      // Arrange
      const isEditingMode = ref(false);
      const { isProductDetailsButtonVisible } = useProductRowSelect(isEditingMode);

      // Act + Assert
      expect(isProductDetailsButtonVisible.value).toBeFalsy();
    });

    it('returns false if row is selected and isEditingMode is true', async () => {
      // Arrange
      const isEditingMode = ref(true);
      const row = { id: 1 };
      const { onRowClick, isProductDetailsButtonVisible } = useProductRowSelect(isEditingMode);

      // Act
      onRowClick(row);

      // Assert
      expect(isProductDetailsButtonVisible.value).toBeFalsy();
    });
  });
});
