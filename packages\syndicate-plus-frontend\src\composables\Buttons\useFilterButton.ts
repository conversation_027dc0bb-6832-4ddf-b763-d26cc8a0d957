import { Ref, computed } from 'vue';
import { SyndicationPageTabNames } from '@enums';

export default function useFilterButton(
  useI18n: Function,
  tab: Ref<SyndicationPageTabNames>,
  isFilterPanelOpen: Ref<boolean>
) {
  // Composables
  const { t } = useI18n && useI18n();

  // Computed
  const isFilterButtonVisible = computed(() => tab.value === SyndicationPageTabNames.Products);
  const filterButtonIcon = computed(() => (isFilterPanelOpen.value ? 'mdi-close' : 'mdi-filter-outline'));
  const filterButtonTooltip = computed(() =>
    isFilterPanelOpen.value ? t('syndicate_plus.common.close') : t('syndicate_plus.common.filter.filter')
  );

  return { isFilterButtonVisible, filterButtonIcon, filterButtonTooltip };
}
