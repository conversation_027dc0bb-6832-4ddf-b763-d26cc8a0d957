<template>
  <div class="c-media-naming-override">
    <q-table
      class="media-naming-override-table"
      row-key="name"
      separator="none"
      dense
      flat
      :columns="columns"
      :rows="rows"
      hide-bottom
    >
      <template #body-cell-namingOverride="props">
        <q-td :props="props" class="flex">
          <q-card-section
            v-for="(item, index) in props.row.namingOverride"
            :key="index"
            :horizontal="true"
            class="name-override-function"
          >
            <q-card dense class="color-card">
              {{ item }}
            </q-card>
            <span v-if="index < props.row.namingOverride?.length - 1" class="m-1"> + </span>
          </q-card-section>
        </q-td>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NameOverrideSplittingService } from './NameOverrideSplittingService';

const componentProps = defineProps({
  mediaNamingOverride: {
    type: String,
    default: '',
  },
});

const rows = computed(() => {
  const splitted = NameOverrideSplittingService.Split(componentProps.mediaNamingOverride);
  const namingOverride = NameOverrideSplittingService.RemoveMarkings(splitted);
  return [
    {
      namingOverride,
    },
  ];
});

const columns = [
  {
    name: 'namingOverride',
    field: 'namingOverride',
    label: 'naming override',
    align: 'left' as const,
  },
];
</script>
<style lang="scss" scoped>
.c-media-naming-override {
  .media-naming-override-table {
    min-height: 100px;

    .name-override-function {
      font-size: 10px;
      font-weight: 400;
      display: flex;
      align-items: center;

      .q-card {
        border-radius: 30px;
        border: 1.5px solid var(--color-grey-dark);
        padding: 4px;
        box-shadow: none;

        &.color-card {
          background-color: var(--color-primary);
          height: 20px;
          line-height: 11px;
        }
      }
    }
  }
}
</style>
