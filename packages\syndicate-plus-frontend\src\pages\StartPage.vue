<template>
  <c-layout-with-sidebars>
    <template #right-sidebar>
      <c-tile-btn
        v-if="isManageTradingPartnerButtonVisible"
        icon="mdi-cog-outline"
        class="pendo__settings"
        :icon-size="20"
        :tooltip-left="$t('core.manage_trading_partners.title')"
        @click="logEventAndClick(ActionName.GO_TO_MANAGE_TRADING_PARTNERS_PAGE, goToManageTradingPartnersPage)"
      />
      <c-request-template-btn
        v-if="isRequestSyndic8TradingPartnerVisible"
        class="pendo__request_trading_partner"
        icon="mdi-plus-circle-outline"
        :icon-size="20"
        :tooltip-left="$t('syndicate_plus.request_trading_partner')"
      />
      <c-tile-btn
        class="pendo__toggle_view_mode"
        :icon="isShowingTradingPartnerGridView ? 'mdi-view-sequential-outline' : 'mdi-grid-large'"
        :icon-size="20"
        :tooltip-left="
          isShowingTradingPartnerGridView ? $t('syndicate_plus.show_table_view') : $t('syndicate_plus.show_grid_view')
        "
        @click="
          logEventAndClick(
            isShowingTradingPartnerGridView ? ActionName.VIEW_AS_TABLE : ActionName.VIEW_AS_GRID,
            toggleViewMode
          )
        "
      />
      <c-tile-btn
        v-if="isShowConnectedTradingPartnersToggleVisible"
        icon="mdi-storefront-outline"
        class="pendo__toggle_show_all"
        :icon-size="20"
        :icon-class="showOnlyConnectedTradingPartners ? '' : 'strike-through'"
        :tooltip-left="
          showOnlyConnectedTradingPartners ? $t('syndicate_plus.show_all') : $t('syndicate_plus.only_show_enabled')
        "
        @click="
          logEventAndClick(
            showOnlyConnectedTradingPartners
              ? ActionName.SHOW_ALL_TRADING_PARTNERS
              : ActionName.SHOW_CONNECTED_TRADING_PARTNERS,
            () => (showOnlyConnectedTradingPartners = !showOnlyConnectedTradingPartners)
          )
        "
      />
      <c-tile-btn
        v-if="isGoToSyndic8ImportsVisible"
        icon="mdi-file-import-outline"
        class="pendo__go_to_show_inbounds"
        :icon-size="20"
        :tooltip-left="$t('syndicate_plus.inbound_data')"
        @click="logEventAndClick(ActionName.GO_TO_IMPORTS_PAGE, goToImportsPage)"
      />
      <c-tile-btn
        v-if="isImportSyndic8DataButtonVisible"
        class="pendo__go_to_import_data"
        icon="mdi-import"
        :icon-size="20"
        :tooltip-left="$t('syndicate_plus.syndication.price_data')"
        @click="logEventAndClick(ActionName.IMPORT_DATA, importData)"
      />
      <c-tile-btn
        v-if="isThirdPartyMessagesButtonVisible"
        class="pendo__to_go_api_calls"
        icon="mdi-swap-horizontal"
        :icon-size="20"
        :tooltip-left="$t('syndicate_plus.third_party_messages.api_calls')"
        @click="logEventAndClick(ActionName.GO_TO_THIRD_PARTY_MESSAGES_PAGE, goToThirdPartyMessagesPage)"
      />
      <GoToReferenceTablesButton />
      <GoToAdminButton />
    </template>
    <div v-if="loading && !displayedTradingPartners?.length" class="spinner">
      <c-spinner size="56" />
    </div>
    <div v-else-if="tradingPartnersExist" class="start-page">
      <c-section v-if="showWarning" class="pb-0">
        <c-info-block
          :bg-color="InfoColor.LIGHTORANGE"
          :border-color="InfoColor.ORANGE"
          :title="$t('syndicate_plus.warning.title')"
          :description="$t('syndicate_plus.warning.failed_import_description', { fileName })"
          @close-info-block="closeWarning"
        />
      </c-section>
      <div v-if="isShowingTradingPartnerGridView">
        <c-section
          v-for="tradingPartnerType in visibleTradingPartnerTypes"
          :id="translateTradingPartnerType(tradingPartnerType as string)"
          :key="tradingPartnerType"
        >
          <div v-if="displayedTradingPartners.filter((_) => _.type === tradingPartnerType).length > 0">
            <c-section-title>{{ translateTradingPartnerType(tradingPartnerType as string) }}</c-section-title>
            <c-grid cols="repeat(auto-fill, minmax(190px, 190px)">
              <template
                v-for="card in displayedTradingPartners.filter((_) => _.type === tradingPartnerType)"
                :key="card.id"
              >
                <c-trading-partner-card
                  :id="card.id"
                  :is-connected="card.isConnected"
                  :name="card.name"
                  :type="card.type"
                  :skus="card['skus']"
                  :logo="card.logo"
                  :destination-id="card['destinationId']"
                  :sub-catalog-id="card['subCatalogId']"
                  :is-core-card="card.type === SyndicationType.Core"
                />
              </template>
            </c-grid>
          </div>
        </c-section>
      </div>
      <div v-else>
        <c-trading-partner-table-view :trading-partner-list="displayedTradingPartners" />
      </div>
    </div>
    <div v-else-if="!tradingPartnersExist">
      <c-section>
        <c-no-data
          src="open-soon"
          :title="$t('syndicate_plus.no_data.opening_soon')"
          :text="$t('syndicate_plus.no_data.trading_partner_message')"
        />
      </c-section>
    </div>
  </c-layout-with-sidebars>
</template>

<script lang="ts" setup>
import { onBeforeMount, ref, computed, onMounted, watch } from 'vue';
import { CTradingPartnerCard, CTradingPartnerTableView, CNoData, CRequestTemplateBtn } from '@components';
import { CInfoBlock } from '@components/Shared';
import { storeToRefs } from 'pinia';
import { useProductsStore, useTradingPartnersStore, useSuggestionStore, useProductOutboxStore } from '@stores';
import filterTradingPartners from '@pages/helpers/filterTradingPartners';
import { GoToAdminButton, GoToReferenceTablesButton } from '@components/StartPage';
import localStorageService from '@services/LocalStorageService';
import importService from '@services/ImportService';
import { InfoColor, PageName, ActionName } from '@enums';
import { SettingsTabNames } from '@core/enums';
import { useAppInsightsStore } from '@stores';
import { useRouter } from '@composables/useRouter';
import { SyndicationType } from '@core/enums/SyndicationType';
import { useCheckPriceImportAvailable } from '@composables';
import isFeatureEnabled from '@utils/isFeatureEnabled';
import {
  isSyndicateAdvanceConfigEnabled,
  isSyndicatePlusPartnersEnabled,
  isSyndicatePlusWithCoreEnabled,
} from '@utils/moduleEnabledService';

// Variables
const magento2 = 'magento 2';

// Composables
const { goToPage } = useRouter();
const { resetSuggestionStoreState } = useSuggestionStore();
const { resetState } = useProductsStore();
const { resetProductOutboxState } = useProductOutboxStore();
const { setScreenName, logEventAndClick, startPageLoadAndRender, endPageLoadAndRender } = useAppInsightsStore();
const { isPriceImportAvailable } = useCheckPriceImportAvailable();
const tradingPartnerStore = useTradingPartnersStore();

// Refs
const isShowingTradingPartnerGridView = ref(true);
const { loading, tradingPartners, showOnlyConnectedTradingPartners } = storeToRefs(tradingPartnerStore);
const showWarning = ref<Boolean>(false);
const fileName = ref<String>('');

// Computed
const isManageTradingPartnerButtonVisible = computed(
  () => isSyndicatePlusWithCoreEnabled() || isSyndicateAdvanceConfigEnabled()
);
const isRequestSyndic8TradingPartnerVisible = computed(() => isSyndicatePlusPartnersEnabled());
const displayedTradingPartners = computed(() =>
  filterTradingPartners(tradingPartners.value || [], showOnlyConnectedTradingPartners.value)
);
const visibleTradingPartnerTypes = computed(() =>
  Array.from(new Set(displayedTradingPartners.value.map((tradingPartner) => tradingPartner.type)))
);

const tradingPartnersExist = computed(() => {
  return !!visibleTradingPartnerTypes.value?.length;
});
const isShowConnectedTradingPartnersToggleVisible = computed(() => isSyndicatePlusPartnersEnabled());
const isGoToSyndic8ImportsVisible = computed(() => isSyndicatePlusPartnersEnabled());
const isImportSyndic8DataButtonVisible = computed(
  () => isSyndicatePlusPartnersEnabled() && isPriceImportAvailable.value
);
const isThirdPartyMessagesButtonVisible = computed(
  () => isFeatureEnabled('3rd-party-messaging') || isMagento2TradingPartnerConnected()
);

// Functions
function trySetShowOnlyConnectedTradingPartnersFromCache() {
  const cachedViewSetting = localStorageService.getShowConnectedTradingPartner();
  if (cachedViewSetting != null) {
    showOnlyConnectedTradingPartners.value = cachedViewSetting;
  }
}

async function displayImportErrorNotification() {
  const notificationState = await importService.getNotificationState();
  if (notificationState.displayNotification) {
    showWarning.value = true;
    fileName.value = notificationState.filename;
  }
}

const isMagento2TradingPartnerConnected = (): boolean =>
  tradingPartners.value.some(
    (tradingPartner) => tradingPartner.name?.toLowerCase().includes(magento2) && tradingPartner.isConnected
  );

const closeWarning = () => {
  showWarning.value = false;
};

function goToImportsPage() {
  goToPage('imports-page');
}

function goToThirdPartyMessagesPage() {
  goToPage('third-party-messages-page');
}

function importData() {
  goToPage('import-general-price-page');
}

function resetStores() {
  resetSuggestionStoreState();
  resetState();
  resetProductOutboxState();
}

/**
 * Later this can use the translate service, currently it just makes the type plural.
 * @param type
 */
function translateTradingPartnerType(type: string): string {
  if (type.endsWith('s') || !isLetter(type.slice(-1)) || type === 'Core') {
    return type.toLocaleLowerCase();
  }
  return type.toLocaleLowerCase() + 's';
}

function isLetter(str) {
  return str.length === 1 && str.match(/[a-z]/i);
}

function toggleViewMode() {
  isShowingTradingPartnerGridView.value = !isShowingTradingPartnerGridView.value;
  localStorageService.setShowGridTradingPartnerView(isShowingTradingPartnerGridView.value);
}

const setViewMode = (): void => {
  const cachedViewSetting = localStorageService.getShowGridTradingPartnerView();
  if (cachedViewSetting != null) {
    isShowingTradingPartnerGridView.value = cachedViewSetting;
  }
};

const goToManageTradingPartnersPage = () => {
  goToPage('manage-trading-partners-page', {
    tabName: SettingsTabNames.FORMATS,
  });
};

// Lifecycle methods
onBeforeMount(async () => {
  startPageLoadAndRender();

  setScreenName(PageName.TRADING_PARTNERS);
  resetStores();
  setViewMode();
  trySetShowOnlyConnectedTradingPartnersFromCache();
  if (tradingPartners.value?.length) {
    return;
  }

  await tradingPartnerStore.fetchTradingPartners();
  endPageLoadAndRender('StartPageLoadAndRender');
});

onMounted(async () => {
  if (isSyndicatePlusPartnersEnabled()) {
    await displayImportErrorNotification();
  }
});

watch(
  () => showOnlyConnectedTradingPartners.value,
  () => localStorageService.setShowConnectedTradingPartners(showOnlyConnectedTradingPartners.value)
);
</script>

<style scoped lang="scss">
.start-page {
  overflow-y: auto;
}

.cursor-pointer .q-table tbody tr {
  cursor: pointer;
}

.spinner {
  margin: auto;
}

:deep(.strike-through) {
  background-image: linear-gradient(to bottom right, transparent 45%, black, transparent 54%);
  background-repeat: no-repeat;
  font-size: 50px;
  text-align: center;
}
</style>
