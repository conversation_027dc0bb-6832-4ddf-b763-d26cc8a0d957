import { IStopCellEditStrategy, BaseStrategy } from '@composables/Preflight/StopCellEditStrategies';
import { TableCellCoordinates } from '@customTypes';
import { PreflightColumnDto } from '@dtos';

export class EnterStrategy extends BaseStrategy implements IStopCellEditStrategy {
  constructor(rows: any[], columns: PreflightColumnDto[]) {
    super(rows, columns);
  }

  getNextRowAndColumnId(currentRowId: number, currentColumnId: number): TableCellCoordinates {
    return {
      columnId: currentColumnId,
      rowId: this.getNextAvailableRowId(currentRowId),
    } as TableCellCoordinates;
  }
}
