<template>
  <div class="mx-auto min-w-1000px w-1000px images-and-text-preview">
    <div class="main-headline ml-20px">{{ module.data.headline }}</div>
    <div class="flex flex-row flex-nowrap justify-center">
      <div
        v-for="orderIndex in numberOfBlocks"
        :key="orderIndex"
        class="module-block text-wrap-word-break m-10px"
        :class="numberOfBlocks === 3 ? 'three-image' : 'four-image'"
        :data-testid="`${orderIndex}-images-and-text-block`"
      >
        <c-caption-angle-image-block-preview
          :image-data="module?.data[`image${orderIndex}`]"
          image-data-class="image mb-10px ml-5px"
        />
        <div class="headline p-10px">{{ module?.data[`headline${orderIndex}`] }}</div>
        <c-html-preview :html="module?.data[`body${orderIndex}`]" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';
import { ContentModule, FourImageTextData, ThreeImageTextData } from '@customTypes/Aplus';

const props = defineProps({
  numberOfBlocks: {
    type: Number,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Computed
const module = computed<ContentModule<FourImageTextData | ThreeImageTextData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.images-and-text-preview {
  .headline {
    font-size: 18px;
    font-weight: bold;
  }

  .three-image .headline {
    width: 335px;
  }

  .module-block {
    &.three-image {
      width: 310px;
      min-width: 310px;
    }

    &.four-image {
      width: 230px;
      min-width: 230px;
    }
  }

  .image {
    object-fit: cover;

    &img {
      vertical-align: middle;
    }
  }

  .three-image {
    :deep() {
      .image {
        width: 300px;
        height: 300px;
      }
    }
  }

  .four-image {
    :deep() {
      .image {
        width: 220px;
        height: 220px;
      }
    }
  }
}

.main-headline {
  font-size: 18px;
  font-weight: bold;
}
</style>
