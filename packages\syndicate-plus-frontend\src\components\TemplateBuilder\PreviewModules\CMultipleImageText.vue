<template>
  <div class="mx-auto min-w-1000px w-1000px multiple-image-text-preview" data-testid="multiple-images-and-text">
    <div class="flex justify-center h-360px m-10px">
      <div class="module-block text-wrap-word-break flex flex-row flex-nowrap m-10px">
        <div>
          <c-caption-angle-image-block-preview
            :image-data="module?.data.image1"
            image-data-class="image mb-10px ml-5px mr-20px"
            caption-class="font-bold pl-10px"
          />
        </div>
        <div>
          <div class="h-190px overflow-auto mb-10px">
            <div class="headline">{{ module?.data.headline1 }}</div>
            <c-html-preview :html="module?.data.body1" />
          </div>
          <div class="flex flex-row flex-nowrap m-10px">
            <div v-for="image in numberOfSmallImages" :key="image" class="mr-30px">
              <c-caption-angle-image-block-preview
                :image-data="module.data[`image${image}`]"
                image-data-class="h-100px w-100px min-w-[100px]"
                caption-class="sidebar-body pl-10px pt-6px"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useTemplateBuilderStore } from '@stores/TemplateBuilder';
import { CHtmlPreview, CCaptionAngleImageBlockPreview } from '@components/TemplateBuilder/Blocks';
import { ContentModule, MultipleImageTextData } from '@customTypes/Aplus';

const props = defineProps({
  index: {
    type: Number,
    required: true,
  },
});

const store = useTemplateBuilderStore();

// Variables
const numberOfSmallImages = 4;

// Computed
const module = computed<ContentModule<MultipleImageTextData>>(() => {
  return store.getModuleByIndex(props.index);
});
</script>

<style lang="scss" scoped>
.multiple-image-text-preview {
  :deep() {
    .image {
      object-fit: cover;
      width: 300px;
      height: 300px;

      &img {
        vertical-align: middle;
      }
    }
  }

  .headline {
    font-size: 18px;
    padding: 10px;
    padding-left: 30px;
    font-weight: bold;
  }
}
</style>
