import { ref } from 'vue';
import { ContentModule, MultipleImageTextData } from '@customTypes/Aplus';

export default function useMultipleImageText() {
  // Refs
  const module = ref<ContentModule<MultipleImageTextData>>({} as ContentModule<MultipleImageTextData>);

  // Functions
  const initData = () => {
    if (!Object.keys(module.value.data)?.length) {
      module.value.data = {
        image1: {
          altText: '',
          angle: '',
          caption: '',
        },
        headline1: '',
        body1: '',
        image2: {
          altText: '',
          angle: '',
          caption: '',
        },
        image3: {
          altText: '',
          angle: '',
          caption: '',
        },
        image4: {
          altText: '',
          angle: '',
          caption: '',
        },
      } as MultipleImageTextData;
    }
  };

  return { module, initData };
}
