import { describe, it, expect } from 'vitest';
import { ref } from 'vue';
import { useAddToCollection } from '@composables/Collection';
import { CollectionDetails } from '@customTypes';

describe('useAddToCollection', () => {
  it('should calculate correct addToCollectionIsDisabled value for different input', async () => {
    const testData = [
      { collection: {} as CollectionDetails, isLoadingState: true, expectedValue: true },
      { collection: undefined, isLoadingState: true, expectedValue: true },
      { collection: { id: 1 } as CollectionDetails, isLoadingState: true, expectedValue: true },
      { collection: { id: 1 } as CollectionDetails, isLoadingState: false, expectedValue: false },
    ];

    testData.forEach(({ collection, isLoadingState, expectedValue }) => {
      // Arrange + Act
      const isLoading = ref(isLoadingState);
      const { selectedCollection, addToCollectionIsDisabled } = useAddToCollection(isLoading);
      selectedCollection.value = collection;

      // Assert
      expect(addToCollectionIsDisabled.value).toBe(expectedValue);
    });
  });
});
