import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';
import { QExpansionItem, QSeparator } from 'quasar';
import { CSeparatedExpansionItem } from '@components/Shared';

describe('CSeparatedExpansionItem', () => {
  it('displays QSeparator', async () => {
    // Arrange
    const label = 'test label';
    const wrapper = mount(CSeparatedExpansionItem, {
      props: {
        isExpanded: true,
        label,
      },
      global: {
        components: {
          'q-expansion-item': QExpansionItem,
          'q-separator': QSeparator,
        },
      },
    });

    // Act
    const separatorComponent = wrapper.findComponent(QSeparator);

    // Assert
    expect(separatorComponent.exists()).toBeTruthy();
  });

  it('displays title', async () => {
    // Arrange
    const label = 'test label';
    const wrapper = mount(CSeparatedExpansionItem, {
      props: {
        isExpanded: true,
        label,
      },
      global: {
        components: {
          'q-expansion-item': QExpansionItem,
          'q-separator': QSeparator,
        },
      },
    });

    // Act
    const componentText = wrapper.html();

    // Assert
    expect(componentText).contains(label);
  });

  it('expands correctly', async () => {
    // Arrange
    const label = 'test label';
    const wrapper = mount(CSeparatedExpansionItem, {
      props: {
        isExpanded: true,
        label,
      },
      global: {
        components: {
          'q-expansion-item': QExpansionItem,
          'q-separator': QSeparator,
        },
      },
    });

    // Act
    const componentHtml = wrapper.html();

    // Assert
    expect(componentHtml).contains('q-expansion-item--expanded');
  });
});
