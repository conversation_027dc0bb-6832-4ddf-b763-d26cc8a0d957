<template>
  <q-field
    ref="fieldRef"
    v-model="value"
    class="text-editor text-wrap-word-break full-width"
    label-slot
    borderless
    :maxlength="maxLength"
    counter
  >
    <template #control>
      <q-editor
        v-model="value"
        class="full-width"
        :toolbar="[
          ['bold', 'italic', 'underline'],
          ['unordered', 'ordered'],
        ]"
        :style="fieldRef && fieldRef.hasError ? 'border-color: var(--color-red)' : ''"
        @keyup.enter="updateModel"
        @blur="updateModel"
        @drop="(e) => onDrop(e)"
      />
    </template>
  </q-field>
</template>
<script lang="ts" setup>
import { ref, watchEffect } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  maxLength: {
    type: Number,
    default: 1000,
  },
});
const emit = defineEmits(['update:modelValue', 'on-field-drop']);

// Refs
const value = ref(props.modelValue);
const fieldRef = ref();

// Methods
const updateModel = () => {
  emit('update:modelValue', value.value);
};

const onDrop = (e) => {
  e.stopPropagation();
  emit('on-field-drop');
};

// Lifecycle methods
watchEffect(() => {
  value.value = props.modelValue;
});
</script>

<style lang="scss" scoped>
:deep(ol) {
  list-style: decimal;
  margin: 0;
  padding-left: 40px;
}

:deep(ul) {
  list-style: disc;
  margin: 0;
  padding-left: 40px;
}

.text-editor {
  :deep(.q-field__control-container) {
    padding-top: 1px;
  }

  :deep(.q-field__native.row) {
    padding-bottom: 2px;
  }
}
</style>
