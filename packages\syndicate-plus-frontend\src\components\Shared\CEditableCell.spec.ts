import { mount } from '@vue/test-utils';
import { expect, it, describe } from 'vitest';
import { QInput } from 'quasar';
import { CEditableCell } from '@components/Shared';

describe('CEditableCell', () => {
  it('displays data if isEditMode is false', async () => {
    // Arrange
    const value = 'My test text';
    const props = {
      isEditMode: false,
      value,
    };

    // Act
    const wrapper = mount(CEditableCell, {
      props,
      global: {
        components: {
          'q-input': QInput,
        },
      },
    });

    // Assert
    expect(wrapper.text()).contains(value);
    expect(wrapper.findComponent(QInput).exists()).toBe(false);
  });

  it('displays a dash if incoming value is missing', async () => {
    // Arrange
    const props = {
      isEditMode: false,
      value: '',
    };

    // Act
    const wrapper = mount(CEditableCell, {
      props,
      global: {
        components: {
          'q-input': QInput,
        },
      },
    });

    // Assert
    expect(wrapper.text()).contains('-');
  });

  it('displays input with data if isEditMode is true', async () => {
    // Arrange
    const value = 'My test text';
    const props = {
      isEditMode: true,
      value,
    };

    // Act
    const wrapper = mount(CEditableCell, {
      props,
      global: {
        components: {
          'q-input': QInput,
        },
      },
    });

    // Assert
    expect(wrapper.findComponent(QInput).exists()).toBe(true);
  });
});
