import { computed, ref } from 'vue';
import { vi, expect, it, describe } from 'vitest';
import { useFilterFieldsActionButtons } from '@composables/ProductDetails';
import { FilterField } from '@customTypes/Products';

describe('useFilterFieldsActionButtons', () => {
  describe('isSelectedAll', () => {
    it('sets isSelectedAll to false if no items are selected', async () => {
      // Arrange
      const allColumns = computed(() => [{ name: 'a' }, { name: 'b' }, { name: 'c' }] as FilterField[]);
      const selectedColumnNames = ref([]);
      const applyFilter = vi.fn();
      const { isSelectedAll } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      selectedColumnNames.value = [];

      // Assert
      expect(isSelectedAll.value).toBeFalsy();
    });

    it('sets isSelectedAll to true if all items are selected', async () => {
      // Arrange
      const allColumns = computed(() => [{ name: 'a' }, { name: 'b' }, { name: 'c' }] as FilterField[]);
      const selectedColumnNames = ref([] as string[]);
      const applyFilter = vi.fn();
      const { isSelectedAll } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      selectedColumnNames.value = ['a', 'b', 'c'];

      // Assert
      expect(isSelectedAll.value).toBeTruthy();
    });

    it('sets isSelectedAll to null if elements are partially selected', async () => {
      // Arrange
      const allColumns = computed(() => [{ name: 'a' }, { name: 'b' }, { name: 'c' }] as FilterField[]);
      const selectedColumnNames = ref([] as string[]);
      const applyFilter = vi.fn();
      const { isSelectedAll } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      selectedColumnNames.value = ['a', 'c'];

      // Assert
      expect(isSelectedAll.value).toBe(null);
    });
  });

  describe('selectAll', () => {
    it('deselects all elements if the elements are partially selected', async () => {
      // Arrange
      const allColumns = computed(() => [{ name: 'a' }, { name: 'b' }, { name: 'c' }] as FilterField[]);
      const selectedColumnNames = ref(['a']);
      const applyFilter = vi.fn();
      const { selectAll } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      selectAll();

      // Assert
      expect(selectedColumnNames.value).toStrictEqual([]);
      expect(applyFilter).toHaveBeenCalledOnce();
    });

    it('selects all elements if no items are selected', async () => {
      // Arrange
      const allColumns = computed(() => [{ name: 'a' }, { name: 'b' }, { name: 'c' }] as FilterField[]);
      const selectedColumnNames = ref([]);
      const applyFilter = vi.fn();
      const { selectAll } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      selectAll();

      // Assert
      expect(selectedColumnNames.value).toStrictEqual(['a', 'b', 'c']);
      expect(applyFilter).toHaveBeenCalledOnce();
    });
  });

  describe('getDefaultColumnNames', () => {
    it('returns list of required columns', async () => {
      // Arrange
      const allColumns = computed(
        () =>
          [
            { name: 'a', isRequired: true },
            { name: 'b', isRequired: false },
            { name: 'c', isRequired: true },
          ] as FilterField[]
      );
      const selectedColumnNames = ref([]);
      const applyFilter = vi.fn();
      const { getDefaultColumnNames } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      const result = getDefaultColumnNames();

      // Assert
      expect(result).toStrictEqual(['a', 'c']);
    });
  });

  describe('clearFilter', () => {
    it('sets list of required columns', async () => {
      // Arrange
      const allColumns = computed(
        () =>
          [
            { name: 'a', isRequired: true },
            { name: 'b', isRequired: false },
            { name: 'c', isRequired: true },
          ] as FilterField[]
      );
      const selectedColumnNames = ref(['a', 'b', 'c']);
      const applyFilter = vi.fn();
      const { clearFilter } = useFilterFieldsActionButtons(allColumns, selectedColumnNames, applyFilter);

      // Act
      clearFilter();

      // Assert
      expect(selectedColumnNames.value).toStrictEqual(['a', 'c']);
      expect(applyFilter).toHaveBeenCalledOnce();
    });
  });
});
